<template>
  <div class="main">
    <jt-header></jt-header>
    <div class="content">
      <div class="inner-content">
        <img src="@/assets/images/forbidden-v1.1.png" alt="" />
        <div v-if="isForbiddenResourcePool && !isApplication" class="text-wrapper">
          <div class="text">您暂无访问资源池权限，请联系平台管理员申请新建项目权限，或联系项目负责人加入项目</div>
          <div v-if="allowApplication" class="help">您可以去<a-button type="link" class="btn" @click="goApplication">申请新建项目空间权限</a-button></div>
        </div>
        <div v-else-if="isForbiddenResourcePool && isApplication" class="text-wrapper">
          <div class="text">您已申请新建项目空间</div>
          <div class="text">请等待平台管理员审批</div>
          <div class="help">您可以去<a-button type="link" class="btn" @click="checkApplication">查看申请详情</a-button></div>
        </div>
        <div v-else class="text-other">
          <div class="text">暂无访问权限，请联系管理员申请</div>
        </div>
      </div>
    </div>
    <create-modal v-model:open="createModalDisplay" @ok="createModalHandleOk" />
    <cancel-modal v-model:open="cancelModalDisplay" :application-detail="applicationDetail" @ok="cancelModalHandleOk" />
  </div>
</template>

<script setup>
// import simpleHeader from '@/components/simpleHeader.vue';
import JtHeader from '@/components/header.vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { getEnvConfig } from '@/config';
import { projectSpaceApi } from '@/apis';

import createModal from '@/components/applicationModal/createModal.vue';
import cancelModal from '@/components/applicationModal/cancelModal.vue';

const router = useRouter();
const route = useRoute();
const isForbiddenResourcePool = ref(route.query.type === '1');
const showRequirment = ref(getEnvConfig('SHOW_REQUIRMENT_ICON') === '1');

const store = useStore();
const allowApplication = computed(() => {
  const poolInfo = store.state.poolInfo;
  return store.state.poolList.length === 0 || (store.state.poolList.length > 0 && poolInfo.allowApplication);
});

//申请新建
const createModalDisplay = ref(false);

const goApplication = () => {
  createModalDisplay.value = true;
};

//查看-取消申请
const applicationDetail = ref({});
const cancelModalDisplay = ref(false);

const checkApplication = () => {
  cancelModalDisplay.value = true;
};

//在途申请查询
const isApplication = ref(false);

const getUserQuery = async () => {
  const res = await projectSpaceApi.getProcessing({ type: 'CreateProject', poolId: store.state.poolInfo?.id });
  if (res.code === 0) {
    // Object.keys(res.data).length !== 0
    if (res.data) {
      isApplication.value = true;
      applicationDetail.value = res.data || {};
    } else {
      isApplication.value = false;
    }
  }
};
onMounted(async () => {
  if (checkAuth()) {
    if(isForbiddenResourcePool.value) {
      router.replace('/home');
    }
    return;
  }
  if (allowApplication.value) {
    getUserQuery();
  }
});

function checkAuth() {
  return store.state.poolInfo.existAuth;
}

watch(
  () => route.query.type,
  (value) => {
    isForbiddenResourcePool.value = value;
  }
);
</script>

<style lang="less" scoped>
.main {
  background: #f1f9ff;
  margin-top: -20px;
  padding-top: 20px;
}
.content {
  height: calc(100vh - 134px) !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  margin: 70px 20px 20px 20px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

img {
  width: 416px;
}

.text-wrapper {
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.text {
  font-family: PingFangSC, PingFang SC, sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #00141a;
  line-height: 24px;
  font-style: normal;
  width: 346px;
  justify-content: center;
  margin: 0 auto;
  text-align: center;
}
.text-other {
  margin-top: 30px;
}
.help {
  font-size: 14px;
  text-align: center;
  color: rgba(0, 20, 26, 0.75);
  margin-top: 8px;
}
.btn {
  font-weight: 400;
  font-size: 14px;
  color: #00a0cc;
  line-height: 22px;
  font-style: normal;
}
p {
  width: 100%;
  text-align: center;
  &:nth-of-type(1) {
    font-size: 28px;
    line-height: 34px;
    font-weight: 600;
  }
  &:nth-of-type(2) {
    margin-top: 16px;
    font-size: 18px;
    color: #606972;
    line-height: 20px;
    span {
      color: #178cf9;
      cursor: pointer;
    }
  }
}
:deep(.ant-btn) {
  padding: 4px 8px;
}
</style>
