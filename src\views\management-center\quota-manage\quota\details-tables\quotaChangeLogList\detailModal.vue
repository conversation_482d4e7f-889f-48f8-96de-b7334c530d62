<template>
  <a-drawer v-model:open="open" title="处理详情" width="632" class="quota-record-item-modal" :body-style="{ padding: '16px 24px' }" @close="handleClose">
    <a-spin :spinning="loading">
      <a-space direction="vertical" :size="16">
        <div class="list">
          <div class="label">处理人：</div>
          <div class="desc">
            {{ detailInfo.updateUser }}
          </div>
        </div>
        <div class="list">
          <div class="label">处理时间：</div>
          <div class="desc">
            {{ getFormatTime(detailInfo.updateTime) }}
          </div>
        </div>
        <div class="list">
          <div class="label">处理说明：</div>
          <div class="desc">
            {{ detailInfo.updateDesc || '--' }}
          </div>
        </div>
        <div v-if="showSMS" class="list">
          <div class="label">短信通知：</div>
          <div class="time">{{ smsSendTimeTxt }}</div>
          <div><a-button type="link" :disabled="smsDisabled" :loading="smsLoading" @click="handleResend('sms')">重新发送</a-button></div>
        </div>
        <div v-if="showEmail" class="list">
          <div class="label">邮件通知：</div>
          <div class="time">{{ emailSendTimeTxt }}</div>
          <div><a-button type="link" :disabled="emailDisabled" :loading="emailLoading" @click="handleResend('email')">重新发送</a-button></div>
        </div>
        <div class="list">
          <div class="label">站内信通知：</div>
          <div class="time">{{ messageSendTimeTxt }}</div>
          <div><a-button type="link" :disabled="messageDisabled" :loading="messageLoading" @click="handleResend('message')">重新发送</a-button></div>
        </div>
        <div class="list">
          <div class="label">变更内容：</div>
          <div class="desc">
            <div class="desc-main desc-left">
              <div class="desc-title">原项目空间配额</div>
              <div class="desc-content">
                <div class="desc-lable">加速卡</div>
                {{ currQuota.acce_card || '--' }} 卡
              </div>
              <div class="desc-content">
                <div class="desc-lable">CPU</div>
                {{ currQuota.cpu || '--' }} 核
              </div>
              <div class="desc-content">
                <div class="desc-lable">镜像资源</div>
                {{ handleData(currQuota.image).sizeTxt || '--' }} {{ handleData(currQuota.image).units }}
              </div>
              <div class="desc-content">
                <div class="desc-lable">普通文件存储</div>
                {{ handleData(currQuota.general_stor).sizeTxt || '--' }} {{ handleData(currQuota.general_stor).units }}
              </div>
              <div class="desc-content">
                <div class="desc-lable">高性能文件存储</div>
                {{ handleData(currQuota?.hs_stor).sizeTxt || '--' }} {{ handleData(currQuota.hs_stor).units }}
              </div>
              <div class="desc-content">
                <div class="desc-lable">对象存储</div>
                {{ handleData(currQuota.object_stor).sizeTxt || '--' }} {{ handleData(currQuota.object_stor).units }}
              </div>
            </div>
            <div class="desc-main desc-right">
              <div class="desc-title">调整后项目空间配额</div>
              <div class="desc-content">
                <div class="desc-lable">加速卡</div>
                {{ apvQuota.acce_card || '--' }} 卡
              </div>
              <div class="desc-content">
                <div class="desc-lable">CPU</div>
                {{ apvQuota.cpu || '--' }} 核
              </div>
              <div class="desc-content">
                <div class="desc-lable">镜像资源</div>
                {{ handleData(apvQuota.image).sizeTxt || '--' }} {{ handleData(apvQuota.image).units }}
              </div>
              <div class="desc-content">
                <div class="desc-lable">普通文件存储</div>
                {{ handleData(apvQuota.general_stor).sizeTxt || '--' }} {{ handleData(apvQuota.general_stor).units }}
              </div>
              <div class="desc-content">
                <div class="desc-lable">高性能文件存储</div>
                {{ handleData(apvQuota?.hs_stor).sizeTxt || '--' }} {{ handleData(apvQuota.hs_stor).units }}
              </div>
              <div class="desc-content">
                <div class="desc-lable">对象存储</div>
                {{ handleData(apvQuota.object_stor).sizeTxt || '--' }} {{ handleData(apvQuota.object_stor).units }}
              </div>
            </div>
          </div>
        </div>
        <div class="list">
          <div class="label">变更结果：</div>
          <div class="desc-result">
            <div v-if="detailInfo?.updateResult === '' || detailInfo?.updateResult == 0"><jt-icon type="iconCheck-Circle-Fill" style="color: #00ccaa; margin-right: 9px" />配额变更成功</div>
            <div v-else>
              <span><jt-icon type="iconClose-Circle-Fill" style="color: #fe3a47; margin-right: 9px" />配额变更失败</span>
              <div class="desc-icon">{{ filterResult(detailInfo?.updateResult || '') }}</div>
            </div>
          </div>
        </div>
        <div class="list">
          <div class="label">工单号：</div>
          <div class="desc">
            {{ detailInfo.ticketId ? detailInfo.ticketId : '--' }}
          </div>
        </div>
        <div class="list">
          <div class="label">申请人：</div>
          <div class="desc">
            {{ detailInfo.ticketId ? detailInfo.reqUser : '--' }}
          </div>
        </div>
        <div class="list">
          <div class="label">申请时间：</div>
          <div class="desc">
            {{ detailInfo.ticketId ? getFormatTime(detailInfo.createTime) : '--' }}
          </div>
        </div>
        <div class="list">
          <div class="label">扩容说明：</div>
          <div class="desc line-desc">
            {{ detailInfo.ticketId ? detailInfo.reqDesc : '--' }}
          </div>
        </div>
      </a-space>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { dataToSizeConversion } from '@/utils/storage';

import { getQuotaChangeDetailApi, renotifyApi } from '@/apis/quota';
import { getFormatTime, isValidTimestamp } from '@/utils';
import { RESEND_MESSAGE_MAP, UPDATE_RESULT_CODE } from '@/constants/quota';
import { showSMS, showEmail } from '@/config';

const props = defineProps({
  visibleHandle: {
    type: Boolean,
    required: true,
  },
  quotaLogId: {
    type: [String, Number],
  },
  idType: {
    type: String,
    default: 'recordId',
  },
});
const emit = defineEmits(['close']);
const open = ref(false);
const loading = ref(false);
const detailInfo = ref({});
const currQuota = ref({});
const apvQuota = ref({});
const emailDisabled = ref(false);
const smsDisabled = ref(false);
const messageDisabled = ref(false);
const emailLoading = ref(false);
const smsLoading = ref(false);
const messageLoading = ref(false);

const handleData = (value) => {
  let obj = {};
  if (value > 1024) {
    obj = dataToSizeConversion(value);
  } else {
    obj = {
      sizeTxt: value,
      units: 'GB',
    };
  }
  return obj;
};

const getDetail = async () => {
  const params = { [props.idType]: props.quotaLogId };
  loading.value = true;
  try {
    const res = await getQuotaChangeDetailApi(params);
    loading.value = false;
    if (res.code === 0) {
      detailInfo.value = res.data;
      currQuota.value = res.data.currQuota;
      apvQuota.value = res.data.apvQuota ? res.data.apvQuota : {};
      smsSendTimeTxt.value = getSmsSendTimeTxt();
      emailSendTimeTxt.value = getEmailSendTimeTxt();
      messageSendTimeTxt.value = getMessageSendTimeTxt();

      emailDisabled.value = detailInfo.value.emailSendStatus === 4;
      smsDisabled.value = detailInfo.value.smsSendStatus === 4;
      messageDisabled.value = detailInfo.value.messageSendStatus === 4;
    } else {
      message.error(res.msg);
    }
  } catch (e) {
    console.warn(e?.toString());
  } finally {
    loading.value = false;
  }
};

// 获取变更结果
const filterResult = (code) => {
  if (code.includes('|')) {
    const arr = code.split('|');
    const rplArr = arr.map(item => UPDATE_RESULT_CODE[item]);
    return rplArr.join('；');
  } else {
    return UPDATE_RESULT_CODE[code];
  }
};

const handleResend = async (notifyType) => {
  if (notifyType === 'email') {
    emailLoading.value = true;
  } else if (notifyType === 'sms') {
    smsLoading.value = true;
  } else if (notifyType === 'message') {
    messageLoading.value = true;
  }
  const params = { recordId: detailInfo.value.id, notify: notifyType };
  try {
    const res = await renotifyApi(params);
    if (res.code === 0 && res.data) {
      message.success(`重新发送成功`);
      getDetail();
    } else {
      message.error(RESEND_MESSAGE_MAP[res.code] || `重新发送失败`);
    }
    if (notifyType === 'email') {
      emailLoading.value = false;
    } else if (notifyType === 'sms') {
      smsLoading.value = false;
    } else if (notifyType === 'message') {
      messageLoading.value = false;
    }
  } catch (e) {
    console.warn(e?.toString());
  } finally {
    if (notifyType === 'email') {
      emailLoading.value = false;
    } else if (notifyType === 'sms') {
      smsLoading.value = false;
    } else if (notifyType === 'message') {
      messageLoading.value = false;
    }
  }
};

function handleClose() {
  open.value = false;
  emit('close');
}
// 0：发送成功 1：发送失败 2：不需要发送
const getSmsSendTimeTxt = () => {
  if (detailInfo.value.smsSendStatus === 0) {
    return isValidTimestamp(detailInfo.value.smsSendTime) ? getFormatTime(detailInfo.value.smsSendTime) : '--';
  } else {
    return '--';
  }
};

const getEmailSendTimeTxt = () => {
  if (detailInfo.value.emailSendStatus === 0) {
    return isValidTimestamp(detailInfo.value.emailSendTime) ? getFormatTime(detailInfo.value.emailSendTime) : '--';
  } else {
    return '--';
  }
};

const getMessageSendTimeTxt = () => {
  if (detailInfo.value.messageSendStatus === 0) {
    return isValidTimestamp(detailInfo.value.messageSendTime) ? getFormatTime(detailInfo.value.messageSendTime) : '--';
  } else {
    return '--';
  }
};
const smsSendTimeTxt = ref('');
smsSendTimeTxt.value = getSmsSendTimeTxt();

const emailSendTimeTxt = ref('');
emailSendTimeTxt.value = getEmailSendTimeTxt();

const messageSendTimeTxt = ref('');
messageSendTimeTxt.value = getMessageSendTimeTxt();

watch(
  () => props.visibleHandle,
  (value) => {
    open.value = value;
    if (value) {
      getDetail();
    }
  }
);
</script>

<style lang="less" scoped>
.list {
  display: flex;
  align-items: center;
  align-items: flex-start;
  line-height: 24px;
  // margin-bottom: 8px;
  .label {
    min-width: 85px;
    margin-right: 8px;
    text-align: right;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 20, 26, 0.7);
    line-height: 22px;
  }
}
.desc {
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
  line-height: 22px;
  display: flex;
  justify-content: space-between;
  .desc-main {
    padding-top: 16px;
    .desc-title {
      font-weight: 500;
      font-size: 14px;
      color: #00141a;
      line-height: 22px;
      text-align: center;
    }
    .desc-content {
      font-size: 14px;
      color: rgba(0, 20, 26, 0.7);
      line-height: 22px;
      padding-top: 16px;
      display: flex;
      .desc-lable {
        width: 130px;
        text-align: right;
        padding-right: 15px;
      }
    }
  }
  .desc-left {
    width: 240px;
    height: 294px;
    border-radius: 2px;
    background: rgba(0, 20, 26, 0.04);
    margin-right: 16px;
  }
  .desc-right {
    width: 240px;
    height: 294px;
    border-radius: 2px;
    border: 1px solid rgba(0, 20, 26, 0.08);
  }
}
// .line-desc {
//   line-height: 24px;
// }
.desc-result {
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
  line-height: 22px;
  .desc-icon {
    margin-top: 7px;
    line-height: 24px;
  }
}
.time {
  width: 150px;
}
:deep(.ant-drawer .ant-drawer-body) {
  padding: 0;
}
:deep .ant-btn {
  margin-top: -4px;
}
</style>
