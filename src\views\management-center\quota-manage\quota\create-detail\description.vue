<template>
  <div>
    <a-descriptions :label-style="{ display: 'inline-block', width: '110px', textAlign: 'right' }">
      <!-- 新建项目申请 -->
      <template v-if="QUOTA_MANAGE_TABS.CREATE_PROJECT === routeType">
        <a-descriptions-item label="工单号">{{ detailItem.id }} </a-descriptions-item>
        <a-descriptions-item label="申请时间">{{ getFormatTime(detailItem.applyTime) }} </a-descriptions-item>
        <a-descriptions-item label="状态">
          <span v-if="detailItem.status === undefined">--</span>
          <jt-tag v-else :color="ADMIN_PROJECT_QUOTA_COLOR[detailItem.status]">{{ ADMIN_PROJECT_QUOTA_TEXT[detailItem.status] }}</jt-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请人">{{ detailItem.applicant }} </a-descriptions-item>
        <a-descriptions-item label="联系方式">
          <contact-item :hideMessage="detailItem.phone" :url="projectRequestUrl" :params="{ type: 'phone', id: detailItem.id }"></contact-item>
        </a-descriptions-item>
        <a-descriptions-item label="邮箱信息">
          <contact-item :hideMessage="detailItem.email" :url="projectRequestUrl" :params="{ type: 'email', id: detailItem.id }"></contact-item>
        </a-descriptions-item>
        <a-descriptions-item label="姓名">{{ detailItem.fullName || '--' }} </a-descriptions-item>
        <a-descriptions-item label="工作单位">{{ detailItem.company || '--' }} </a-descriptions-item>
        <a-descriptions-item label="所属组织" :contentStyle="{overflow:'hidden', 'text-overflow': 'ellipsis', 'white-space': 'nowrap', display: 'block'}">
          <a-tooltip :title="detailItem.group" placement="topLeft">
            {{ detailItem.group || '--' }}
          </a-tooltip>
        </a-descriptions-item>
        <a-descriptions-item label="需求名称" :contentStyle="{overflow:'hidden', 'text-overflow': 'ellipsis', 'white-space': 'nowrap', display: 'block'}">
          <a-tooltip :title="detailItem.applyTitle" placement="topLeft">
            <span>{{ detailItem.applyTitle || '--' }}</span>
          </a-tooltip>
        </a-descriptions-item>
        <a-descriptions-item  :span="2" label="新建项目配额">{{ detailItem?.content?.crprj.oldValue || 0 }} -> {{ detailItem?.content?.crprj.newValue || 0 }}个 </a-descriptions-item>
        <a-descriptions-item :span="3" label="需求描述" :contentStyle="itemContentStyle">{{ detailItem.applyDesc  || '--' }} </a-descriptions-item>
        <!-- 已处理的才显示 -->
        <template v-if="detailItem.status === ADMIN_PROJECT_QUOTA_STATUS.PASS || detailItem.status === ADMIN_PROJECT_QUOTA_STATUS.REJECT">
          <a-descriptions-item label="处理人">{{ detailItem.processor }} </a-descriptions-item>
          <a-descriptions-item label="通知用户">{{ formatNotify(detailItem.notify) }}<a @click="openDrawer" style="padding-left: 12px">详情</a> </a-descriptions-item>
          <a-descriptions-item label="处理时间">{{ getFormatTime(detailItem.processTime) }} </a-descriptions-item>
          <a-descriptions-item :span="3" label="处理说明" :contentStyle="itemContentStyle">{{detailItem.processDesc || '--'}} </a-descriptions-item>
        </template>
      </template>

      <!-- 新建配额申请 -->
      <template v-if="QUOTA_MANAGE_TABS.CREATE_QUOTA === routeType">
        <a-descriptions-item label="用户名">{{ detailItem.userName }} </a-descriptions-item>
        <a-descriptions-item label="联系方式">
          <contact-item :hideMessage="detailItem.phone" :url="quotaRequestUrl" :params="{ type: 'phone', userName: detailItem.userName }"></contact-item>
        </a-descriptions-item>
        <a-descriptions-item label="邮箱信息">
          <contact-item :hideMessage="detailItem.email" :url="quotaRequestUrl" :params="{ type: 'email', userName: detailItem.userName }"></contact-item>
        </a-descriptions-item>
        <a-descriptions-item label="所属组织" :contentStyle="{overflow:'hidden', 'text-overflow': 'ellipsis', 'white-space': 'nowrap', display: 'block'}">
          <a-tooltip :title="detailItem.group" placement="topLeft">
            {{ detailItem.group || '--' }}
          </a-tooltip>
        </a-descriptions-item>
        <a-descriptions-item label="新建项目配额">{{ detailItem.createProjectQuota }} </a-descriptions-item>
        <a-descriptions-item label="已新建项目数">{{ detailItem.createProjectNum }} </a-descriptions-item>
      </template>
    </a-descriptions>
    <detail-drawer v-model:open="open" :edit-auth="editAuth" idType="ticketId" :id="detailItem.id" />
  </div>
</template>

<script setup>
import { QUOTA_MANAGE_TABS, ADMIN_PROJECT_QUOTA_TEXT, ADMIN_PROJECT_QUOTA_COLOR, ADMIN_PROJECT_QUOTA_STATUS, APV_QUATO_EDIT_AUTH } from '@/constants/quota';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';
import { getFormatTime } from '@/utils';
import DetailDrawer from '../../components/detailDrawer';
import ContactItem from '@/components/contactItem/index.vue';
// 获取用户工单相关的用户信息
const projectRequestUrl = '/web/admin/approval/v1/user/userInfo';
//获取用户配额相关的用户信息
const quotaRequestUrl = '/web/admin/project/v1/quota/user/userInfo';
const editAuth = checkPermissionByApvAndMgtItem(APV_QUATO_EDIT_AUTH);
const sendTypeText = {
  message: '站内信',
  sms: '短信',
  email: '邮件',
};

defineProps({
  detailItem: {
    type: Object,
    required: true,
  },
  routeType: {
    type: String,
    required: true,
  },
});
const formatNotify = (notify) => {
  if (notify && notify.length > 0) {
    return notify
      .map((val) => {
        return sendTypeText[val];
      })
      .join('、');
  } else {
    return '--';
  }
};

const itemContentStyle = {
  'white-space': 'wrap'
}

const open = ref(false);
const openDrawer = () => {
  open.value = true;
};
</script>

<style lang="less" scoped>
// :deep .ant-descriptions .ant-descriptions-item-container .ant-descriptions-item-content {
//   display: inline-block;
//   white-space: nowrap;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   &:hover {
//     cursor: pointer;
//   }
// }
</style>
