<template>
  <a-drawer :open="openDrawer" :body-style="datasetDrawerBodyStyle" :mask-closable="false" :width="890" class="prompt-template-drawer" title="引用Prompt" @close="drawerCancel">
    <div v-if="visibleNotice" class="notice">
      <p><span class="notice-btn" @click="goPrompt">提示词管理</span> 模块可体验提示词构造、管理、优化等功能 <span class="notice-btn" @click="goPrompt">去前往</span></p>
      <jt-icon type="iconguanbi" @click="closeNotice" />
    </div>
    <prompt-template ref="promptTemplateRef" class="prompt-template-drawer" :has-modal="true" :hasDrawer="true" @selectPrompt="selectPrompt" />
  </a-drawer>
  <use-modal :openUse="openUse" :info="promptVal" @closeUse="closeUse" />
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { useRouter } from 'vue-router';
import promptTemplate from '@/views/project-modules/assets-management/prompt-engineering/list/promptTemplate/index.vue';
import UseModal from '../tip-word-drawer/use-modal.vue';

const props = defineProps({
  openDrawer: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['closed']);

const router = useRouter();
const openDrawer = ref(false);
const visibleNotice = ref(true);
const openUse = ref(false);
const promptVal = ref('');


const datasetDrawerBodyStyle = {
  paddingTop: '9px',
  paddingBottom: '9px',
};

const closeNotice = () => {
  visibleNotice.value = false;
};

const goPrompt = () => {
  router.push('/prompt-engineering');
};

const drawerCancel = () => {
  openDrawer.value = false;
};

const selectPrompt = (data) => {
  const { promptContent } = data || {};
  promptVal.value = promptContent;
  openUse.value = true;
};

const closeUse = (data) => {
  openUse.value = false;
  if (data) {
    emit('closed', data);
  }
};

watch(
  () => props.openDrawer,
  () => {
    openDrawer.value = props.openDrawer;
  }
);
</script>

<style lang="less" scoped>
.notice {
  height: 40px;
  line-height: 40px;
  background: #eafbff;
  border: 1px solid #8ddbeb;
  border-radius: 2px;
  padding: 0 16px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  .notice-btn {
    color: #00a0cc;
    cursor: pointer;
  }
}
</style>