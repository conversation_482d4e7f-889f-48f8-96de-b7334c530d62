<!-- 数据清洗 -->
<template>
  <div v-if="configData.cleaners" class="detail-instance-list">
    <!-- 异常清洗配置 -->
    <div class="sub-title-item">异常清洗配置</div>
    <a-table :columns="cleanerColumns" :data-source="configData.cleaners" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
  <div v-if="configData.filters" class="detail-instance-list">
    <!-- 过滤配置 -->
    <div class="sub-title-item">过滤配置</div>
    <a-table :columns="filterColumns" :data-source="configData.filters" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
  <div v-if="configData.protectors" class="detail-instance-list">
    <!-- 去隐私设置 -->
    <div class="sub-title-item">去隐私设置</div>
    <a-table :columns="protectorColumns" :data-source="configData.protectors" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
  <div v-if="configData.deduplicators" class="detail-instance-list">
    <!-- 相似度去重算子配置 -->
    <div class="sub-title-item">相似度去重算子配置</div>
    <a-table :columns="deduplicatorColumns" :data-source="configData.deduplicators" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
  <div v-if="configData.text_mapper_oprs" class="detail-instance-list">
    <!-- 多模态-文本异常算子配置 -->
    <div class="sub-title-item">多模态-文本异常算子配置</div>
    <a-table :columns="textMapperColumns" :data-source="configData.text_mapper_oprs" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
  <div v-if="configData.text_filter_oprs" class="detail-instance-list">
    <!-- 多模态-文本过滤算子 -->
    <div class="sub-title-item">多模态-文本过滤算子</div>
    <a-table :columns="textFilterColumns" :data-source="configData.text_filter_oprs" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
  <!-- 多模态-图片算子 -->
  <div v-if="configData.image_oprs" class="detail-instance-list">
    <div class="sub-title-item">多模态-图片算子</div>
    <a-table :columns="imageColumns" :data-source="configData.image_oprs" :pagination="false">
      <template #emptyText>
        <light-empty title="暂无数据" />
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import LightEmpty from './light-empty.vue';

const props = defineProps({
  configData: {
    type: Object,
    default: () => {
      return {
        cleaners: [],
        filters: [],
        protectors: [],
        deduplicators: [],
        text_mapper_oprs: [],
        text_filter_oprs: [],
        image_oprs: [],
      };
    },
  },
  sampleData: {
    type: Object,
    required: true,
  },
});

const cleanerColumns = ref([]);
const filterColumns = ref([]);
const protectorColumns = ref([]);
const deduplicatorColumns = ref([]);
const textMapperColumns = ref([]);
const textFilterColumns = ref([]);
const imageColumns = ref([]);

const baseColumns = ref([
  {
    title: '配置项目',
    dataIndex: 'operator_name',
    key: 'operator_name',
    customRender: ({ text }) => {
      return text || '--';
    },
  },
  {
    title: '处理样本数',
    dataIndex: 'sample_count',
    key: 'sample_count',
    customRender: ({ text }) => {
      return text || 0;
    },
  },
]);

const addColumn = {
  title: '阈值',
  dataIndex: 'params',
  key: 'params',
  customRender: ({ text }) => {
    if (text) {
      const keys = Object.keys(text);
      if (keys.length === 1) {
        let key = keys.toString();
        if (key === 'length' || key === 'range') {
          return text[key].replace(',', '-');
        } else {
          return text[key];
        }
      } else {
        let value = [];
        for (const key of keys) {
          value.push(text[key]);
        }
        return value.toString();
      }
    } else {
      return '--';
    }
  },
};

const Filter_Columns = {
  cleaners: cleanerColumns,
  filters: filterColumns,
  protectors: protectorColumns,
  deduplicators: deduplicatorColumns,
  text_mapper_oprs: textMapperColumns,
  text_filter_oprs: textFilterColumns,
  image_oprs: imageColumns,
};

watch(
  () => props,
  (val) => {
    if (val.configData && val.sampleData) {
      const { cleaners, filters, protectors, deduplicators, text_mapper_oprs, text_filter_oprs, image_oprs } = val.configData;
      const objData = { cleaners, filters, protectors, deduplicators, text_mapper_oprs, text_filter_oprs, image_oprs };
      Object.keys(objData).forEach(item => {
        if (objData[item]) {
          Filter_Columns[item].value = filterParams(objData[item]);
          objData[item].map(item => {
            item.sample_count = val.sampleData[item.operator_id].sample_count;
            return item;
          })
        }
      })
    }
  },
  {
    deep: true,
  }
);

const filterParams = (arr) => {
  const filterArr = arr.filter(el => Object.keys(el.params).length);
  if (filterArr.length) {
    let columns = JSON.parse(JSON.stringify(baseColumns.value));
    columns.splice(1, 0, addColumn);
    return columns;
  } else {
    return baseColumns.value;
  }
};
</script>

<style lang="less" scoped>
.detail-instance-list {
  margin-top: 32px;
}
</style>
