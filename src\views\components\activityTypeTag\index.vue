<template>
  <jt-tag rounded="small" :color="color">{{ text }}</jt-tag>
</template>
  
<script setup>
import { ACTIVE_TYPE, ACTIVE_TYPE_COLOR } from './index.js';
  
const props = defineProps({
  text: {
    type: String,
    default: '',
  }
});

const color = ref('default');
watch(
  () => props.text,
  (newValue) => {
    const index = Object.keys(ACTIVE_TYPE).find((key) => ACTIVE_TYPE[key] === newValue);
    color.value = ACTIVE_TYPE_COLOR[index] || 'default';
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="less" scoped></style>
