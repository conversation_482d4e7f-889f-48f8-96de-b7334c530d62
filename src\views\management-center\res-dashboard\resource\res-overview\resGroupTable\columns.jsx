import { RESOURCE_ATTRIBUTES } from '@/constants/projectSpace';
import { transformObjectToArray } from '@/utils';
import { SCENARIO_TYPE, SCENARIO_TYPE_MAP, SOURCE_TYPE } from '@/constants/nodeList.js';

export const columns = [
  {
    title: '资源组名称',
    filters: transformObjectToArray(RESOURCE_ATTRIBUTES),
    dataIndex: 'groupName',
  },
  {
    title: '适用场景',
    dataIndex: 'scene',
    customRender: ({ text, record }) => {
      return text;
    },
    filters: [
      {
        value: SCENARIO_TYPE_MAP.DATA,
        text: SCENARIO_TYPE[SCENARIO_TYPE_MAP.DATA],
      },
      {
        value: SCENARIO_TYPE_MAP.TRAIN,
        text: SCENARIO_TYPE[SCENARIO_TYPE_MAP.TRAIN],
      },
      {
        value: SCENARIO_TYPE_MAP.OPTIMIZE,
        text: SCENARIO_TYPE[SCENARIO_TYPE_MAP.OPTIMIZE],
      },
      {
        value: SCENARIO_TYPE_MAP.INFERENCE,
        text: SCENARIO_TYPE[SCENARIO_TYPE_MAP.INFERENCE],
      },
    ],
  },
  {
    title: '资源组类型',
    dataIndex: 'resourceType',
    filters: transformObjectToArray(SOURCE_TYPE),
    customRender({ text }) {
      return SOURCE_TYPE[text] || '--';
    },
  },
  {
    title: '加速卡类型',
    dataIndex: 'gpuCardType2',
    customRender: ({ text, record }) => {
      return record.resourceType === 'cpu' ? '--' : record.gpuCardType;
    },
  },
  {
    title: '所属集群',
    dataIndex: 'clusterName',
    customRender: ({ text, record }) => {
      return text;
    },
  },
  {
    title: '资源类型',
    dataIndex: 'gpuCardType',
    customRender: ({ text, record }) => {
      return record.gpuCardType ? record.gpuCardType : 'CPU';
    },
  },
  {
    title: '加速卡使用率',
    dataIndex: 'gpuUseRate',
    sorter: true,
    customRender: ({ text, record }) => {
      return record.resourceType === 'cpu' ? '--' : (text === 0 ? 0 : `${text}%`);
    },
  },
  {
    title: '加速卡显存使用率',
    dataIndex: 'gpuMemUseRate',
    sorter: true,
    customRender: ({ text, record }) => {
      return record.resourceType === 'cpu' ? '--' : (text === 0 ? 0 : `${text}%`);
    },
  },
  {
    title: 'CPU使用率',
    dataIndex: 'cpuUseRate',
    sorter: true,
    customRender: ({ text, record }) => {
      return text === 0 ? 0 : `${text}%`;
    },
  },
  {
    title: '内存使用率',
    dataIndex: 'memUseRate',
    sorter: true,
    customRender: ({ text, record }) => {
      return text === 0 ? 0 : `${text}%`;
    },
  },
  {
    title: '占用率',
    dataIndex: 'occupyRate',
    customRender: ({ text, record }) => {
      return text === 0 ? 0 : `${text}%`;
    },
    sorter: true,
  },
  {
    title: '已用/总节点',
    dataIndex: 'useNodeNum',
    customRender: ({ text, record }) => {
      const { useNodeNum, totalNodeNum } = record;
      return `${useNodeNum} / ${totalNodeNum}`;
    },
  },
  {
    title: '已用/总算力',
    dataIndex: 'useComputingPower',
    customRender: ({ text, record }) => {
      const { useComputingPower, totalComputingPower, resourceType } = record;
      const unit = resourceType === 'cpu' ? '核' : '卡';
      return `${useComputingPower} / ${totalComputingPower}${unit}`;
    },
  },

  // {
  //   title: '已用/总加速卡',

  //   dataIndex: 'acce_card',
  //   customRender: ({ text, record }) => {
  //     return `${record.gpuCardUsed}/${record.gpuCardTotal}`;
  //   },
  // },
  // {
  //   title: '已用/总CPU',

  //   dataIndex: 'cpu',
  //   customRender: ({ text, record }) => {
  //     return `${record.cpuUsed}/${record.cpuTotal}`;
  //   },
  // },
  // {
  //   title: '已用/总内存GB',
  //   dataIndex: 'store',
  //   customRender: ({ text, record }) => {
  //     return `${record.memoryUsed}/${record.memoryTotal}`;
  //   },
  // },
];
