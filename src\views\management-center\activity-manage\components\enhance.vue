<template>
  <div>
    <slot></slot>
    <a-table :loading="loading" :dataSource="dataSource" :columns="columns" :pagination="false">  
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'before_sample'">
          <template v-if="column.key === 'before_sample'">
            <a-image v-if="configData?.enhance_mode === 'rule_enhance' && configData?.sub_type === 'image'" :width="100" :src="text?.image" />
            <a-tooltip v-else-if="configData?.enhance_mode === 'rule_enhance' && configData?.sub_type === 'pure_text'" placement="topLeft">
              <template #title>
                <span v-html="text?.text ? `${highlight(text?.text)}` : ''"></span>
              </template>
              <div class="table-text" v-html="text?.text ? `${highlight(text?.text)}` : ''"></div>
            </a-tooltip>
            <div v-else class="table-content">
              <div v-if="text?.conversations">
                <div v-for="(item, index) in text.conversations" :key="index">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      <span v-html="item?.prompt ? `问：${highlight(item?.prompt)}` : ''"></span>
                    </template>
                    <div class="table-col" v-html="item?.prompt ? `问：${highlight(item?.prompt)}` : ''"></div>
                  </a-tooltip>
                  <a-tooltip placement="topLeft">
                    <template #title>
                      <span v-html="item?.response ? `答：${highlight(item?.response)}` : ''"></span>
                    </template>
                    <div class="table-col" v-html="item?.response ? `答：${highlight(item?.response)}` : ''"></div>
                  </a-tooltip>
                </div>
              </div>
              <div v-else>
                <a-tooltip placement="topLeft">
                  <template #title>
                    <span v-html="text?.prompt ? `问：${highlight(text?.prompt)}` : ''"></span>
                  </template>
                  <div class="table-col" v-html="text?.prompt ? `问：${highlight(text?.prompt)}` : ''"></div>
                </a-tooltip>
                <a-tooltip placement="topLeft">
                  <template #title>
                    <span v-html="text?.response ? `答：${highlight(text?.response)}` : ''"></span>
                  </template>
                  <div class="table-col" v-html="text?.response ? `答：${highlight(text?.response)}` : ''"></div>
                </a-tooltip>
              </div>
            </div>
          </template>
          <template v-if="column.key === 'after_sample'">
            <a-image v-if="configData?.enhance_mode === 'rule_enhance' && configData?.sub_type === 'image'" :width="100" :src="text?.image" />
            <a-tooltip v-else-if="configData?.enhance_mode === 'rule_enhance' && configData?.sub_type === 'pure_text'" placement="topLeft">
              <template #title>
                <span v-html="text?.text ? `${highlight(text?.text)}` : ''"></span>
              </template>
              <div class="table-text" v-html="text?.text ? `${highlight(text?.text)}` : ''"></div>
            </a-tooltip>
            <div v-else class="table-content">
              <div v-if="text?.conversations">
                <div v-for="(item, index) in text.conversations" :key="index">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      <span v-html="`问：${highlight(item?.prompt)}`"></span>
                    </template>
                    <div class="table-col" v-html="`问：${highlight(item?.prompt)}`"></div>
                  </a-tooltip>
                  <a-tooltip placement="topLeft">
                    <template #title>
                      <span v-html="`答：${highlight(item?.response)}`"></span>
                    </template>
                    <div class="table-col" v-html="`答：${highlight(item?.response)}`"></div>
                  </a-tooltip>
                </div>
              </div>
              <div v-else>
                <a-tooltip placement="topLeft">
                  <template #title>
                    <span v-html="text?.prompt ? `问：${highlight(text?.prompt)}` : ''"></span>
                  </template>
                  <div class="table-col" v-html="text?.prompt ? `问：${highlight(text?.prompt)}` : ''"></div>
                </a-tooltip>
                <a-tooltip placement="topLeft">
                  <template #title>
                    <span v-html="text?.response ? `答：${highlight(text?.response)}` : ''"></span>
                  </template>
                  <div class="table-col" v-html="text?.response ? `答：${highlight(text?.response)}` : ''"></div>
                </a-tooltip>
              </div>
            </div>
          </template>
        </template>
      </template>
    </a-table>
    <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>

<script setup>
import { getFileServiceUrl, getFileContent, getMultiContent, downloadImage } from '@/apis/activityManage';
import { useStore } from 'vuex';

const store = useStore();

const props = defineProps({
  configData: {
    type: Object,
    default: () => {},
  },
  columns: {
    type: Array,
    default: () => [],
  },
});

const loading = ref(false);
const dataSource = ref([]);

const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});

const getTableList = async () => {
  loading.value = true;
  // 获取请求url
  let resUrl = await getFileServiceUrl({}, { projectId: props.configData?.projectId, poolId: store.state.poolInfo.id });

  /* 
    1.增强类型：多模态增强，增强方式：大模型增强，显示图片、文本
    2.增强类型：文本增强，增强方式：问答对抽取，显示问题、答案
  */
  if (props.configData?.type === 'multimodal' && props.configData?.enhance_mode === 'large_model_enhance') {
    const params = {
      previewType: '4-5',
      dataPath: props.configData?.post_dataset_path,
      fileName: '',
      page: paginationOptions.value.pageNum,
      pageSize: paginationOptions.value.pageSize
    };
    const concatUrl = resUrl?.data.replace('/fileList', '/dataset/preview');
    const requestUrl = concatUrl.replace('https://uatpm1.jiutian.10086.cn/kunlun', '');
    // const res = await getMultiContent(requestUrl, params, { projectId: props.configData?.projectId, poolId: store.state.poolInfo.id, 'Content-Type': 'multipart/form-data' });
    const res = await getMultiContent(requestUrl, params);
    if (res.resultCode === '200') {
      res.body.list.forEach(el => {
        el.image = dlImage(resUrl?.data.replace('/fileList', el.imageBase));
        dataSource.value.push(el);
      });
      paginationOptions.value.total = res.body.total;
    }
    loading.value = false;
  } else if (props.configData?.enhance_mode === 'prompt_response_pairs_extract') {
    const params = {
      previewType: '0-0',
      dataPath: props.configData?.post_dataset_path,
      fileName: '',
      page: paginationOptions.value.pageNum,
      pageSize: paginationOptions.value.pageSize
    };
    const concatUrl = resUrl?.data.replace('/fileList', '/dataset/preview');
    const requestUrl = concatUrl.replace('https://uatpm1.jiutian.10086.cn/kunlun', '');
    // const res = await getMultiContent(requestUrl, params, { projectId: props.configData?.projectId, poolId: store.state.poolInfo.id, 'Content-Type': 'multipart/form-data' });
    const res = await getMultiContent(requestUrl, params);
    if (res.resultCode === '200') {
      dataSource.value = res.body.list;
      paginationOptions.value.total = res.body.total;
    }
    loading.value = false;
  } else {
    const formData = new FormData();
    formData.append('filePath', props.configData?.diff_path);
    formData.append('pageNum', paginationOptions.value.pageNum);
    formData.append('pageSize', paginationOptions.value.pageSize);

    const concatUrl = resUrl?.data.replace('/fileList', '/dataset/fileContent'); 
    const requestUrl = concatUrl.replace('https://uatpm1.jiutian.10086.cn/kunlun', 'uatpm1');

    const res = await getFileContent(requestUrl, formData, { projectId: props.configData?.projectId, poolId: store.state.poolInfo.id, 'Content-Type': 'multipart/form-data' });
    if (res.resultCode === '200') {
      const body = JSON.parse(res?.body);
      count.value = body?.post_count || 0;
      body?.samples_list.forEach((item) => {
        item.before_sample = JSON.parse(item.before_sample);
        item.after_sample = JSON.parse(item.after_sample);
        dataSource.value.push(item);
      });
      paginationOptions.value.total = res.data.total;
    }
    loading.value = false;
  }
};

// 下载图片
const dlImage = async (imageBase) => {
  try {
    const response = await downloadImage(imageBase, {}, {responseType: 'blob'});
    const blob = new Blob([response.data], {
      type: response.headers['content-type'],
    });
    const blobUrl = window.URL.createObjectURL(blob);
    return blobUrl;
  } catch (err) {
    console.error('Error downloading image:', err);
    return imageFallback;
  }
};

const highlight = (text) => {
  if (!text) return text;
  // const regex = /<(?!\/?highlight|\/?line-through)(.*?)>/g;
  if (text.includes('<highlight>')) {
    text = text.replace(/<highlight>|<\/highlight>/g, (match) => {
      if (match === '<highlight>') {
        return '<span style="color: #fbc075">';
      } else if (match === '</highlight>') {
        return '</span>';
      }
    });
  }
  return text;
};

const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getTableList();
};

const changePageNum = (num) => {
  paginationOptions.value.pageNum = num;
  getTableList();
};

watch(
  () => props.configData,
  (val) => {
    if (val?.status && val?.status !== 6) {
      getTableList();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.table-text {
  max-width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-content {
  .table-col {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>