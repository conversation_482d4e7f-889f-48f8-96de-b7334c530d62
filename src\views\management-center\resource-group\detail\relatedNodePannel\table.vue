<template>
  <div class="related-node-table">
    <a-config-provider>
      <template #renderEmpty>
        <jt-empty :show-operation="showOperation" title="节点" />
      </template>
      <a-table :columns="columns" :data-source="tableData.data" :pagination="false" :loading="tableLoading" @change="tableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <jt-tag :color="record.status === '0' ? '' : record.status === '1' ? 'green' : 'red'">
              {{ record.status === '0' ? '不可用' : record.status === '1' ? '可用' : '禁止调度' }}
            </jt-tag>
          </template>
          <template v-else-if="column.key === 'nodeName'"
            ><span class="route-hover-item" @click="toNodeDetail(record)">{{ record.nodeName }}</span></template
          >
          <template v-else-if="column.key === 'usedTotalGpu'">{{ gpuCardNum(record) }}</template>
          <template v-else-if="column.key === 'usedTotalCpu'"> {{ record.cpuUsed + '/' + record.cpuTotal }} </template>
          <template v-else-if="column.key === 'usedTotalMem'"> {{ record.memoryUsed + '/' + record.memoryTotal }} </template>
        </template>
      </a-table>
    </a-config-provider>
    <jt-pagination :pageSizeOptions="[5, 10, 20, 50]" :total="tableData.total" :page-num="tablePage.pageNum" :page-size="tablePage.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>
<script setup>
import { resourceGroupApi } from '@/apis';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

import { RESOURCE_TYPE_ENUM_OBJ } from '@/constants/resourceGroup.js';
let sortField = undefined;
let sortOrder = undefined;
const { state } = useStore();
const router = useRouter();
const nodeInfo = computed(() => state.resGroup.resGroupInfo);

const props = defineProps({
  searchValue: {
    type: String,
    default: '',
  },
  resGroupId: {
    type: [String, Number],
    default: '',
  },
});
const columns = [
  {
    title: '节点名称',
    dataIndex: 'nodeName',
    key: 'nodeName',
    width: '29%', // 需要跟上面的alert中的资源配比大致对其
  },
  {
    title: '节点状态',
    dataIndex: 'status',
    key: 'status',
    filters: [
      { text: '可用', value: 1 },
      { text: '不可用', value: 0 },
      { text: '禁止调度', value: 2 },
    ],
  },
  {
    title: '已用/总加速卡',
    dataIndex: 'usedGpuCard',
    key: 'usedTotalGpu',
    sorter: true,
  },
  {
    title: '已用/总CPU',
    key: 'usedTotalCpu',
    dataIndex: 'usedCpu',
    sorter: true,
  },
  {
    title: '已用/总内存（GB）',
    key: 'usedTotalMem',
    dataIndex: 'usedMemory',
    sorter: true,
  },
];
const tableData = ref([]);
const tableLoading = ref(false);
const tablePage = ref({
  pageNum: 1,
  pageSize: 10,
});

const toNodeDetail = (record) => {
  router.push({
    path: '/resource-group/node-detail',
    query: {
      nodeId: record.id,
    },
  });
};

const showOperation = computed(() => {
  const hasFilter = () => {
    let flag = false;
    for (const i in tableFilterObj.value) {
      if (tableFilterObj.value[i]) {
        flag = true;
      }
    }
    return flag;
  };
  return !(props.searchValue || hasFilter());
});
const gpuCardNum = (record) => {
  if (nodeInfo.type === RESOURCE_TYPE_ENUM_OBJ.CPU) {
    return '-';
  }
  return record.gpuCardTotal === 0 ? '-' : record.gpuCardUsed + '/' + record.gpuCardTotal;
};

const changePageSize = (pageSize) => {
  tablePage.value.pageSize = pageSize;
  changePageNum(1);
};
const changePageNum = (pageNum) => {
  tablePage.value.pageNum = pageNum;
  getData();
};

const tableFilterObj = ref({});
const tableChange = (pagination, filters, sorter) => {
  tableFilterObj.value = filters;
  if (sorter.order) {
    sortField = sorter.field;
    sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
  } else {
    sortField = undefined;
    sortOrder = undefined;
  }
  changePageNum(1);
};
const getData = () => {
  const params = {
    keyWord: props.searchValue,
    groupId: props.resGroupId,
    sortField,
    sortOrder,
    status: tableFilterObj.value.status,
    ...tablePage.value,
  };
  resourceGroupApi
    .getResourceGroupNodeList(params)
    .then((res) => {
      if (res.code === 0) {
        tableData.value = res.data;
      }
    })
    .finally(() => {
      tableLoading.value = false;
    });
};
watch(
  () => props.searchValue,
  () => {
    changePageNum(1);
  }
);

onMounted(() => {
  getData();
});

defineExpose({
  getData,
});
</script>
