export const leftTopCard = [
    { "name": "CM-1000B基座大模型", "value": "18432卡", rate: 100 },
    { "name": "CM-200B基座大模型", "value": "9424卡", rate: 100 },
    { "name": "CM-57B基座大模型", "value": "4096卡", rate: 100 },
    { "name": "CM-57B大模型微调", "value": "1712卡", rate: 100 },
    { "name": "CM-13.9B基座大模型", "value": "512卡", rate: 100 },
];

export const leftBottomCard = [
    { "name": "社会综治大模型", "value": "64卡", rate: 99 },
    { "name": "医疗大模型", "value": "64卡", rate: 98 },
    { "name": "政务大模型", "value": "64卡", rate: 98 },
    { "name": "家庭生态大模型", "value": "64卡", rate: 97 },
    { "name": "文体大模型", "value": "64卡", rate: 96 },
    { "name": "教育大模型", "value": "64卡", rate: 95 },
    { "name": "出行大模型", "value": "64卡", rate: 95 },
    { "name": "客服大模型", "value": "64卡", rate: 80},
    { "name": "电商金融大模型", "value": "56卡", rate: 83 },
    { "name": "企业通话大模型", "value": "48卡", rate: 85 },
    { "name": "代码大模型", "value": "32卡", rate: 90 },
    { "name": "工业大模型", "value": "32卡", rate: 90 },
    { "name": "物联网大模型", "value": "32卡", rate: 90 },
    { "name": "交通大模型", "value": "32卡", rate: 82 },
    { "name": "工程设计大模型", "value": "32卡", rate: 80 },
    { "name": "安全大模型", "value": "32卡", rate: 80 },
    { "name": "专利大模型", "value": "24卡", rate: 97 }
]

export const pieChartData = [
    {"name": "华为", "value": 4253, "percentage": 83.08, color: '#00E4FF'},
    {"name": "英伟达", "value": 844, "percentage": 16.49, color: '#00FFCA'},
    {"name": "昆仑芯", "value": 10, "percentage": 0.20, color: '#488BFF'},
    {"name": "燧原", "value": 4, "percentage": 0.08, color: '#C3F9FF'},
    {"name": "寒武纪", "value": 2, "percentage": 0.04, color: '#FF6B59'},
    {"name": "海光", "value": 2, "percentage": 0.04, color: '#FFB057'},
    {"name": "壁仞", "value": 2, "percentage": 0.04, color: '#FF8CFD'},
    {"name": "天数", "value": 2, "percentage": 0.04, color: '#FFF273'}
]