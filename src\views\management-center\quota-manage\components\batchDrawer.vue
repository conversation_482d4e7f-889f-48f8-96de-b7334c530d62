<template>
  <a-drawer v-model:open="open" :title="`${modalTitle[type]}`" width="400" :body-style="{ padding: '8px 24px' }" :footer-style="{ height: '64px', padding: '8px 24px' }" @close="handleClose(false)">
    <div class="select-count">已选工单： {{ props.id.length }}个</div>
    <div class="table-list">
      <a-table :columns="columns" :data-source="dataSource" :scroll="{ y: 150 }" :pagination="false"></a-table>
    </div>
    <a-form ref="formRef" :model="form" :rules="formRules" layout="vertical">
      <a-form-item label="处理说明" name="processDesc">
        <JtTextarea v-model:value="form.processDesc" :rows="6" class="textarea space-info" placeholder="请输入处理说明" :maxlength="400" />
      </a-form-item>
      <a-form-item label="常用说明" style="margin-bottom: 32px;">
        <a-select v-model:value="generalDesc" placeholder="请选择" allowClear :options="descOptions"></a-select>
      </a-form-item>
      <a-form-item label="通知用户">
        <a-checkbox-group v-model:value="form.notify">
          <a-checkbox v-if="showEmail" value="email" name="type">发送邮件</a-checkbox>
          <a-checkbox v-if="showSMS" value="sms" name="type">发送短信</a-checkbox>
          <a-checkbox value="message" name="type" disabled>发送站内信</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
    <template #footer>
      <div style="text-align: right">
        <a-space>
          <a-button @click="handleClose(false)">取消</a-button>
          <a-button v-if="type === 1" danger :loading="loading" @click="handleOk">驳回</a-button>
          <a-button v-else type="primary" :loading="loading" class="kl-create-btn" @click="handleOk">通过</a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, reactive } from 'vue';
import JtTextarea from '@/components/JtTextarea.vue';
import { quotaApi } from '@/apis';
import { message } from 'ant-design-vue';
import { showSMS, showEmail } from '@/config';

const props = defineProps({
  // 1(驳回) 0(同意)
  type: {
    type: Number,
    required: true,
  },
  id: {
    type: Array,
    default: () => [],
  },
});

const modalTitle = {
  0: '通过申请',
  1: '驳回申请',
};

const loading = ref(false);

const open = defineModel('open', { required: true, default: false });

const dataSource = ref([]);

const columns = [
  {
    title: '序号',
    customRender: ({ text, record, index }) => index + 1,
    width: 60,
  },
  {
    title: '工单号',
    dataIndex: 'id',
    key: 'id',
    // width: 170,
  },
  {
    title: '新建项目配额',
    dataIndex: 'content',
    key: 'content',
    customRender: ({ text }) => {
      const oldVal = text?.crprj.oldValue;
      const newVal = text?.crprj.newValue;
      return `${oldVal} -> ${newVal} 个`;
    },
  },
];

const formRef = ref(null);

/*
sms:短信；message:站内信；email:邮箱
*/
const form = ref({
  processDesc: '',
  notify: ['message'],
});

const formRules = reactive({
  processDesc: [
    {
      required: false,
      message: '',
    },
    { max: 400, message: '400个字符以内', trigger: ['blur', 'change'] },
  ],
});

const generalDesc = ref(null);
const descOptions = ref([]);

const getCommonDesc = async () => {
  const params = {
    type: 'CreateProject',
    scene: props.type,
  };
  const res = await quotaApi.getCommonDesc(params);
  if (res.code === 0) {
    descOptions.value = res?.data.map((item) => {
      return {
        lable: item,
        value: item,
      };
    });
  }
};

const batchList = async () => {
  const res = await quotaApi.getBatchList(props.id);
  if (res.code === 0) {
    dataSource.value = res?.data;
  }
};

const handleOk = async () => {
  await formRef.value.validate();
  const params = {
    ids: props.id,
    processDesc: form.value.processDesc,
    notify: form.value.notify,
    status: props.type,
  };
  loading.value = true;
  const res = await quotaApi.batchProcess(params);
  const text = props.type === 1 ? '驳回' : '通过';
  if (res.code === 0) {
    if (res.data === 0) {
      message.success(`新建项目申请批量${text}成功`);
    } else {
      if (props.id.length > res.data) {
        message.error(`${res.data}个新建项目申请${text}失败，请稍后再试`);
      } else {
        message.error(`新建项目申请批量${text}失败，请稍后再试`);
      }
    }
    handleClose(true);
  } else {
    message.error(`新建项目申请批量${text}失败，请稍后再试`);
  }
  loading.value = false;
};

const emit = defineEmits(['close']);

const handleClose = (val) => {
  open.value = false;
  form.value.processDesc = '';
  form.value.notify = ['message'];
  generalDesc.value = null;
  emit('close', val);
};

watch(
  () => generalDesc.value,
  (val) => {
    form.value.processDesc = val || '';
  }
);

watch(
  () => open.value,
  (val) => {
    if (val) {
      getCommonDesc();
      batchList();
    }
  }
);
</script>

<style lang="less" scoped>
.select-count {
  font-size: 14px;
  color: #00141a;
  line-height: 22px;
}
.table-list {
  max-height: 196px;
  background: rgba(0, 20, 26, 0.04);
  margin: 8px 0 24px;
}
.textarea-length {
  text-align: end;
  height: 18px;
  font-size: 12px;
  font-weight: 400;
  color: #aaacb4;
  line-height: 18px;
}
.sub-title {
  font-size: 16px;
  font-weight: 600;
  color: #121f2c;
}
.content-wrap-box {
  position: relative;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  min-height: ~'calc(100% - 20px)';
  border-radius: 4px;
}
.modal-title {
  display: inline-flex;
  align-items: center;
}
.warning-icon {
  font-size: 21px;
  color: #f53922;
  margin-right: 8px;
}
:deep(.ant-form-item .ant-form-item-label > label) {
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
}
:deep .ant-drawer .ant-drawer-close {
  margin-inline-end: 0;
}

:deep ::-webkit-scrollbar {
  width: 4px;
}
:deep ::-webkit-scrollbar-thumb {
  background: rgba(0, 20, 26, 0.15);
}
:deep .ant-table-wrapper .ant-table-cell-scrollbar:not([rowspan]) {
  box-shadow: 0 0 0 0 rgba(0, 20, 26, 0.04);
}
:deep ::-webkit-scrollbar-track {
  background: rgba(0, 20, 26, 0.04);
}
:deep .ant-table-container .ant-table-thead > tr > th {
  background: rgba(0, 20, 26, 0.04);
  padding-top: 16px;
  padding-bottom: 8px;
  line-height: 22px;
}
:deep .ant-table-wrapper .ant-table-thead > tr > th {
  border-bottom: none;
}
:deep .ant-table-tbody > tr > td {
  background: rgba(0, 20, 26, 0.04);
  padding-top: 0;
  padding-bottom: 8px;
  line-height: 22px;
}
:deep .ant-table-container .ant-table-tbody > tr.ant-table-row:hover > td {
  background: rgba(0, 20, 26, 0.04);
}
:deep .ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}
:deep .ant-table-wrapper .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr > td {
  border-top: none;
}
</style>
