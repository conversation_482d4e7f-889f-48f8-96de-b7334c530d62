<template>
  <div class="image-value-box">
    <div class="tag-box">
      <span class="type">运行实例</span><span class="num">{{ taskInfo?.replicas }}</span
      ><span class="unit">个</span>
    </div>
    <div><jt-icon type="iconguanbi" class="anticon"></jt-icon></div>
    <div class="tag-box">
      <div v-if="Number(taskInfo?.resourceInfo?.gpu) > 0" class="center-box" style="margin-right: 32px">
        <span class="type">使用加速卡</span><span class="num">{{ taskInfo?.resourceInfo?.gpu || defaultText }}</span
        ><span class="unit">{{ taskInfo?.resourceInfo?.gpuType || defaultText }}</span>
      </div>
      <div class="center-box" style="margin-right: 32px">
        <span class="type">使用CPU</span><span class="num">{{ taskInfo.resourceInfo?.cpu || defaultText }}</span
        ><span class="unit">核</span>
      </div>
      <div class="center-box" style="margin-right: 4px">
        <span class="type">使用内存</span><span class="num">{{ Number(taskInfo.resourceInfo?.mem) || defaultText }}</span
        ><span class="unit">GB</span>
      </div>
    </div>
  </div>
</template>
<script setup>
const defaultText = '--';
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {},
  },
});
</script>
<style lang="less" scoped>
.image-value-box {
  display: flex;
  align-items: center;
  .tag-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 64px;
    padding: 23px 20px;
    border-radius: 2px;
    background: rgba(0, 20, 26, 0.02);

    .center-box{
      display: flex;
      align-items: center;
    }
    .type {
      color: @jt-text-color;
      margin-right: 8px;
    }
    .num {
      font-size: 22px;
      font-weight: 600;
      color: @jt-text-color-primary;
    }
    .unit {
      color: #121f2c;
      margin-left: 4px;
    }
  }
  .anticon {
    color: @jt-disable-color;
    margin: 0 12px;
    font-size: 16px;
  }
}
</style>
