<template>
  <div class="template-card" :class="hasDrawer ? 'template-card-drawer' : ''" @click.stop="showDetail">
    <a-flex style="height: 24px" justify="space-between" align="center">
      <h2 ref="tooltipTitleRef" class="ellipsis-text title" @mouseenter="intoTitlePopover">
        <a-tooltip>
          <template v-if="showTitleTips" #title>{{ item?.tempName }}</template>
          {{ item?.tempName }}
        </a-tooltip>
      </h2>
      <div class="btns">
        <a-button v-if="hasModal" type="link" style="padding: 0" @click.stop="quote">引用</a-button>
        <template v-else>
          <a-button v-if="hasDrawer" type="link" style="padding: 0" @click.stop="selectPrompt">选择</a-button>
          <a-button v-else-if="type === 'preset'" type="link" style="padding: 0" @click.stop="copyPrompt">复制</a-button>
          <a-dropdown v-else v-model:open="visible">
            <a class="ant-dropdown-link" @click.prevent>...</a>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="edit">编辑</a-menu-item>
                <a-menu-item key="delete">删除</a-menu-item>
                <a-menu-item key="copy">复制</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </div>
    </a-flex>
    <div class="tag">
      <!-- <jt-tag v-if="item?.tagName" :color="tagColor(item?.tagName)" rounded="small">{{ item?.tagName }}</jt-tag> -->
      <a-tag v-if="item?.tagName" rounded="small" :style="getTagColor">{{ item?.tagName }}</a-tag>
    </div>
    <div ref="tooltipRef" class="desc" @mouseenter="intoPopover">
      <a-tooltip placement="leftTop" >
        <template v-if="showTips" #title>
          <span style="display: inline-block;overflow: hidden; overflow-y: scroll;max-height: 300px;word-break: break-all;">{{ item?.promptContent }}</span>
        </template>
        {{ item?.promptContent }}
      </a-tooltip>
    </div>
  </div>
  <delete-template-modal :open="deleteVisible" :template-info="item" @close="() => (deleteVisible = false)" @reload="reload" />
  <templates-detail :open="detailVisible" :has-modal="hasModal" :has-drawer="hasDrawer" :type="type" :template-info="item" @close="() => (detailVisible = false)" @updatePrompt="updatePrompt" />
  <templates-create :open="createVisible" :type="type" :action="modalAction" :template-info="item" @close="() => (createVisible = false)" @reload="reload" />
</template>
<script setup>
import { PROMPT_MODAL_ACTION, PROMPT_TEMPLATE_TYPE } from '@/constants/promptEngineering';
import deleteTemplateModal from './deleteTemplateModal.vue';
import templatesDetail from './templateDetail.vue';
import templatesCreate from './templateCreate.vue';
import { handleCopyWithFormat } from '@/utils';
import { computed } from 'vue';

const props = defineProps({
  hasDrawer: {
    type: Boolean,
    default: false,
  },
  hasModal: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: PROMPT_TEMPLATE_TYPE.CUSTOM,
  },
  item: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['reload', 'selectPrompt', 'clickPrompt']);

const tooltipRef = ref(null);
const tooltipTitleRef = ref(null);
const deleteVisible = ref(false);
const detailVisible = ref(false);
const createVisible = ref(false);
const visible = ref(false);
const showTips = ref(false);
const showTitleTips = ref(false);
const modalAction = ref(PROMPT_MODAL_ACTION.ADD);
// const templateInfo = reactive({
//   id: '',
// });

const handleMenuClick = (e) => {
  visible.value = false;
  if (e.key === 'edit') {
    // 调转编辑模板页面
    modalAction.value = PROMPT_MODAL_ACTION.EDIT;
    // Object.assign(templateInfo, props.item)
    createVisible.value = true;
    return;
  }
  if (e.key === 'delete') {
    deleteVisible.value = true;
    return;
  }
  if (e.key === 'copy') {
    copyPrompt();
    return;
  }
};

// 复制
const copyPrompt = () => {
  handleCopyWithFormat(props.item?.promptContent, 'prompt复制成功');
};

// 选择模板
const selectPrompt = () => {
  emit('selectPrompt', props.item);
};
// 引用
const quote = () => {
  emit('selectPrompt', props.item);
};

// 更新
const updatePrompt = (data) => {
  modalAction.value = data.action;
  // if (props.type === PROMPT_TEMPLATE_TYPE.PRESET) {
  //   // 另存为模板-saveAs
  //   Object.assign(templateInfo, props.item)
  //   // templateInfo.id = ''
  //   templateInfo.action = data.action
  // }
  detailVisible.value = false;
  createVisible.value = true;
};

// 进入气泡框
const intoPopover = () => {
  const targetElement = tooltipRef.value;
  if (targetElement?.scrollHeight) {
    showTips.value = targetElement.scrollHeight > 44;
  }
};

// 进入气泡框
const intoTitlePopover = () => {
  const targetElement = tooltipTitleRef.value;
  if (targetElement?.scrollWidth) {
    showTitleTips.value = targetElement.scrollWidth > 288;
  }
  if (targetElement?.scrollWidth && props.hasDrawer) {
    showTitleTips.value = targetElement.scrollWidth > 188;
  }
};

// 查看详情
const showDetail = () => {
  detailVisible.value = true;
  // 自定义交互
  emit('clickPrompt', props.item);
};

// 转换标签颜色模块
const getTagColor = computed(() => {
  let color = {};
  if (props.item?.color) {
    const tagColor = JSON.parse(props.item.color) || {};
    color = {
      color: tagColor.fontColor,
      background: tagColor.backgroundColor,
      borderColor: tagColor.borderColor,
    };
  }
  return color;
});

const reload = () => {
  emit('reload');
};
</script>
<style lang="less" scoped>
.template-card {
  padding: 20px;
  background-image: url('~@/assets/images/prompt/bg-template.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: -webkit-fill-available;
  &:hover {
    cursor: pointer;
  }
  .title {
    max-width: 288px;
    color: @jt-text-color-primary;
    font-size: @jt-font-size-lg;
    font-weight: @jt-font-weight-medium;
  }
  .tag {
    height: 25px;
    margin: 3px 0 12px 0;
  }
  .desc {
    font-size: @jt-font-size-base;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
    line-height: 22px;
    height: 44px;
  }
  .ant-dropdown-link {
    display: inline-block;
    height: 28px;
  }
}
.template-card.template-card-drawer {
  .title {
    width: 188px;
  }
  .desc {
    width: 225px;
  }
}
</style>
