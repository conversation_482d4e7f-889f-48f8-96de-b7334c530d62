<template>
  <JtConfirmModal :open="deleteVisible" :maskClosable="false" class="modal-height" width="480px" @cancel="onClose">
    <template #title>
      <div class="title">
        <jt-icon type="iconjinggao-tianchong" class="icon-warn"></jt-icon>
        <span class="title-text">确认要删除这个镜像文件吗？</span>
      </div>
    </template>
    <template #footer>
      <div>
        <a-button type="primary" danger class="btn-delete" :loading="confirmLoading" @click="handleOk()">删除</a-button>
        <a-button type="default" @click="onClose()">取消</a-button>
      </div>
    </template>
    <a-row class="form-row" style="margin-top: 16px">
      <a-col :span="6" class="row-title">镜像名称：</a-col>
      <a-col :span="18">
        <span class="content-text">{{ formData.imageGroupName }}</span>
      </a-col>
    </a-row>
    <a-row class="form-row" style="margin-top: 16px">
      <a-col :span="6" class="row-title">{{ formData.imageId ? '镜像文件ID：' : '文件名：' }}  </a-col>
      <a-col :span="18">
        <span class="content-text">{{ formData.imageId ? formData.imageId : formData.fileName || formData.imageGroupName }}</span>
      </a-col>
    </a-row>
    <a-row class="form-row input-row" style="margin-top: 16px">
      <a-col :span="6" class="row-title tags-box"> 镜像标签：</a-col>
      <a-col :span="18">
        <span v-for="(item, index) in formData.tags" :key="index" class="banben-tag" style="margin-right: 8px">{{ item }}</span>
      </a-col>
    </a-row>
    <a-row class="form-row input-row" style="margin-top: 16px">
      <a-col :span="6" class="row-title"> 大小：</a-col>
      <a-col :span="18">
        <span class="content-text">{{ dataSizeFormat(formData) }}</span>
      </a-col>
    </a-row>
    <a-row class="form-row input-row" style="margin-top: 16px">
      <a-col :span="6" class="row-title"> 最新推送时间：</a-col>
      <a-col :span="18">
        <span class="content-text">{{ formData.updateTime }}</span>
      </a-col>
    </a-row>
    <div class="row-tip">这将永久删除您的镜像文件，请谨慎操作</div>
  </JtConfirmModal>
</template>

<script>
import { message } from 'ant-design-vue';
import { mirrorManageApi } from '@/apis';
import { dataToSizeConversion } from '@/utils/storage'

export default {
  name: 'DeleteMirror',
  components: {},
  props: {
    deleteVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    deleteMirrorInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['childFn'],
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      formData: {},
      confirmLoading: false,
    };
  },
  created() {
    this.formData = this.deleteMirrorInfo;
  },
  methods: {
    onClose() {
      this.$emit('childFn');
    },
    dataSizeFormat(formData) {
      if (formData.totalSize === undefined || (formData.status !== 0 && formData.source === 1)) return '--'
      const { sizeTxt, units } = dataToSizeConversion(formData.totalSize, 2)
      return `${sizeTxt}${units}`
    },
    handleOk() {
      this.confirmLoading = true;
      const formData = new FormData();
      formData.append('id', this.deleteMirrorInfo.id);
      if(this.deleteMirrorInfo.imageId) {
        mirrorManageApi.canDeleteMirror(formData).then((res) => {
          if (res.data) {
            mirrorManageApi.deleteMirror(formData).then((res) => {
              this.confirmLoading = false;
              if (res.code == 0) {
                message.success(`镜像删除成功`);
                this.$emit('childFn', true);
              } else {
                message.error(`镜像删除失败，请稍后再试`);
              }
            });
          } else {
            this.confirmLoading = false;
            message.error('镜像删除失败，当前镜像正在被使用，请先停止使用再删除镜像');
          }
        });
      } else {
        mirrorManageApi.deleteMirror(formData).then((res) => {
          this.confirmLoading = false;
          if (res.code == 0) {
            message.success(`镜像删除成功`);
            this.$emit('childFn', true);
          } else {
            message.error(`镜像删除失败，请稍后再试`);
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.banben-tag {
  line-height: 30px;
  display: inline-block;
  max-width: 170px;
  min-width: 53px;
  border: 1px solid #5ac8e0;
  text-align: center;
  border-radius: 2px;
  background: #e6f7fa;
  color: @jt-primary-color;
  padding: 0 8px;
}

.row-title {
  text-align: right;
}
// .tags-box {
//   line-height: 26px;
// }

.row-tip {
  color: #ff454d;
  margin-left: 4px;
  margin-top: 16px;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  .icon-warn {
    font-size: 20px;
    color: #ff454d;
    margin-right: 9px;
  }
}

.content-text {
  color: #00141a;
}

:deep(.ant-btn-danger) {
  background-color: #ff454d;
  color: white;

  &:hover {
    background-color: #e52830;
    color: white;
  }
}
</style>
