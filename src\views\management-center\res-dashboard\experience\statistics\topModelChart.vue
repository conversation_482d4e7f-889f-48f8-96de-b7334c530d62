<!-- top5组件 -->
<template>
  <div class="box-container">
    <a-row>
      <a-col :span="10">
        <div class="t-top">
          模型热度TOP5
          <a-tooltip overlay-class-name="model-tooltip-wrapper">
            <template #title>用户每发出一次提问记做一轮对话，模型热度对应+1</template>
            <QuestionCircleOutlined class="hover-icon" />
          </a-tooltip>
        </div>
        <div class="bar-chart">
          <div v-if="hotData.length === 0" class="no-data">暂无数据</div>
          <v-chart v-else :option="hotOption" autoresize style="height: 180px; width: 100%" />
        </div>
      </a-col>
      <a-col :span="4">
        <div class="name-box"></div>
        <div class="name-content">
          <div v-for="(item, index) in nameData" :key="index" class="main-box">
            <div class="name">{{ item.serviceName }}</div>
            <div class="type" v-if="item?.chatType > -1">
              <jt-tag rounded="small" :color="CHAT_TYPE_TAG_COLOR[item?.chatType]">{{ CHAT_TYPE_MAP[item?.chatType] }}</jt-tag>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="10">
        <div class="t-top">模型热度TOP5评价情况</div>
        <div class="bar-chart">
          <div v-if="posData.length === 0 && negData.length === 0" class="no-data">暂无数据</div>
          <v-chart v-else :option="commentOption" autoresize style="height: 180px; width: 100%" />
        </div>
      </a-col>
    </a-row>
    <div class="chart-bg" @mousemove.stop="mouseMove">
      <div v-for="(item, index) in bgData" :key="index" class="item-bg" :class="{ 'show-light': isShow }" @mouseover="showWrap(item)" @mouseout="hideWrap"></div>
    </div>
    <div v-show="isShow" class="hover-box" :style="`left:${tooltipLeft}px;top:${tooltipTop}px;`">
      <div class="hover-header">
        <div class="hover-name">{{ hoverData?.serviceName }}</div>
        <div class="hover-type">
          <jt-tag rounded="small" :color="CHAT_TYPE_TAG_COLOR[hoverData?.chatType]">{{ CHAT_TYPE_MAP[hoverData?.chatType] }}</jt-tag>
        </div>
      </div>
      <div class="hover-content">
        <div class="hover-item">
          <div class="hover-label item1">模型热度</div>
          <div class="hover-num">{{ hoverData?.chatNum }}</div>
        </div>
        <div class="hover-item">
          <div class="hover-label item2">积极评价</div>
          <div class="hover-num">{{ hoverData?.positiveNum }}</div>
        </div>
        <div class="hover-item">
          <div class="hover-label item3">消极评价</div>
          <div class="hover-num">{{ hoverData?.negativeNum }}</div>
        </div>
      </div>
      <div class="hover-time">{{ curTime }}</div>
    </div>
    <div class="footer-box">
      <div>仅统计平台预置模型服务相关数据，统计截止到{{ curTime }}</div>
      <div class="footer-right">
        <div class="right right1">热度情况</div>
        <div class="right right2">积极评价</div>
        <div class="right right3">消极评价</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { watch } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
import VChart from 'vue-echarts';
import { CHAT_TYPE_MAP, CHAT_TYPE_TAG_COLOR } from '@/constants/board';
import { getHotList } from '@/apis/board';
import notification from '@/utils/notification';

use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);

const curTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
const tooltipLeft = ref(0);
const tooltipTop = ref(0);
const isShow = ref(false);
const bgData = ref([0, 1, 2, 3, 4]);
const totalData = ref([]);
const hotData = ref([]);
const posData = ref([]);
const negData = ref([]);
const nameData = ref([]);

const getHotChartOption = () => ({
  grid: {
    left: '15%',
    right: '7%',
    top: '0',
    bottom: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    inverse: true,
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  yAxis: {
    type: 'category',
    inverse: true,
    axisLabel: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
  },
  series: [
    {
      name: '占用情况',
      type: 'bar',
      data: hotData.value,
      itemStyle: {
        color: {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(250,165,72,0.48)', // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(250,165,72,1)', // 100% 处的颜色
            },
          ],
        },
        borderRadius: [100, 100, 100, 100],
      },
      barWidth: 12,
      label: {
        show: true,
        position: 'left',
        color: 'rgba(0,20,26,0.45)',
        fontSize: 14,
        distance: 12,
        offset: [0, 1],
        formatter: (params) => {
          if (!params.value) {
            return '';
          }
        },
      },
    },
  ],
});

const getCommentChartOption = () => ({
  grid: {
    left: '12%',
    right: '10%',
    top: '0',
    bottom: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  yAxis: {
    type: 'category',
    inverse: true,
    axisLabel: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
  },
  series: [
    {
      type: 'bar',
      data: negData.value,
      stack: 'Total',
      stackStrategy: 'all',
      itemStyle: {
        color: {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(247,101,77,0.6)', // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(247,101,77,1)', // 100% 处的颜色
            },
          ],
        },
        borderRadius: [0, 100, 100, 0],
      },
      barWidth: 12,
      label: {
        show: true,
        position: 'right',
        color: 'rgba(0,20,26,0.45)',
        fontSize: 14,
        distance: 12,
        offset: [0, 1],
        formatter: (params) => {
          if (!params.value) {
            return '';
          }
        },
      },
    },
    {
      type: 'bar',
      data: posData.value,
      stack: 'Total',
      itemStyle: {
        color: {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(43,180,214,1)', // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(43,180,214,0.6)', // 100% 处的颜色
            },
          ],
        },
        borderRadius: [100, 0, 0, 100],
      },
      barWidth: 12,
      label: {
        show: true,
        position: 'left',
        color: 'rgba(0,20,26,0.45)',
        fontSize: 14,
        distance: 12,
        offset: [0, 1],
        formatter: (params) => {
          if (!params.value) {
            return '';
          } else {
            return -params.value;
          }
        },
      },
    },
  ],
});

const hotOption = ref(getHotChartOption());
const commentOption = ref(getCommentChartOption());
const hoverData = ref({});

const mouseMove = ({ clientX, clientY }) => {
  if (clientX + 210 > innerWidth) {
    tooltipLeft.value = clientX - 450;
  } else {
    tooltipLeft.value = clientX - 210;
  }
  tooltipTop.value = clientY - 320;
};

const showWrap = (item) => {
  if (totalData.value.length !== 0) {
    if (Object.keys(totalData.value[item]).length) {
      isShow.value = true;
      hoverData.value = totalData.value[item];
    }
  }
};

const hideWrap = () => {
  isShow.value = false;
  hoverData.value = {};
};

const getChartData = async () => {
  const res = await getHotList({ top: 5 });
  if (res.code === 0) {
    totalData.value = res?.data || [];
    if (totalData.value) {
      if (totalData.value.length < 5) {
        const targetLength = 5;
        totalData.value = totalData.value.concat(Array.from({length: targetLength - totalData.value.length}, (v, k) => (
          // { chatNum: 0, positiveNum: 0 , negativeNum: 0, serviceName: '', chatType: null }
          { }
        )))
      }
      hotData.value = totalData.value.map((key) => {
        return key.chatNum;
      });
      posData.value = totalData.value.map((key) => {
        return -key.positiveNum;
      });
      negData.value = totalData.value.map((key) => {
        return key.negativeNum;
      });
      nameData.value = totalData.value.map((key) => {
        return {
          serviceName: key.serviceName,
          chatType: key.chatType,
        };
      });
      hotOption.value = getHotChartOption();
      commentOption.value = getCommentChartOption();
    }
  } else {
    hotData.value = [];
    posData.value = [];
    negData.value = [];
    nameData.value = [];
    notification.error({
      message: `请求出错`,
      description: res.msg,
    });
  }
};

watch(
  () => curTime.value,
  () => {
    getChartData();
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.box-container {
  margin-top: 20px;
  position: relative;
  .chart-bg {
    width: 100%;
    height: 180px;
    position: absolute;
    top: 32px;
    .item-bg {
      width: 100%;
      height: 36px;
      // &:hover {
      //   background: rgba(92, 220, 255, 0.15);
      // }
    }
    .item-bg:nth-child(odd) {
      background: rgba(0, 20, 26, 0.02);
      // &:hover {
      //   background: rgba(92, 220, 255, 0.15);
      // }
    }
    .show-light:hover {
      background: rgba(92, 220, 255, 0.15);
    }
  }
  .no-data {
    width: 100%;
    height: 180px;
    font-size: 14px;
    color: rgba(0, 20, 26, 0.45);
    text-align: center;
    line-height: 180px;
  }
  .hover-box {
    position: absolute;
    top: 32px;
    left: 45%;
    min-width: 180px;
    min-height: 167px;
    background: #fff;
    box-shadow: 0px 2px 20px 0px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    padding: 16px 12px 0;
    z-index: 1;
    .hover-header {
      display: flex;
      justify-content: space-between;
      .hover-name {
        font-size: 14px;
        color: #121f2c;
        line-height: 28px;
        margin-right: 8px;
        // white-space: nowrap;
      }
    }
    .hover-content {
      margin-top: 7px;
      .hover-item {
        display: flex;
        justify-content: space-between;
        line-height: 17px;
        margin-bottom: 12px;
        .hover-label {
          font-size: 12px;
          color: #606972;
          &::before {
            content: '';
            display: inline-block;
            width: 7px;
            height: 7px;
            border-radius: 3px;
            margin-right: 8px;
            vertical-align: middle;
          }
        }
        .item1 {
          &::before {
            background: rgba(250, 165, 72, 0.85);
          }
        }
        .item2 {
          &::before {
            background: rgba(46, 194, 231, 0.85);
          }
        }
        .item3 {
          &::before {
            background: rgba(242, 73, 67, 0.85);
          }
        }
        .hover-num {
          font-size: 14px;
          color: #121f2c;
        }
      }
    }
    .hover-time {
      font-size: 12px;
      color: #606972;
      padding: 4px 0;
      border-top: 1px solid #efefef;
    }
  }
  .t-top {
    font-size: 14px;
    color: #00141a;
    line-height: 20px;
    margin-bottom: 12px;
    text-align: center;
  }
  .main-box {
    width: 200px;
    margin: 0 auto;
    display: flex;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: rgba(0, 20, 26, 0.7);
    .name {
      width: 120px;
      text-align: right;
      // margin-left: 10%;
      padding-right: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .name-box {
    margin-top: 32px;
  }
  .bar-chart {
    width: 100%;
    height: 180px;
    position: relative;
  }
  .footer-box {
    margin: 16px 0 12px;
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: rgba(0, 20, 26, 0.45);
    line-height: 18px;
  }
  .footer-right {
    display: flex;
    color: rgba(0, 20, 26, 0.7);
    div {
      margin-left: 24px;
    }
    .right::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 3px;
      margin-right: 4px;
      vertical-align: middle;
    }
    .right1::before {
      background: rgba(250, 165, 72, 0.85);
    }
    .right2::before {
      background: rgba(43, 180, 214, 0.85);
    }
    .right3::before {
      background: rgba(247, 101, 77, 0.85);
    }
  }
}
.hover-icon {
  font-size: 14px;
  color: #c2c5cf;
  padding-left: 5px;
}
.model-tooltip-wrapper {
  .ant-tooltip-inner {
    width: 198px;
    height: unset;
    padding: 8px;
  }
}
:deep .ant-tag {
  margin-inline-end: 0;
}
// :deep .name-tooltip .ant-tooltip-inner {
//   margin-bottom: -10px;
// }
</style>
