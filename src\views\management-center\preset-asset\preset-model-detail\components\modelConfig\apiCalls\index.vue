<template>
  <div>
    <config-title :title="'API调用配置'" :open="props.info.addToOperateList"></config-title>

    <config-sub-title :title="'基础配置'"></config-sub-title>
    <div class="base-config-form">
      <a-row class="base-row-box">
        <a-col :span="24">
          <div class="info-item flex-start">
            <div class="info-left change-width">调用方式：</div>
            <div class="info-right-box" v-if="codeList.length > 0">
              <div class="sub-tab-box">
                <div v-for="item in codeList" :key="item" class="tab-item" :class="currentTab === item.title ? 'tab-active' : ''" @click="handleChange(item.title)">
                  {{ item.title }}
                </div>
              </div>
              <a-textarea v-model:value="codeStr" class="tab-content-box" disabled />
            </div>
            <div v-else>-</div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import configTitle from '../components/configTitle.vue';
import configSubTitle from '../components/configSubTitle.vue';
const props = defineProps({
  info: {
    type: Object,
    default() {
      return {};
    },
  },
});
// 支持列表展示
const codeList = ref([
  {
    title: 'Python',
    key: '1',
  },
  {
    title: 'Java',
    key: '2',
  },
  {
    title: 'Curl',
    key: '3',
  },
]);
const currentTab = ref('');
const codeStr = ref('');
const setCode = () => {
  if (currentTab.value === 'Python') {
    codeStr.value = props.info.pythonExampleCode;
  } else if (currentTab.value === 'Java') {
    codeStr.value = props.info.javaExampleCode;
  } else {
    codeStr.value = props.info.curlExampleCode;
  }
};
const handleChange = (item) => {
  currentTab.value = item;
  setCode();
};

watch(
  () => props.info,
  (val) => {
    let list = [];
    if (val.callMethod) {
      list = val.callMethod.split(',') || [];
    }
    codeList.value = codeList.value.filter((item) => list.includes(item.key));
    currentTab.value = codeList.value[0]?.title || '';
    setCode();
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.base-config-form {
  padding-bottom: 0;
  .base-row-box {
    margin-bottom: 0;
    .info-item {
      display: flex;
      align-items: center;
      .info-left {
        width: 110px;
        margin-right: 4px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 20, 26, 0.7);
        text-align: right;
      }
      .change-width {
        width: auto;
        padding-left: 12px;
      }
      .info-right-box {
        flex: 1;
        .sub-tab-box {
          display: flex;
          align-items: center;
          margin-bottom: 17px;
          .tab-item {
            height: 24px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(0, 20, 26, 0.15);
            font-weight: 400;
            font-size: 12px;
            color: rgba(0, 20, 26, 0.7);
            cursor: pointer;
            &:hover {
              color: #00a0cc;
              border-color: #00a0cc;
            }
          }
          .tab-active {
            color: #00a0cc;
            border-color: #00a0cc;
          }
        }
        .tab-content-box {
          width: calc(100% - 44px);
          height: 460px;
          padding: 20px;
          background: rgba(0, 20, 26, 0.02);
          overflow-y: auto;
          font-weight: 400;
          font-size: 14px;
          color: #00141a;
          cursor: text;
          resize: none;
        }
      }
    }
    .flex-start {
      align-items: flex-start;
    }
  }
}
</style>
