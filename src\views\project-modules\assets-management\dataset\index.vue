<template>
  <jt-container>
    <jt-iframe-container :link="getLink" />
  </jt-container>
</template>

<script setup>

const queryDataRedirectUrl = () => {
  const regex = /[?&]redirectUrl=([^]*)/;
  const redirectUrl = window.location.href.match(regex)?.[0]?.split('?redirectUrl=')?.[1] || '';
  return decodeURIComponent(redirectUrl?.split?.('&')[0]);
};

const getLink = computed(() => {
  const redirectUrl = queryDataRedirectUrl();
  if (redirectUrl) {
    return redirectUrl;
  }
  return './ds-web/#/index/data-manage/dataset-manage/my-data-set';
});

</script>

<style lang="less" scoped>
#container-wrap {
  color: @jt-text-color-primary;
}
</style>

    