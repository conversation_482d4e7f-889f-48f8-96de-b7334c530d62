<template>
  <JtConfirmModal :open="deleteVisible" :maskClosable="false" class="modal-height" :width="480" @cancel="onClose">
    <template #title>
      <div class="title">
        <jt-icon type="iconjinggao-tianchong" class="icon-warn"></jt-icon>
        <span class="title-text">确认要删除这个镜像吗？</span>
      </div>
    </template>
    <template #footer>
      <a-button type="primary" danger class="btn-delete" :loading="confirmLoading" @click="handleOk()">删除</a-button>
      <a-button type="default" @click="onClose()">取消</a-button>
    </template>
    <div class="error-text">确定删除镜像“{{ mirrorGroupInfo.imageGroupName }}”吗？删除后不可恢复，请谨慎操作</div>
  </JtConfirmModal>
</template>

<script>
import { message } from 'ant-design-vue';
import { mirrorManageApi } from '@/apis';

export default {
  name: 'DeleteMirrorGroup',
  components: {},
  props: {
    deleteVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    mirrorGroupInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['childFn'],
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      confirmLoading: false,
    };
  },
  methods: {
    onClose() {
      this.$emit('childFn');
    },
    handleOk() {
      this.confirmLoading = true;
      const sendData = {
        imageGroupId: this.mirrorGroupInfo.imageGroupId,
      };
      mirrorManageApi.canDeleteGroup(sendData).then((res) => {
        if (res.data) {
          const formData = new FormData();
          formData.append('imageGroupId', this.mirrorGroupInfo.imageGroupId);
          mirrorManageApi.deleteGroup(formData).then((res) => {
            this.confirmLoading = false;
            if (res.code == 0) {
              message.success(`镜像 ${this.mirrorGroupInfo.imageGroupName} 删除成功`);
              this.$emit('childFn', true);
            } else {
              message.error(`镜像 ${this.mirrorGroupInfo.imageGroupName} 删除失败，请稍后再试`);
            }
          });
        } else {
          this.confirmLoading = false;
          message.error(`镜像 ${this.mirrorGroupInfo.imageGroupName} 删除失败，当前镜像正在被使用，请先停止使用再删除镜像`);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  .icon-warn {
    font-size: 20px;
    color: #f53922;
    margin-right: 8px;
  }
}

.error-text {
  margin-bottom: 32px;
  font-weight: 400;
  color: rgba(0,20,26,0.7);;
}
</style>
