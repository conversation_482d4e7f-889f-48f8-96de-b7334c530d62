<template>
  <div class="table-container">
    <a-tabs v-model:active-key="activeKey" :destroy-inactive-tab-pane="true" @change="handleTabChange">
      <a-tab-pane key="projectResource" tab="项目资源使用">
        <ProjectTable :active-key="'projectResource'"></ProjectTable>
      </a-tab-pane>
      <a-tab-pane key="projectActivityResource" tab="项目活动资源使用">
        <ProjectActTable :active-key="'projectActivityResource'"></ProjectActTable>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import ProjectTable from './projectTable.vue';
import ProjectActTable from './projectActTable.vue';

const activeKey = ref('projectResource');

const handleTabChange = (tab) => {
  activeKey.value = tab;
};

// watch(
//   () => route.query.tab,
//   (newTab, oldTab) => {
//     if (newTab !== oldTab) {
//       activeKey.value = newTab || '1';
//     }
//   }
// );
// watch(
//   () => activeKey.value,
//   (newValue) => {
//     if (route.query.tab !== newValue) {
//       router.replace({ query: { tab: newValue } });
//     }
//   }
// );
</script>

<style lang="less" scoped>
.table-container {
  margin-bottom: 28px;
}
</style>