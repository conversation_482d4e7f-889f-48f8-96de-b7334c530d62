<template>
  <div>
    <div>服务调用TOP10</div>
    <empty-data v-if="!chartsData.datas.count.length" title="请选择时间点" class="empty-content"></empty-data>
    <v-chart v-else :option="chartOption" autoresize style="height: 400px; width: 100%" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
import { getFormatTime } from '@/utils';
import VChart from 'vue-echarts';
import { transferNumber } from '@/utils/number'
import emptyData from '@/components/emptyData';

use([Can<PERSON><PERSON><PERSON><PERSON>, Bar<PERSON><PERSON>, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);

const props = defineProps({
  dataSource: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  timeParamsRootRef: {
    type: [Number],
    default: 0,
  },
});

const colorMap = ['rgba(91, 143, 249, 0.85)', 'rgba(90, 216, 166, 0.85)', 'rgba(246, 189, 22, 0.85)', 'rgba(232, 104, 74, 0.85)', 'rgba(109, 200, 236, 0.85)', 'rgba(146, 112, 202, 0.85)', 'rgba(255, 157, 77, 0.85)', 'rgba(73, 183, 182, 0.85)', 'rgba(255, 153, 195, 0.85)', 'rgba(128, 170, 255, 0.85)', 'rgba(189, 210, 253, 0.85)', 'rgba(199, 225, 102, 0.85)', 'rgba(189, 239, 219, 0.85)', 'rgba(237, 116, 173, 0.85)', 'rgba(184, 196, 224, 0.85)', 'rgba(255, 185, 78, 0.85)', 'rgba(251, 229, 162, 0.85)', 'rgba(255, 134, 106, 0.83)', 'rgba(246, 195, 183, 0.85)', 'rgba(140, 220, 251, 0.85)', 'rgba(182, 227, 245, 0.85)', 'rgba(119, 114, 241, 0.85)'];

const chartsData = reactive({
  xAxis: [],
  datas: [],
});

const getXAix = () => {
  const arr = props.dataSource.map((item) => {
    return `${item.projectName}`;
  });
  return arr;
};

const getChartOption = () => {
  chartsData.xAxis = getXAix();
  chartsData.datas.count = props.dataSource.map((x) => x.count);
  chartsData.datas.itemObj = props.dataSource.map((x) => x);

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params, ticket) => {
        let rowString = ``;
        // 调试 params，检查它的结构
        params.forEach((x, i) => {
          const dataIndex = x.dataIndex;
          const dataObj = props.dataSource[dataIndex];
          let text = '';
          if (dataObj.resource.gpu === '0') {
            text = `${dataObj.resource.cpu / 1000}核CPU | ${dataObj.resource.memory / 1024}GB内存`;
          } else {
            text = `${dataObj.resource.gpu}加速卡 | ${dataObj.resource.cpu / 1000}核CPU | ${dataObj.resource.memory / 1024}GB内存 `;
          }
          rowString += `<div class="row">
                  <p style="color: #121F2C;">${dataObj.instanceName}</p>
                  <p><span class="value-name">调用量</span> <span class="value">${x.data}</span></p>
                  <p><span class="value-name">所属项目</span> <span class="value">${dataObj.projectName}</span></p>
                  <p><span class="value-name">实例数</span> <span class="value">${dataObj.replicas}</span>个</p>
                  <p><span class="value-name">单实例</span> <span class="value">${text}</span></p>
                  <p>资源配置</p>
                </div>`;
        });
        return `
          <div class="tooltip-wrap">
            <div class="tooltip-contents">
              ${rowString}
            </div>
            <div class="tooltip-footer">${getFormatTime(props.timeParamsRootRef, 'YYYY-MM-DD HH:mm')}</div>
          </div>
          `;
        // <div class="tooltip-footer">${getFormatTime(props.timeParamsRootRef, 'YYYY-MM-DD HH:mm')}</div>
      },
    },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    yAxis: {
      type: 'category',
      data: chartsData.xAxis,
      axisLabel: {
        fontSize: 12,
        color: 'rgba(0, 20, 26, 0.7)',
        width: 80,
        overflow: 'truncate',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0,20,26,0.08)',
        },
      },
    },
    series: [
      {
        name: '占用情况',
        type: 'bar',
        barWidth: 20,
        data: chartsData.datas.count,
        itemStyle: {
          // color: 'linear-gradient( 180deg, rgba(250,165,72,0.8) 0%, rgba(250,165,72,0.48) 100%);',
          color: {
            type: 'linear',
            x: 1,
            y: 0,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(250,165,72,0.8)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(250,165,72,0.48)', // 100% 处的颜色
              },
            ],
          },
        },   
        label: {
          show: true,
          position: 'right',
          color: 'rgba(0,20,26,0.7)',
          formatter: (params) => {
            return transferNumber(params.data);
          },
        },
      },
    ],
  };
};

const chartOption = ref(getChartOption());

watch(
  () => props.dataSource,
  (newValue) => {
    chartOption.value = getChartOption();
  }
);
</script>

<style lang="less" scoped>
.empty-content {
  margin-top: 60px;
}
:deep(.tooltip-wrap) {
  min-width: 200px;
  .tooltip-content {
    margin-bottom: 12px;
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .row {
    margin-bottom: 8px;
    p {
      margin-right: 16px;
      display: flex;
      align-items: center;
      justify-content: left;
      font-weight: 400;
      font-size: 12px;
      color: #606972;
      line-height: 20px;
      margin-top: 8px;
      .value-name {
        width: 48px;
        text-align: right;
      }
      .value {
        font-weight: 400;
        font-size: 14px;
        color: #121f2c;
        margin-left: 16px;
      }
    }
    span {
      font-size: 12px;
      font-weight: 400;
      color: #606972;
      line-height: 18px;
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
</style>
