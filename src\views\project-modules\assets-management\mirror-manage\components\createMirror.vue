<template>
  <a-modal :open="addVisible" :mask-closable="false" :confirm-loading="confirmLoading" ok-text="确认" class="modal-group" width="520px" @ok="handleOk" @cancel="onClose">
    <template #title>
      <div class="title-box">{{ titleText }}</div>
    </template>
    <a-form ref="ruleForm" :label-col="labelCol" :wrapper-col="wrapperCol" :model="formData" :rules="rules">
      <a-form-item v-if="!editMirrorGroup" name="imageGroupName" :validate-status="nameError" label="镜像名称" :help="helpText" class="form-name">
        <a-input v-if="!editMirrorGroup" v-model:value="formData.imageGroupName" :disabled="editMirrorGroup" :max-length="24" placeholder="请输入" autocomplete="off" />
        <span v-else>{{ formData.imageGroupName }}</span>
      </a-form-item>
      <a-form-item v-else name="imageGroupName" :validate-status="nameError" label="镜像名称">
        <span>{{ formData.imageGroupName }}</span>
      </a-form-item>
      <a-form-item name="checkedList" label="适用场景" class="scene-box">
        <a-checkbox-group v-model:value="formData.checkedList">
          <a-row>
            <a-col :span="8" style="margin-right: 40px">
              <a-checkbox :value="IMAGE_SCENE_TYPE.TRAIN" :disabled="mirrorGroupInfo?.isImageGroupUsed && [IMAGE_SCENE_TYPE.TRAIN, IMAGE_SCENE_TYPE.ALL].includes(mirrorGroupInfo?.scene)">
                <div class="box train">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN] }}</div>
              </a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox :value="IMAGE_SCENE_TYPE.REASON" :disabled="mirrorGroupInfo?.isImageGroupUsed && [IMAGE_SCENE_TYPE.REASON, IMAGE_SCENE_TYPE.ALL].includes(mirrorGroupInfo?.scene)">
                <div class="box push">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON] }}</div>
              </a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item name="architecture" label="架构">
        <a-radio-group v-model:value="formData.architecture" :options="architectureOptions" :disabled="mirrorGroupInfo?.isImageGroupUsed" @change="changeArchitecture" />
      </a-form-item>
      <a-form-item name="chipType" label="芯片类型" class="form-scene">
        <a-radio-group v-model:value="formData.chipType" :options="chipTypeOptions" :disabled="mirrorGroupInfo?.isImageGroupUsed" @change="changeChipType" />
      </a-form-item>
      <a-form-item name="description" label="描述" class="scene-box">
        <a-textarea v-model:value="formData.description" placeholder="请输入" :rows="5" />
        <div class="show-count">
          <span>{{ formData.description.split(' ').join('').length }}</span>
          / 100
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { mirrorManageApi } from '@/apis';
import { debounce } from 'lodash';
import { IMAGE_SCENE_TYPE, IMAGE_SCENE_MSG, ARCHITECTURE_TYPE, ARCHITECTURE_TYPE_MSG, CHIP_TYPE, CHIP_TYPE_MSG } from '@/constants/image';

const architectureOptions = [
  { label: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.X86], value: ARCHITECTURE_TYPE.X86 },
  { label: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.ARM], value: ARCHITECTURE_TYPE.ARM },
];

const chipTypeOptions = [
  { label: CHIP_TYPE_MSG[CHIP_TYPE.GPU], value: CHIP_TYPE.GPU },
  { label: CHIP_TYPE_MSG[CHIP_TYPE.NPU], value: CHIP_TYPE.NPU },
  { label: CHIP_TYPE_MSG[CHIP_TYPE.CPU], value: CHIP_TYPE.CPU },
];

export default {
  name: 'CreateMirrorGroup',
  components: {},
  props: {
    addVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    editMirrorGroup: {
      type: Boolean,
      default() {
        return false;
      },
    },
    mirrorGroupInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['childFn'],
  data() {
    this.validateName = debounce(this.validateName, 100);
    return {
      rules: {
        imageGroupName: [{ required: true, validator: this.validateName, trigger: ['blur', 'change'] }],
        checkedList: [{ required: true, validator: this.checkSelect, trigger: ['blur', 'change'] }],
        architecture: [{ required: true, validator: (rule, value) => {
          if (value === 99) {
            return Promise.reject("请选择架构");
          } else {
            return Promise.resolve();
          }
        }}],
        chipType: [{ required: true, validator: (rule, value) => {
          if (value === 99) {
            return Promise.reject("请选择芯片类型");
          } else {
            return Promise.resolve();
          }
        }}],
        description: [{ trigger: ['blur', 'change'], required: true, validator: this.checkDescription }],
      },
      helpText: '请输入24个字符内，以字母开头，支持英文小写a-z、数字0-9',
      titleText: '新建镜像',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      formData: {
        imageGroupName: '',
        checkedList: [IMAGE_SCENE_TYPE.TRAIN],
        architecture: ARCHITECTURE_TYPE.X86,
        chipType: CHIP_TYPE.GPU,
        description: '',
      },
      descError: false,
      confirmLoading: false,
      nameError: 'success',
      IMAGE_SCENE_TYPE, // 场景类型
      IMAGE_SCENE_MSG,
      ARCHITECTURE_TYPE, // 架构类型
      ARCHITECTURE_TYPE_MSG,
      CHIP_TYPE, // 芯片类型
      CHIP_TYPE_MSG,
      architectureOptions,
      chipTypeOptions,
    };
  },
  created() {
    if (this.editMirrorGroup) {
      this.titleText = '编辑镜像';
      const data = JSON.parse(JSON.stringify(this.mirrorGroupInfo));
      const { imageGroupName, scene, architecture, chipType, groupDescribe } = data;
      this.formData.imageGroupName = imageGroupName;
      this.formData.checkedList = scene === IMAGE_SCENE_TYPE.ALL ? [IMAGE_SCENE_TYPE.REASON, IMAGE_SCENE_TYPE.TRAIN] : [data.scene];
      this.formData.architecture = architecture;
      this.formData.chipType = chipType;
      this.formData.description = groupDescribe || '';
    }
  },
  methods: {
    async validateName(rule, value) {
      if (this.editMirrorGroup) return Promise.resolve();
      const reg = /^[a-z][a-z0-9]*$/;
      if (!value) {
        this.helpText = '请输入';
        this.nameError = 'error';
        return Promise.reject('请输入');
      } else {
        if (!reg.test(value) || value.length > 24) {
          this.helpText = '请输入24个字符内，以字母开头，支持英文小写a-z、数字0-9';
          this.nameError = 'error';
          return Promise.reject('请输入24个字符内，以字母开头，支持英文小写a-z、数字0-9');
        } else {
          const sendData = {
            imageGroupName: value,
          };
          const res = await mirrorManageApi.judgeGroupExist(sendData);
          if (res.code == 0 && res.data) {
            this.helpText = '镜像名称已存在，请重新命名';
            this.nameError = 'error';
            return Promise.reject('镜像名称已存在，请重新命名');
          }
          this.helpText = '请输入24个字符内，以字母开头，支持英文小写a-z、数字0-9';
          this.nameError = 'success';
          return Promise.resolve();
        }
      }
    },
    checkSelect(rule, value) {
      if (value.length == 0) {
        return Promise.reject('请选择');
      }
      return Promise.resolve();
    },
    checkDescription(rule, value) {
      if (value === '' || value.split(' ').join('') === '') {
        this.descError = true
        return Promise.reject('请输入镜像描述');
      } else {
        if (value.includes(' ')) {
          this.descError = true
          return Promise.reject('不允许输入空格');
        } if (value.length > 100) {
          this.descError = true;
          return Promise.reject('字符数不能超过100');
        } else {
          this.descError = false
          return Promise.resolve()
        }
      }
    },
    onClose() {
      this.$emit('childFn');
    },
    changeArchitecture(e) {
      this.formData.architecture = e.target.value;
    },
    changeChipType(e) {
      this.formData.chipType = e.target.value;
    },
    handleOk() {
      const { imageGroupName, checkedList, architecture, chipType, description } = this.formData;
      const sendData = {
        imageGroupName,
        scene: checkedList.length > 1 ? IMAGE_SCENE_TYPE.ALL : checkedList[0],
        architecture,
        chipType,
        groupDescribe: description,
      };
      if (!this.editMirrorGroup) {
        Promise.all([this.$refs.ruleForm.validate()])
          .then(() => {
            this.confirmLoading = true;
            mirrorManageApi.createGroup(sendData).then((res) => {
              this.confirmLoading = false;
              if (res.code === 0) {
                message.success(`镜像 ${imageGroupName} 新建成功`);
                this.$emit('childFn', true);
              } else {
                message.error(`镜像 ${imageGroupName} 新建失败，请稍后再试`);
              }
            });
          })
          .catch((error) => {
            throw new Error('弹窗校验失败');
          });
      } else {
        Promise.all([this.$refs.ruleForm.validate(['checkedList', 'architecture', 'chipType', 'description'])])
          .then(() => {
            this.confirmLoading = true;
            mirrorManageApi.updateGroup({ ...sendData, imageGroupId: this.mirrorGroupInfo.imageGroupId }).then((res) => {
              this.confirmLoading = false;
              if (res.code === 0) {
                message.success(`镜像 ${imageGroupName} 编辑成功`);
                this.$emit('childFn', true);
              } else {
                message.error(`镜像 ${imageGroupName} 编辑失败，请稍后再试`);
              }
            });
          })
          .catch((error) => {
            throw new Error('弹窗校验失败');
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.title-box {
  margin-bottom: 32px;
}
.form-name {
  height: 50px;
}
.item-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  .item-img {
    margin-bottom: 6px;
    width: 80px;
    height: 80px;
    img {
      width: 100%;
    }
  }
  .item-text {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.box {
  border-radius: 2px;
  width: 50px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #b7eb8f;
}
.push {
  color: #389e0d;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
}
.train {
  color: #0958d9;
  background: #e6f4ff;
  border-color: #91caff;
}
:deep(.ant-form-item .ant-form-item-label >label){
  color: #00141a;
}
:deep(.ant-radio-wrapper){
  margin-right: 40px;
}
:deep(.ant-form-item-control-input-content) {
  position: relative;
  .show-count {
    position: absolute;
    right: 12px;
    bottom: 6px;
    color: #999;
  }
}
.scene-box {
  margin-bottom: 32px;
}
</style>
