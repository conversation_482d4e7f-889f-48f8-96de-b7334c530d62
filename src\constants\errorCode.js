import { addGetKeyMethod } from '@/utils';
import $store from '@/store';

// 通用状态码
const COMMON_ERROR_CODE = {
  0: '成功',
  100001: '无访问项目权限',
  100002: '参数不合法',
  100003: '未知异常',
};

// 项目空间错误码
export const PROJECT_SPACE_ERROR_CODE = {
  130000: '参数不合法',
  130001: '项目名称不合法',
  130002: '项目描述不合法',
  130003: '成员名称不合法',
  130004: '成员角色不合法',
  130005: '资源组不合法',
  130006: '项目ID不合法',
  130101: '项目不存在',
  130102: '操作过于频繁，请稍后重试',
  130103: '拥有的项目数量超过限制',
  130104: '参与的项目数量超过限制',
  130105: '项目成员数量超过限制',
  130107: '没有权限',
  130108: '操作失败',
  130109: '用户不存在',
  130110: '非法请求',
  130111: '用户已经加入该项目',
  130121: '用户无权限创建项目',
  130233: '生成SSH密钥失败',
  130234: 'SSH密钥不存在',
  130235: '用户未绑定手机号，无法发送验证码',
  130236: '短信服务异常，发送失败',
  130237: '验证码校验服务异常',
};

addGetKeyMethod(PROJECT_SPACE_ERROR_CODE);

// 开发环境错误码
const TRAIN_DEVELOP_ERROR_CODE = {
  170001: '开发环境不存在',
  170002: '开发环境不处于运行中状态',
  170003: '开发环境不处于已停止或失败状态',
  170004: '已有同名开发环境',
  170005: '开发环境命名不符合规范',
  170006: '创建开发环境实例资源不符合规范',
  170007: '开发环境名称不能为空',
  170008: '开发环境创建失败，请稍后再试',
  170009: '开发环境运行失败，请稍后再试',
  170010: '内部错误:无法创建开发环境',
  170011: '镜像不能在当前项目中使用',
  170012: '获取镜像信息失败',
  170013: '获取镜像用户信息失败',
  170014: '目前资源组剩余可用加速卡不足，请稍后再试',
  170015: '目前资源组剩余可用CPU不足，请稍后再试',
  170016: '目前项目空间剩余可用加速卡不足，请稍后再试',
  170017: '目前项目空间剩余可用CPU不足，请稍后再试',
  170018: '开发环境运行失败，请稍后再试',
  170019: '获取资源信息失败',
  170020: '获取项目存储信息失败',
  170025: '目前资源组未关联当前项目 请修改后再试',
  170036: '开发环境运行实例数不得大于2，请修改后再试',
  170037: `所有开发环境合计可最多占用${$store.state.gpuCardMax.data}卡，超出将无法启动，请提交训练任务`, // 需要根据后端具体返回显示
  170038: '数据集挂载信息查询为空',
  170040: '数据集对应版本不存在，请修改后再试', // 需要根据后端具体返回显示
  170041: '数据集不存在，请修改后再试', // 需要根据后端具体返回显示
  170050: '数据集版本不存在或未启用，请修改后再试', // 需要根据后端具体返回显示
};

// 提示词管理错误码
const PROMPT_ERROR_CODE = {
  270000: '系统出错，请联系相应人员',
  270001: '项目ID为空',
  270002: '提示词ID为空',
  270003: '参数错误',
  270004: '非法操作，请确认',
  270005: 'prompt 模板内容字数超限',
  270006: '模板名称重复',
  270007: '模板名称错误，30字符以内，仅支持中英文数字下划线',
  270008: '标签不存在',
  270009: '模板不存在',
  270010: '模板不存在或无权限',
  270011: '目前存在模板使用该标签，无法删除',
  270012: '新增标签失败，数量达到上限',
  270013: '标签已存在',
  270014: '模型不存在',
  270015: '标签颜色不存在',
  270016: '现有标签数量已达上限50个，请删除其他标签后再试',
  270017: '预置模板不存在',
  270018: '上架状态提示词模版不支持删除，请稍后再试',
  270019: '已上架状态不支持编辑，请下架后重试',
  270020: '目前存在模板关联改标签，无法删除，请稍后再试',
  270021: '当前模板已上架',
  270022: '当前模板已下架',
};

// 训练任务错误码
const TRAIN_TASK_ERROR_CODE = {
  151001: '参数非法',
  151002: '项目下存在同名任务名称',
  151003: '项目下不存在该镜像',
  151004: '调用模板服务创建任务失败',
  151005: '任务不存在',
  151006: '当前任务状态不能进行此操作',
  151007: '镜像地址异常',
  151008: '调用资源服务获取pod信息失败',
  151009: '目前项目空间剩余可用加速卡不足，请稍后再试',
  151010: '目前项目空间剩余可用CPU不足，请稍后再试',
  151011: '目前资源组未关联当前项目 请修改后再试',
  151022: '命令长度超出限制',
  151035: '数据集对应版本不存在，请修改后再试', // 需要根据后端具体返回显示
  151040: '数据集不存在', // 需要根据后端具体返回显示
  151058: '数据集版本不存在或未启用，请修改后再试', // 需要根据后端具体返回显示
  203005: '项目空间配额不足',
};

// 镜像管理错误码
const IMAGE_ERROR_CODE = {
  12200400: '请求参数错误',
  12200401: '用户权限不足',
  12200405: '当前用户下创建项目失败',
  12200409: '重复创建',
  122000014: '镜像文件名不合法',
  12200415: '不支持请求的媒体类型',
  12200410: '删除组件失败',
  1200411: '删除项目失败',
  12200011: '镜像文件不存在',
  12200012: '镜像文件已被部署成服务',
  12100001: '镜像组已存在，不可重复创建',
  12100002: '镜像组不存在',
  12100003: '镜像组有已上传文件',
  12100004: '镜像组创建失败',
  12100005: '镜像组删除失败',
  12100006: '镜像组名不符合规则',
  12100007: '当前镜像版本已存在',
  12100021: '用户不存在',
  12200027: '此镜像已分享，不可重复分享',
  12200029: '项目空间不存在',
  12200035: '此项目镜像组镜像重复同步',
  12200037: '调用模版服务失败',
};

// 推理服务错误码
const SERVICE_ERROR_CODE = {
  180015: '目前资源组未关联当前项目，请修改后再试',
  180016: '所需CPU数量超出资源组可用配额，请修改实例数或CPU核数',
  180017: '所需加速卡卡数超出资源组可用配额，请修改实例数或加速卡卡数',
  180018: '所需CPU数量超出项目配额，请修改实例数或CPU核数',
  180019: '所需加速卡数量超出项目配额，请修改实例数或加速卡卡数',
};

// 实例错误码
const INSTANCE_ERROR_CODE = {};

// 存储目录管理错误码
const STORAGE_DIR_ERROR_CODE = {
  140008: '此项目己挂载目录，无法重复新增',
  140009: '项目空间ID不存在，请修改后再试',
  140010: '已有同名项目内部目录，请修改后再试',
  140013: '项目已关闭数据出口，不支持读写权限挂载',
  140014: '该外部存储目录无权限挂载',
};

const INCREMENTAL_PRETRAIN = {
  240017: '目前项目空间剩余可用加速卡不足，请稍后再试',
  240018: '目前项目空间剩余可用CPU不足，请稍后再试',
  240026: '目前资源组未关联当前项目，请修改后再试',
  240030: '基准模型不存在，请重新选择',
  240028: '训练集不存在，请重新选择',
  240035: '验证集不存在，请重新选择',
  240036: '目前项目空间剩余可用普通文件存储不足，请清理后再试',
  240045: '该基础模型已下架，无法运行',
};

const BIT_STATUS_ERROR_CODE = {
  240001: '无效的信息',
  240002: '参数非法',
  240003: '获取项目名称信息失败',
  240004: '训练方式参数不合法',
  240005: '任务不存在',
  240006: '当前任务状态不能进行此操作',
  240007: '调用资源服务删除资源失败',
  240008: '已有同名任务',
  240009: '获取资源组信息失败',
  240010: '获取资源信息失败',
  240011: '资源组不适用该场景',
  240012: '%s任务不存在',
  240013: '内部错误:无法创建训练任务,模板ID不存在',
  240014: '基础模型信息不存在',
  240015: '获取镜像用户信息失败',
  240016: '获取存储信息为空',
  240017: '目前项目空间可用加速卡不足，请稍后再试',
  240018: '目前项目空间剩余可用CPU不足，请稍后再试',
  240019: '新建任务失败，请稍后再试',
  240020: '发布模型失败',
  240021: '创建模型清理任务失败',
  240022: '模型正在发布，不能删除',
  240023: '任务删除失败',
  240024: '模型正在发布中',
  240025: '启动任务失败，请稍后再试',
  240026: '目前资源组未关联当前项目，请修改后再试',
  240027: '获取数据集信息失败',
  240028: '训练集%s不存在，请重新选择',
  240029: '未选择任何训练数据集',
  240030: '模型已下架或不存在',
  240031: '获取模型信息失败',
  240032: '任务命名不符合规范',
  240033: '获取数据集信息缺失',
  240034: '该模型被%s任务占用',
  240035: '验证集%s不存在，请重新选择',
  240036: '目前项目空间剩余可用普通文件存储不足，请清理后再试',
  240037: '训练集超过上限%d个，请重新选择',
  240038: '验证集超过上限%d个，请重新选择',
  240039: '目前资源组剩余可用加速卡不足，请稍后再试',
  240040: '目前资源组剩余可用CPU不足，请稍后再试',
  240041: '获取资源组资源使用信息失败',
  240042: '运行实例数无效，需在%d-%d之间',
  240043: '获取镜像信息失败',
  240044: '数据拆分比例无效',
  240045: '当前模型已下架',
  240046: '当前模型已删除',
  240047: '训练集%s已停用，请重新选择',
  240048: '验证集%s已停用，请重新选择',
  240049: '训练集%s已取消分享，请重新选择',
  240050: '验证集%s已取消分享，请重新选择',
  240051: '单实例资源配置%s无效%s',
  240052: '调用loki服务异常',
  240053: '导出%s活动明细表失败',
  240054: '训练集%s等%d个数据集版本不存在或未启用，请修改后再试',
  240055: '验证集%s等%d个数据集版本不存在或未启用，请修改后再试',
  240056: '未选择任何验证数据集',
};

// 资源池管理错误码
const RESOURCE_POOL_MANAGE_CODE = {
  201001: '参数非法',
  201002: '非法资源池id',
  202034: '非法排序字段',
  202035: '非法排序方式',
  202049: '非法资源池名称',
  202050: '资源池名称已存在',
  202051: '非法资源池描述',
  202030: '集群不存在',
  202053: '非法的资源类型',
  202007: '加速卡类型不存在',
  202054: '资源类型已存在',
  202056: '资源类型存在纳管节点',
  202058: '非法的资源池模块配置信息',
  202060: '数据出口配置不支持关闭',
  202061: '模型管理配置不支持关闭',
  202062: '文件管理配置不支持关闭',
  202063: '镜像管理配置不支持关闭',
  202064: '模型体验配置必须关闭',
  202065: '模型调优配置必须关闭',
  202066: '数据准备配置必须关闭',
  202067: '语料收集统计配置必须关闭',
  202068: '语料收集管理配置必须关闭',
  202069: '数据安全管理配置必须关闭',
  202070: '应用接入配置必须关闭',
  202071: '调用统计配置必须关闭',
  //接口定义code，message信息提示数据库操作异常，本地自定义msg
  202052: '资源池编辑失败，请稍后再试',
  202055: '资源类型新增失败，请稍后再试',
  202057: '资源类型删除失败，请稍后再试',
  202072: '功能信息编辑失败，请稍后再试',
  // 本地自定义接口响应异常码
  202503001: '资源池列表查询失败，请稍后再试',
  202503002: '资源信息查询失败，请稍后再试',
  202503003: '资源池详情查询失败，请稍后再试',
  202503004: '功能信息查询失败，请稍后再试',
  202503005: '加速卡类型查询失败，请稍后再试',
  202503006: '资源池集群信息查询失败，请稍后再试',
  202503007: '资源池文件资源配额情况查询失败，请稍后再试',
  202503008: '资源池镜像资源配额情况查询失败，请稍后再试',
  202503009: '资源类型删除失败，请稍后再试',
  202503010: '查询失败，请稍后再试',
};

//平台公告相关错误码
const ANNOUNCEMENT_ERROR_CODE = {
  250001: '变更前公告未开启',
  250002: '系统未处在变更中',
  250003: '不超过20个字符',
  250004: '不超过500个字符',
  250005: '不超过20个字符',
  250006: '不超过100个字符',
  250007: '参数错误',
  250008: '非法Pool Id',
};

// 告警管理相关错误码
const ALARM_ERROR_CODE = {
  140000: '非法参数',
  140001: '非法参数：资源池ID',
  140002: '非法参数：项目ID',
  140003: '非法参数：规则ID',
  140004: '非法参数：监控模块ID',
  140005: '非法参数：监控对象ID',
  140006: '非法参数：告警事件ID',
  140007: '非法参数：规则启用状态',
  140008: '非法参数：排序字段',
  140009: '非法参数：事件等级',
  140010: '非法参数：通知方式',
  140011: '规则名称不能为空',
  140012: '30个字符以内，必须以中英文或数字开头，支持小括号、短横线和空格',
  140013: '已有同名告警规则',
  140014: '监控对象不能为空',
  140015: '非法参数：告警单号',
  140016: '非法参数：告警事件',
  140017: '非法参数：触发方式',
  140018: '非法参数：触发模式',
  140019: '非法参数：通知状态开关',
  140020: '非法参数：通知组ID',
  140021: '非法参数：通知对象',
  140022: '该规则已经启用',
  140023: '该规则已经停用',
  140024: '通知组名称不能为空',
  140025: '30个字符内，以字母开头，支持英文小写a-z、数字0-9、下划线(_)与短横线(-)',
  140026: '已有同名告通知组',
  140027: '非法参数：通知组描述',
  140028: '通知组成员不能为空',
  140100: '操作失败',
  140101: '操作过于频繁',
  140102: '告警规则不存在',
  140103: '告警记录不存在',
  140104: '通知记录不存在',
  140105: '告警记录不存在',
  140106: '创建告警规则失败',
  140107: '通知组已关联开启状态的告警规则时不支持删除，请稍后再试',
  140108: '没有权限',
  151026: '项目id为空', // 告警管理新增专属接口，error信息共享不可移动
};

// 运营总览相关错误码
const OVERVIEW_ERROR_CODE = {
  130007: '非法参数: 资源 Id',
  130019: '非法参数: 查询开始或者结束时间',
  130018: '非法参数: Stat 类型',
  270000: '内部错误',
  270002: '无效资源池id',
  270001: '无效参数',
};

// 统一定义后端状态码对应的文案
const ERROR_CODE = {
  ...BIT_STATUS_ERROR_CODE,
  ...COMMON_ERROR_CODE,
  ...PROJECT_SPACE_ERROR_CODE,
  ...TRAIN_DEVELOP_ERROR_CODE,
  ...TRAIN_TASK_ERROR_CODE,
  ...IMAGE_ERROR_CODE,
  ...INSTANCE_ERROR_CODE,
  ...SERVICE_ERROR_CODE,
  ...STORAGE_DIR_ERROR_CODE,
  ...INCREMENTAL_PRETRAIN,
  ...PROMPT_ERROR_CODE,
  ...RESOURCE_POOL_MANAGE_CODE,
  ...ANNOUNCEMENT_ERROR_CODE,
  ...ALARM_ERROR_CODE,
  ...OVERVIEW_ERROR_CODE,
};

export default ERROR_CODE;
