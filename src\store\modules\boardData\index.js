const store = {
  state: () => ({
    detailInfo: {},
    isShow: false,
    name: ''
  }),
  mutations: {
    UPDATE_DETAILINFO(state, data) {
      state.detailInfo = data;
    },
    UPDATE_SHOW(state, data) {
      state.isShow = data;
    },
    CALL_METHOD(state, data) {
      state.name = data;
    }
  },
  actions: {
    updateDetailInfo({ commit }, data) {
      commit('UPDATE_DETAILINFO', data);
    },
    show({ commit }, data) {
      commit('UPDATE_SHOW', data);
    },
    callMethod({ commit }, data) {
      commit('CALL_METHOD', data);
    }
  },
};

export default store;
