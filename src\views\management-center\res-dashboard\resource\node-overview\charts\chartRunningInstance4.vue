<template>
  <div>
    <v-chart :option="chartOption" autoresize style="height: 400px; width: 100%" />
  </div>
</template>

<script setup>
import { LineChart } from 'echarts/charts'; // Changed to LineChart
import { GridComponent, LegendComponent, TitleComponent, TooltipComponent, DataZoomComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { ref, watch } from 'vue';
import { getPastTimeRange } from '@/utils/time';

import VChart from 'vue-echarts';

use([CanvasRenderer, LineChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent, DataZoomComponent]);

const props = defineProps({
  dataSource: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  selectedTimeType: {
    type: String,
    default: '',
  },
});

const colorMap = ['rgba(91, 143, 249, 0.85)', 'rgba(90, 216, 166, 0.85)', 'rgba(246, 189, 22, 0.85)', 'rgba(232, 104, 74, 0.85)', 'rgba(109, 200, 236, 0.85)', 'rgba(146, 112, 202, 0.85)', 'rgba(255, 157, 77, 0.85)', 'rgba(73, 183, 182, 0.85)', 'rgba(255, 153, 195, 0.85)', 'rgba(128, 170, 255, 0.85)', 'rgba(189, 210, 253, 0.85)', 'rgba(199, 225, 102, 0.85)', 'rgba(189, 239, 219, 0.85)', 'rgba(237, 116, 173, 0.85)', 'rgba(184, 196, 224, 0.85)', 'rgba(255, 185, 78, 0.85)', 'rgba(251, 229, 162, 0.85)', 'rgba(255, 134, 106, 0.83)', 'rgba(246, 195, 183, 0.85)', 'rgba(140, 220, 251, 0.85)', 'rgba(182, 227, 245, 0.85)', 'rgba(119, 114, 241, 0.85)'];

const echartsCommonOptions = {
  grid: {
    left: '5%',
    right: '5%',
    bottom: 50,
  },
};

const chartsData = reactive({
  xAxis: [],
  datas: {
    gpuUseRate: [],
    gpuMemUseRate: [],
    cpuUseRate: [],
    memUseRate: [],
  },
});
const ObjectSortArr = [
  {
    key: 'gpuUseRate',
    name: '加速卡使用率',
    show: true,
  },
  {
    key: 'gpuMemUseRate',
    name: '加速卡显存使用率',
    show: true,
  },
  {
    key: 'cpuUseRate',
    name: 'CPU使用率',
    show: true,
  },
  {
    key: 'memUseRate',
    name: '内存使用率',
    show: true,
  },
];

const getLinearColor = (index) => {
  return {
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: colorMap[index].replace('0.85)', '0.15)'), // 100% 处的颜色
      },
      {
        offset: 1,
        color: colorMap[index].replace('0.85)', '0)'), // 0% 处的颜色
      },
    ],
    global: false, // 缺省为 false
  };
};

const getChartOption = () => {
  props.dataSource.forEach(item => {
    chartsData.xAxis.push(item.formatTime)
    chartsData.datas.gpuUseRate.push(item.gpuUseRate);
    chartsData.datas.gpuMemUseRate.push(item.gpuMemUseRate);
    chartsData.datas.cpuUseRate.push(item.cpuUseRate);
    chartsData.datas.memUseRate.push(item.memUseRate);
  })
  const series = [];

  for (let i = 0; i < ObjectSortArr.length; i++) {
    const item = ObjectSortArr[i];
    const dataSeries = chartsData.datas[item.key];
    series.push({
      name: item.name,
      data: dataSeries,
      type: 'line',
      smooth: true,
      symbolSize: 10,
      showSymbol: false,
      areaStyle: { color: getLinearColor(i) },
      itemStyle: {
        color: colorMap[i],
        borderWidth: 10,
      },
      lineStyle: { color: colorMap[i] },
    });
  }

  /* 
    获取1小时的值
  */
  const getDataZoomOneHour = () => {
    let start, end;
    const totalDataPoints = props.dataSource?.length;
    let dataPointsPerHour = 60;
    if (props.selectedTimeType === 'min') {
      dataPointsPerHour = 60;
    } else if (props.selectedTimeType === 'day') {
      dataPointsPerHour = 24;
    } else if (props.selectedTimeType === 'week') {
      dataPointsPerHour = 15;
    } else if (props.selectedTimeType === 'month') {
      dataPointsPerHour = 12;
    }
    if (totalDataPoints <= dataPointsPerHour) {
      start = 0;
      end = 100;
    } else {
      start = ((totalDataPoints - dataPointsPerHour) / totalDataPoints) * 100;
      end = 100;
    }
    return { start, end };
  };

  const { start, end } = getDataZoomOneHour();

  return {
    dataZoom: [
      {
        type: 'inside',
        start,
        end,
      },
      // {
      //   type: 'slider',
      //   left: '140px',
      //   right: '140px',
      // },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {},
      },
      formatter: (params, ticket) => {
        const pastTime = getPastTimeRange(params[0].axisValue);
        let rowString = ``;
        params.forEach((x, i) => {
          const val = x.data || 0;
          const valTxt = val === 0 ? `0` : `${val}%`;
          rowString += `<div class="row">
                <p>
                    <span class="dot-common" style="background-color:${colorMap[i]}"></span>
                    <span style="display:inline-block;min-width:80px">${x.seriesName}</span>
                    <span style="margin-left:20px">${valTxt}</span>
                </p>
                </div>`;
        });
        return `
          <div class="tooltip-wrap">
            <div class="tooltip-content">
            ${rowString}
            </div>
            <div class="tooltip-footer">${pastTime[props.selectedTimeType]}</div>
          </div>
        `;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'value',
      name: '使用率（%）',
    },
    xAxis: {
      type: 'category',
      data: chartsData.xAxis,
    },
    legend: {
      left: 'right',
      data: (() =>
        series.map((x) => {
        if (ObjectSortArr.find((y) => y.name === x.name)?.show) {
          return x.name;
        }
      }))(),
      itemHeight: 8,
    },
    series,
    ...echartsCommonOptions,
  };
};

const chartOption = ref(getChartOption());
watch(
  () => props.dataSource,
  (newValue) => {
    chartOption.value = getChartOption();
  }
);
</script>

<style lang="less" scoped>
:deep(.tooltip-wrap) {
  min-width: 200px;
  .tooltip-content {
    margin-bottom: 12px;
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .row {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    margin-bottom: 8px;
    p {
      margin-right: 60px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #121f2c;
      line-height: 20px;
    }
    span {
      font-size: 12px;
      font-weight: 400;
      color: #606972;
      line-height: 18px;
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
</style>
