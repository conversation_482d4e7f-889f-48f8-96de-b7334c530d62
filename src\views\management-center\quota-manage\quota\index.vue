<template>
  <div class="wrapper-box">
    <section class="content-box">
      <guide />
      <div class="content-wrap-box">
        <a-tabs v-model:active-key="activeKey" :destroy-inactive-tab-pane="true" @change="handleTabChange">
          <template #rightExtra>
            <a-space>
              <jt-reload-icon-btn @click="handleReload" />
              <a-input v-model:value="searchText" class="input" :class="{ 'cre-pro-input': activeKey === QUOTA_MANAGE_TABS.CREATE_PROJECT }" :placeholder="searchPlaceholder" allow-clear @change="handleInput">
                <template #prefix>
                  <jt-icon type="iconsousuo" class="search-icon" style="color: #bec2c5"></jt-icon>
                </template>
              </a-input>
              <a-button v-if="activeKey === QUOTA_MANAGE_TABS.CREATE_QUOTA && manageEditPermission" class="kl-create-btn" type="primary" @click="handleCreate"><PlusOutlined /> 新增用户配额</a-button>
              <a-button v-if="activeKey === QUOTA_MANAGE_TABS.CREATE_PROJECT && apvEditPermission" danger :disabled="!selectRows.length" @click="open(1)">驳回</a-button>
              <a-button v-if="activeKey === QUOTA_MANAGE_TABS.CREATE_PROJECT && apvEditPermission" type="primary" ghost :disabled="!selectRows.length" @click="open(0)">通过</a-button>
            </a-space>
          </template>

          <a-tab-pane v-if="expansionPermission" :key="QUOTA_MANAGE_TABS.EXPANSION" tab="项目扩容申请">
            <expansion-list ref="expansionListRef" :search-text="searchText" :reload="reloadTable"></expansion-list>
          </a-tab-pane>
          <a-tab-pane v-if="managePermission" :key="QUOTA_MANAGE_TABS.QUOTA" tab="项目配额管理">
            <quota-list ref="quotaListRef" :search-text="searchText" :reload="reloadTable"></quota-list>
          </a-tab-pane>
          <a-tab-pane v-if="expansionPermission" :key="QUOTA_MANAGE_TABS.CREATE_PROJECT" tab="用户新建项目申请">
            <create-list ref="projectRef" v-model:selectRows="selectRows" :active-key="QUOTA_MANAGE_TABS.CREATE_PROJECT" :search-text="searchText" :reload="reloadTable"></create-list>
          </a-tab-pane>
          <a-tab-pane v-if="managePermission" :key="QUOTA_MANAGE_TABS.CREATE_QUOTA" tab="用户新建项目配额管理">
            <create-list :active-key="QUOTA_MANAGE_TABS.CREATE_QUOTA" :reload="reloadTable" :search-text="searchText"></create-list>
          </a-tab-pane>
        </a-tabs>
      </div>
      <change-quota-drawer v-model:open="createQuotaDrawerVisible" type="create" :active-key="activeKey" @close="handleDrawerClose" />
    </section>
  </div>
</template>

<script setup>
import debounce from 'lodash/debounce';

import { computed, ref, watch } from 'vue';
import { QUOTA_MANAGE_SEARCH_PLACEHOLDER, QUOTA_MANAGE_TABS, APV_QUATO_VIEW_AUTH, MGT_QUATO_VIEW_AUTH, MGT_QUATO_EDIT_AUTH, APV_QUATO_EDIT_AUTH } from '@/constants/quota.js';
import { useRoute, useRouter } from 'vue-router';
import expansionList from './expansionList/expansionList.vue';
import quotaList from './quotaList.vue';
import Guide from '../guide/index.vue';
import CreateList from '../components/createList/index.vue';
import ChangeQuotaDrawer from '../components/changeQuotaDrawer/index.vue';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';
import { PlusOutlined } from '@ant-design/icons-vue';

const expansionPermission = checkPermissionByApvAndMgtItem(APV_QUATO_VIEW_AUTH);
const managePermission = checkPermissionByApvAndMgtItem(MGT_QUATO_VIEW_AUTH);
const manageEditPermission = checkPermissionByApvAndMgtItem(MGT_QUATO_EDIT_AUTH);
const apvEditPermission = checkPermissionByApvAndMgtItem(APV_QUATO_EDIT_AUTH);

const router = useRouter();
const route = useRoute();
const { EXPANSION, QUOTA } = QUOTA_MANAGE_TABS;
const { tab } = route.query;

const searchText = ref(route.query.searchText || '');

const expansionListRef = ref(null);
const quotaListRef = ref(null);
const projectRef = ref(null);

const selectRows = ref([]);

const handleTabChange = (tab) => {
  searchText.value = '';
  route.query.statuses = undefined;
  route.query.pageNum = 1;
  route.query.pageSize = 10;
  route.query.sortField = undefined;
  route.query.isAsc = undefined;
  router.replace({ query: { tab: tab } });
};

const initActiveKey = () => {
  if (tab) {
    return tab;
  }
  if (expansionPermission) {
    return EXPANSION;
  }
  if (managePermission) {
    return QUOTA;
  }
};

const createQuotaDrawerVisible = ref(false);
const handleCreate = () => {
  createQuotaDrawerVisible.value = true;
};

const activeKey = ref(initActiveKey());
const searchPlaceholder = computed(() => QUOTA_MANAGE_SEARCH_PLACEHOLDER[activeKey.value]);
const handleInput = debounce((event) => {
  searchText.value = event.target._value;
}, 400);

const reloadTable = ref(false);
const handleReload = () => {
  reloadTable.value = !reloadTable.value;
};

const handleDrawerClose = (reload) => {
  if (reload) {
    handleReload();
  }
};

const open = (val) => {
  projectRef.value.openDrawer(val);
};

</script>

<style lang="less" scoped>
.wrapper-box {
  .content-box {
    padding: 20px;
    .content-wrap-box {
      position: relative;
      margin-bottom: 20px;
      padding: 20px;
      padding-bottom: 48px;
      background-color: #fff;
      min-height: ~'calc(100% - 20px)';
      border-radius: 4px;
    }
  }
}
:deep(.ant-tabs-top-bar) {
  border: 0;
  margin-bottom: 16px;
}
:deep(.ant-tabs .ant-tabs-large-bar .ant-tabs-tab) {
  height: 33px;
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  padding: 0 0 10px 0;
}

:deep(.ant-tabs-extra-content) {
  height: 33px;
  display: flex;
  align-items: center;

  .reload-icon {
    font-size: 18px;
    margin-right: 10px;
    cursor: pointer;
  }

  .input {
    width: 240px;
    height: 32px;
    border-radius: 2px;
    font-size: 14px;

    .search-icon {
      color: #bec2c5;
      position: relative;
      top: 0px;
  }
}

  .cre-pro-input {
    width: 290px;
  }

  .add {
    height: 32px;
    background: @jt-primary-color;
    border-radius: 2px;
    font-size: 14px;
    color: #fff;
    margin-left: 12px;
    display: flex;
    align-items: center;

    &-icon {
      color: #fff;
      margin-right: 4px;
    }
  }
}
.tab-bar {
  margin-right: 6px;
}
:deep .ant-btn-primary.ant-btn-background-ghost:disabled, .ant-btn-default.ant-btn-dangerous:disabled {
  background: rgba(0,20,26,0.04);
  border: 1px solid rgba(0,20,26,0.15);
  color: rgba(0,20,26,0.25);
}
</style>
