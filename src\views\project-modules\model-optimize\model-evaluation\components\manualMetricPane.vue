<template>
  <div class="manual-metric-pane">
    <a-table :dataSource="$attrs.value" :columns="columns" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'id'">{{ index + 1}}</template>
        <template v-if="column.dataIndex === 'name'">
          <div v-if="index === 0">{{ record.name }}</div>
          <a-form-item
            v-else
            :name="['manualMetric', index, 'name']"
            :rules="[{ required: true, message: '请输入评估维度' }]"
          >
            <a-select
              v-model:value="$attrs.value[`${index}`]['name']"
              placeholder="请输入评估维度"
              style="width: 300px"
            >
              <template #dropdownRender="{ menuNode: menu }">
                <v-nodes :vnodes="menu" />
                <a-divider style="margin: 4px 0" />
                <div class="input-box">
                  <a-input
                    class="input-item"
                    :class="(name.trim() && tipTextError) ? 'input-error' : ''"
                    ref="inputRef"
                    v-model:value="name"
                    placeholder="中英文下划线，1-10个字符"
                  />
                  <a-button
                    type="primary"
                    ghost
                    @click="onAddItem"
                    :disabled="!name.trim() || tipTextError"
                  >
                    <template #icon>
                      <plus-outlined />
                    </template>
                    添加
                  </a-button>
                </div>
                <div
                  class="tip-text"
                  :class="(name.trim() && tipTextError) ? 'tip-text-error' : ''"
                >{{ (name.trim() && tipTextError) ? tipTextError : '' }}</div>
              </template>
              <a-select-option
                type="primary"
                class="option"
                v-for="(item, index) in items"
                :key="index"
                :value="item"
                :disabled="$attrs.value.find(i=> i.name === item)"
              >
                {{ item }}
                <jt-icon
                  v-if="showDelete(item)"
                  type="iconshanchu1"
                  class="delete"
                  @click.stop="onDeleteItem(item)"
                />
              </a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <template v-if="column.dataIndex === 'des'">
          <a-form-item
            :name="['manualMetric', index, 'desc']"
            :rules="[{ required: true, message: '请输入评估维度备注' }]"
          >
            <ev-textarea
              :rows="1"
              placeholder="请输入评估维度备注"
              v-model:value="$attrs.value[index]['desc']"
              :maxlength="300"
            />
          </a-form-item>
        </template>
        <template v-if="column.dataIndex === 'options'">
          <a-button
            ghost
            class="table-button"
            type="link"
            :disabled="index === 0"
            @click="()=> onDelete(index)"
          >删除</a-button>
        </template>
      </template>
    </a-table>
    <a-tooltip v-if="$attrs.value.length === 5" placement="top" title="最多添加5个评估维度">
      <a-button class="add-button" size="small" ghost type="link" :disabled="true">
        <PlusOutlined class="button-icon" />新建评估维度
      </a-button>
    </a-tooltip>
    <a-button class="add-button" size="small" @click="onAdd" ghost type="link" v-else>
      <PlusOutlined class="button-icon" />新建评估维度
    </a-button>
  </div>
</template>

<script setup lang="jsx">
import EvTextarea from '@/views/project-modules/model-optimize/model-evaluation/components/textArea.vue';
import { PlusOutlined } from '@ant-design/icons-vue';
const emits = defineEmits(['delete', 'add']);
const name = ref('');
const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});
const presetItems = ['满意度','真实性','安全性','有用性','逻辑性']
const items = ref(presetItems)
const columns = [
  {
    title: '序号',
    dataIndex: 'id',
    width: '10%',
  },
  {
    title: '评估维度',
    dataIndex: 'name',
    width: '20%',
  },
  {
    title: '评估维度备注',
    dataIndex: 'des',
  },
  {
    title: '操作',
    dataIndex: 'options',
    width: '100px',
  },
];
const showDelete = (item) => {
    if (presetItems.includes(item)) {
        return false
    }

    return true
}
const tipTextError = computed(() => {
    const reg = /^[a-zA-Z_\u4e00-\u9fa5]{1,10}$/;

    if (!reg.test(name.value)) {
        return '中英文下划线，1-10个字符'
    }
    
    if (items.value.includes(name.value)) {
        return '已包含，请不要重复'
    }
    return false
})
const onDelete = (index) => {
    emits('delete', index)
}
const onAdd = () => {
     emits('add')
}
const onAddItem = () => {
    items.value = [...items.value, name.value]
    name.value = '';
}
const onDeleteItem = (item) => {
  items.value = items.value.filter(i => item !== i);
};
</script>

<style lang="less" scoped>
.manual-metric-pane {
  width: 100%;
}
.option {
  position: relative;
  &:hover {
    color: #00a0cc;
  }
  .delete {
    position: absolute;
    top: 10px;
    right: 14px;
    color: rgba(0, 20, 26, 0.45);
    display: none;
    &:hover {
      color: #00a0cc;
    }
  }
  &:hover {
    .delete {
      display: block;
    }
  }
}
.input-box {
  padding: 4px 10px;
  display: flex;
  align-items: center;
}
.input-item {
  width: 288px;
  margin-right: 8px;
}
.tip-text {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  padding-left: 10px;
  padding-bottom: 8px;
}
.tip-text-error {
  color: #fe3a47;
}
.input-error {
  border-color: #ff6369;
}

.add-button {
  margin-top: 9px;
}
.table-button {
  height: auto;
  padding: 0;
}
:deep(.textarea-box) {
  padding-right: 50px;
}
:deep(.ant-input.input-error:focus) {
  box-shadow: none;
}
:deep(.ant-form-item) {
  margin-bottom: 0px;
}
</style>