<!-- 数据增强-任务总结 -->
<template>
  <div class="task-message jt-bdr-4">
    <div class="config-title">任务总结</div>
    <div v-if="configData?.status && configData?.status !== 6">
      <!-- 增强类型：多模态增强，增强方式：大模型增强，显示图片、文本 -->
      <div v-if="configData?.type === 'multimodal' && configData?.enhance_mode === 'large_model_enhance'">
        <Enhance :columns="multiColumns" :configData="configData">
          <div class="config-description-item">
            <div style="min-width: 100px; text-align: right">Prompt：</div>
            <span class="right-text">{{ configData?.image_model_enhance_config.prompt_template || '--' }}</span>
          </div>
          <div class="config-description-item">
            <div style="width: 100px; text-align: right">生成样本：</div>
            <span class="right-text">（抽样显示100条）</span>
          </div>
        </Enhance>
      </div>
      <!-- 增强类型：文本增强，增强方式：问答对抽取，显示问题、答案 -->
      <div v-else-if="configData?.enhance_mode === 'prompt_response_pairs_extract'">
        <Enhance :columns="extractColumns" :configData="configData">
          <div class="config-description-item">
            <div style="width: 100px; text-align: right">生成样本：</div>
            <span class="right-text">（抽样显示100条）</span>
          </div>
        </Enhance>
      </div>
      <div v-else>
        <Enhance :columns="columns" :configData="configData">
          <a-row>
            <a-col :span="9">
              <div class="config-description-item">
                <div style="min-width: 100px; text-align: right">增强算子：</div>
                <a-tooltip>
                  <template #title>{{ getArrName(configData) }}</template>
                  <span class="right-text">{{ getArrName(configData) }}</span>
                </a-tooltip>
              </div>
              <div class="config-description-item">
                <div style="width: 100px; text-align: right">增强效果对比：</div>
                <span class="right-text">抽样显示100条样本的增强效果对比</span>
              </div>
            </a-col>
            <a-col :span="15">
              <div class="config-description-item">
                <span>生成样本数：</span>
                <span class="right-text">{{ count }}</span>
              </div>
            </a-col>
          </a-row>
        </Enhance>
      </div>
    </div>
    <jt-empty v-else :show-operation="true" :title="configData?.status === 6 ? '数据，增强完成后显示任务详情' : '数据'"></jt-empty>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import Enhance from './enhance.vue';
import LightEmpty from './light-empty.vue';
import { getResultList, getFileServiceUrl, getFileContent, getMultiContent, downloadImage } from '@/apis/activityManage';
import { useStore } from 'vuex';

const store = useStore();

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  configData: {
    type: Object,
    default: () => {
      return {
        enhance_oprs: [],
      };
    },
  },
});

const loading = ref(false);

const taskId = ref(null);
const count = ref(0);

const dataSource = ref([]);

// 多模态列表
const multiColumns = [
  {
    title: '序号',
    customRender: ({ text, record, index }) => index + 1,
    width: 80,
  },
  {
    title: '图片',
    dataIndex: 'image',
    key: 'image',
  },
  {
    title: '文本',
    dataIndex: 'text',
    key: 'text',
    ellipsis: true,
  },
];

// 问答对抽取
const extractColumns = [
  {
    title: '序号',
    customRender: ({ text, record, index }) => index + 1,
    width: 80,
  },
  {
    title: '问题',
    dataIndex: 'image',
    key: 'image',
  },
  {
    title: '答案',
    dataIndex: 'text',
    key: 'text',
    ellipsis: true,
  },
];

const columns = [
  {
    title: '序号',
    customRender: ({ text, record, index }) => index + 1,
    width: 80,
  },
  {
    title: '增强前',
    dataIndex: 'before_sample',
    key: 'before_sample',
    ellipsis: true,
  },
  {
    title: '增强后',
    dataIndex: 'after_sample',
    key: 'after_sample',
    ellipsis: true,
  },
];

const getDataList = async () => {
  loading.value = true;
  const { pageSize, pageNum } = paginationOptions.value;
  const formData = new FormData();
  formData.append('filePath', props.configData?.diff_path);

  const resUrl = await getFileServiceUrl({}, { projectId: props.configData?.projectId, poolId: store.state.poolInfo.id });
  const concatUrl = resUrl?.data.replace('/fileList', '/dataset/fileContent'); 
  const requestUrl = concatUrl.replace('https://uatpm1.jiutian.10086.cn/kunlun', 'uatpm1');

  const res = await getFileContent(requestUrl, formData, { projectId: props.configData?.projectId, poolId: store.state.poolInfo.id, 'Content-Type': 'multipart/form-data' });
  // const res = await getResultList(taskId.value, { pageSize, pageNum });
  if (res.resultCode === '200') {
    const body = JSON.parse(res?.body);
    count.value = body?.post_count || 0;
    body?.samples_list.forEach((item) => {
      item.before_sample = JSON.parse(item.before_sample);
      item.after_sample = JSON.parse(item.after_sample);
      dataSource.value.push(item);
    });
    // paginationOptions.value.total = res.data.total;
  }
  loading.value = false;
};

// watch(
//   () => props.id,
//   (val) => {
//     taskId.value = val;
//   },
//   {
//     immediate: true,
//   }
// );

// watch(
//   () => props.configData,
//   (val) => {
//     if (val?.status && val?.status !== 6) {
//       getDataList();
//     }
//   },
//   {
//     immediate: true,
//     deep: true,
//   }
// );

const getArrName = (obj) => {
  if (!obj?.enhance_oprs) {
    return '--';
  } else if (obj?.enhance_oprs.length < 2) {
    return obj.enhance_oprs[0].operator_name;
  } else {
    const nameArr = obj.enhance_oprs.map((el) => el.operator_name);
    return nameArr.join('、');
  }
};
</script>

<style lang="less" scoped>
.task-message {
  background: white;
  padding: 24px 20px 32px;
  margin-bottom: 20px;
}
.config-title {
  font-weight: 600;
  font-size: 16px;
  color: #00141a;
  line-height: 22px;
  margin-bottom: 20px;
}
.config-description-item {
  display: flex;
  margin-bottom: 16px;
  .right-text {
    color: #00141a;
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
