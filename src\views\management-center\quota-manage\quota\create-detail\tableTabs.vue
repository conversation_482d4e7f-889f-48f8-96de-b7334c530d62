<template>
  <div class="quato-table-tabs">
    <a-tabs v-model:active-key="activeKey" destroy-inactive-tab-pane>
      <a-tab-pane key="1" tab="用户新建项目情况">
        <create-project-record-table/>
      </a-tab-pane>
      <a-tab-pane key="2" tab="配额变更记录">
        <quota-record-table :reload="reload"/>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import CreateProjectRecordTable from './createProjectRecordTable.vue';
import QuotaRecordTable from './quotaRecordTable.vue';

const activeKey = ref('1');
defineProps({
  reload: {
    type: Boolean,
  }
})

</script>

<style lang="less" scoped>

</style>