<template>
  <div>
    <a-config-provider>
      <template #renderEmpty>
        <jt-empty :show-operation="!keyword" title="分享内容" />
      </template>
      <a-table
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          getCheckboxProps: (record) => ({
            disabled: record.disabled,
          }),
          onChange: onSelectChange,
        }"
        row-key="id"
        :data-source="dataSource"
        :columns="columns"
        :pagination="false"
        :loading="tableLoading"
        @change="onTableChange"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key === 'projectName'">
            <a-tooltip>
              <template #title>
                {{ text || '项目已删除' }}
              </template>
              <span class="text-ellipsis" style="max-width: 126px">{{ text || '项目已删除' }}</span>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'sharedProjectId'">
            <a-tooltip>
              <template #title>
                {{ text }}
              </template>
              <span class="text-ellipsis max-width-100">{{ text }}</span>
            </a-tooltip>
          </template>

          <template v-if="column.key === 'sourceObjectName'">
            <a-flex :gap="8" align="center">
              <img :src="record.dir ? folderImg : fileImg" class="file-type-img" alt="" />
              <a-tooltip>
                <template #title>
                  {{ record.sourceObjectName }}
                </template>
                <span class="width-400">{{ record.name }}</span>
              </a-tooltip>
            </a-flex>
          </template>

          <template v-if="column.key === 'shareTime'">
            {{ getFormatTime(text) }}
          </template>

          <template v-if="column.key === 'authority'">
            <jt-tag :color="AUTHORITY_COLORS[text]" :rounded="null">{{ AUTHORITY_TEXTS[text] }}</jt-tag>
          </template>
          <template v-if="column.key === 'status'">
            <jt-tag :color="SHARE_STATUS_COLORS[text]">{{ SHARE_STATUS_TEXTS[text] }}</jt-tag>
          </template>
          <template v-if="column.key === 'expireTime'">
            {{ text || '永久有效' }}
          </template>
        </template>
      </a-table>
    </a-config-provider>
    <a-flex>
      <a-space v-if="dataSource?.length > 0" class="operation-btn-container">
        <a-button danger :disabled="!selectedRowKeys.length" @click="handleBatchCancel">取消分享</a-button>
      </a-space>
      <jt-pagination :total="total" :page-num="pagination.pageNum" :page-size="pagination.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
    </a-flex>
    <cancel-share-dlg v-model:visible="deleteDlgVisible" :delete-items="deleteItems" @delete-success="deleteSuccess" />
  </div>
</template>

<script setup>
import { mirrorManageApi, storageApi } from '@/apis';
import { requestWithProjectId } from '@/request';
import { MODEL_SOURCE_ENUM } from '@/views/project-modules/assets-management/model-manage/util';
import { onMounted, reactive, ref, watch, computed } from 'vue';
import cancelShareDlg from './cancelShareDlg.vue';
import { dataToSizeConversion } from '@/utils/storage';
import { message } from 'ant-design-vue';
import folderImg from '@/assets/images/file_type_folder.png';
import fileImg from '@/assets/images/file_type_file.png';
import { AUTHORITY_COLORS, AUTHORITY_TEXTS, SHARE_STATUS_TEXTS, SHARE_STATUS_COLORS } from './const';
import { getFormatTime } from '@/utils';
import { useListStateMemory } from '@/hooks/useListStateMemory.js';

const emits = defineEmits(['selectNode', 'selectChange', 'keyword-change']);

const props = defineProps({
  bucket: {
    type: String,
    required: true,
  },
  // currentNode: {
  //   type: Object,
  //   default: () => {},
  // },
  bucketInfo: {
    type: Object,
    default: () => {},
  },
});

// 基础列配置
const baseColumns = [
  {
    title: '项目空间名称',
    dataIndex: 'projectName',
    key: 'projectName',
    maxWidth: 100,
  },
  {
    title: '项目空间ID',
    dataIndex: 'sharedProjectId',
    key: 'sharedProjectId',
    maxWidth: 100,
  },
  {
    title: '读写权限',
    dataIndex: 'authority',
    key: 'authority',
  },
  {
    title: '分享文件',
    dataIndex: 'sourceObjectName',
    key: 'sourceObjectName',
  },
  {
    title: '分享时间',
    dataIndex: 'shareTime',
    key: 'shareTime',
    width: 180,
    sorter: true,
  },
  {
    title: '分享状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '失效时间',
    dataIndex: 'expireTime',
    key: 'expireTime',
    width: 180,
  },
];

// 计算属性：将状态应用到表格列配置
const columns = computed(() => {
  return baseColumns.map((column) => {
    const newColumn = { ...column };

    // 应用排序状态
    if (sortKey.value && sortKey.value === column.key) {
      newColumn.sortOrder = sort.value === 'asc' ? 'ascend' : 'descend';
    }

    return newColumn;
  });
});

const tableLoading = ref(false);
const dataSource = ref([]);
const total = ref(0);

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
});
const sortKey = ref('');
const sort = ref('');
const keyword = ref('');

// 使用状态记忆管理分页、排序和搜索状态
const { initFromQuery } = useListStateMemory(
  {
    pagination,
    sortKey,
    sort,
    keyword,
  },
  {
    prefix: 'sharedList',
  }
);

const changePageSize = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.pageNum = 1;
  getDataSource();
};
const changePageNum = (pageNum) => {
  pagination.pageNum = pageNum;
  getDataSource();
};

watch(dataSource, () => {
  selectedRowKeys.value = [];
});

const getDataSource = async () => {
  tableLoading.value = true;
  const params = {
    sourceObjectName: keyword.value,
    page: pagination.pageNum,
    pageSize: pagination.pageSize,
    sortKey: sortKey.value,
    sort: sort.value,
  };
  const res = await requestWithProjectId.POST('/web/storage/v1/objectStor/shareList', params);
  if (res.code === 0) {
    dataSource.value = res.data.objectSharedInfoList.map((item) => {
      return {
        ...item,
        key: item.sourceObjectName,
        name: getFileName(item.sourceObjectName),
        dir: item.sourceObjectName.endsWith('/*'),
      };
    });
    total.value = res.data.total;
  }
  tableLoading.value = false;
};

// 搜索功能
const search = (searchKeyword) => {
  keyword.value = searchKeyword;
  emits('keyword-change', searchKeyword);
  pagination.pageNum = 1;
  getDataSource();
};

// 重新加载
const reload = () => {
  getDataSource();
};

// 获取当前keyword
const getCurrentKeyword = () => {
  return keyword.value;
};

// 监听keyword变化并emit
watch(
  keyword,
  (newValue) => {
    emits('keyword-change', newValue);
  },
  { immediate: true }
);

const selectedRowKeys = ref([]);
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
  const selectedItems = dataSource.value.filter((item) => keys.includes(item.id));
  emits('selectChange', keys, selectedItems);
};

const deleteItems = ref([]);
const deleteDlgVisible = ref(false);
const handleBatchCancel = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要取消分享的文件');
    return;
  }
  deleteDlgVisible.value = true;
  deleteItems.value = dataSource.value.filter((item) => selectedRowKeys.value.includes(item.id));
};

const onTableChange = (pagination, filters, sorter) => {
  if (sorter.order) {
    sortKey.value = sorter.field;
    sort.value = sorter.order === 'ascend' ? 'asc' : 'descend';
  } else {
    sortKey.value = '';
    sort.value = '';
  }
  getDataSource();
};

const deleteSuccess = () => {
  pagination.pageNum = 1;
  getDataSource();
};

function getFileName(objectName) {
  if (objectName.includes('/')) {
    if (objectName.endsWith('/*')) {
      return objectName.split('/')[objectName.split('/').length - 2];
    }
    return objectName.split('/')[objectName.split('/').length - 1];
  }
  return objectName;
}

onMounted(() => {
  // 先从query恢复状态
  initFromQuery();
  // 然后获取数据
  getDataSource();
});

defineExpose({
  search,
  reload,
  getCurrentKeyword,
});
</script>

<style lang="less" scoped>
.operation-bar {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.up-dir-btn {
  width: 24px;
  height: 24px;
  padding: 0;
}
.operation-btn-container {
  padding-top: 20px;
  margin-right: 8px;
}
.pointer {
  cursor: pointer;
}
.width-400 {
  max-width: 400px;
}
.max-width-100 {
  max-width: 100px;
}
.file-type-img {
  width: 28px;
}
:deep(td) {
  vertical-align: middle;
}
</style>
