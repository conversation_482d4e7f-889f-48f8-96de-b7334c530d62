<template>
  <div>
    <div>服务调用量</div>
    <v-chart :option="chartOption" autoresize style="height: 400px; width: 100%" @mouseover="handleHighlight" />
  </div>
</template>

<script setup>
import { LineChart } from 'echarts/charts'; // Changed to LineChart
import { GridComponent, LegendComponent, TitleComponent, TooltipComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { ref, watch } from 'vue';
import { getFormatTime } from '@/utils';
import VChart from 'vue-echarts';

use([CanvasRenderer, LineChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);

const props = defineProps({
  dataSource: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  selectedType: {
    type: String,
    default: '',
  },
});

const timeParamsRef = ref(null);

const emit = defineEmits(['doSth']);

const colorMap = ['rgba(43,180,214, 0.85)', 'rgba(90, 216, 166, 0.85)', 'rgba(246, 189, 22, 0.85)', 'rgba(232, 104, 74, 0.85)', 'rgba(109, 200, 236, 0.85)', 'rgba(146, 112, 202, 0.85)', 'rgba(255, 157, 77, 0.85)', 'rgba(73, 183, 182, 0.85)', 'rgba(255, 153, 195, 0.85)', 'rgba(128, 170, 255, 0.85)', 'rgba(189, 210, 253, 0.85)', 'rgba(199, 225, 102, 0.85)', 'rgba(189, 239, 219, 0.85)', 'rgba(237, 116, 173, 0.85)', 'rgba(184, 196, 224, 0.85)', 'rgba(255, 185, 78, 0.85)', 'rgba(251, 229, 162, 0.85)', 'rgba(255, 134, 106, 0.83)', 'rgba(246, 195, 183, 0.85)', 'rgba(140, 220, 251, 0.85)', 'rgba(182, 227, 245, 0.85)', 'rgba(119, 114, 241, 0.85)'];

const echartsCommonOptions = {
  grid: {
    left: '8%',
    right: '5%',
    bottom: 80,
  },
};

const chartsData = reactive({
  xAxis: [],
  datas: {
    count: [],
  },
});
const ObjectSortArr = [
  {
    key: 'count',
    name: '服务调用量',
    show: true,
  },
];

const getLinearColor = (index) => {
  return {
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: colorMap[index].replace('0.85)', '0.15)'), // 100% 处的颜色
      },
      {
        offset: 1,
        color: colorMap[index].replace('0.85)', '0)'), // 0% 处的颜色
      },
    ],
    global: false, // 缺省为 false
  };
};

const getXAix = () => {
  if (props.selectedType === 'month') {
    return props.dataSource.map((x) => getFormatTime(x.formateTime, 'YYYY-MM-DD'));
  } else if (props.selectedType === 'week') {
    return props.dataSource.map((x) => getFormatTime(x.formateTime, 'YYYY-MM-DD'));
  } else {
    return props.dataSource.map((x) => getFormatTime(x.formateTime, 'YYYY-MM-DD HH:mm'));
  }
};

const getChartOption = () => {
  chartsData.xAxis = getXAix();
  chartsData.datas.count = props.dataSource.map((x) => x.count);
  const series = [];

  for (let i = 0; i < ObjectSortArr.length; i++) {
    const item = ObjectSortArr[i];
    const dataSeries = chartsData.datas[item.key];
    series.push({
      name: item.name,
      data: dataSeries,
      type: 'line',
      symbolSize: 10,
      showSymbol: false,
      smooth: true,
      areaStyle: { color: getLinearColor(i) },
      itemStyle: {
        color: colorMap[i],
        borderWidth: 10,
      },
      lineStyle: { color: colorMap[i] },
    });
  }

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params, ticket) => {
        let rowString = ``;
        params.forEach((x, i) => {
          rowString += `<div class="row">
                  <p>
                    <span class="dot-common" style="background-color:${colorMap[i]}"></span>
                    <span style="margin-right:6px">${x.seriesName}</span>
                    <span style="display:inline-block;min-width:50px">${x.data}</span>
                  </p>
                </div>`;
        });
        return `
          <div class="tooltip-wrap">
            <div class="tooltip-content">
              ${rowString}
            </div>
            <div class="tooltip-footer">${params[0].axisValue}</div>
          </div>
          `;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function (value) {
          if (value >= 100000) {
            return (value / 100000).toFixed(2) + '万';
          }
          return value;
        }
      },
    },
    xAxis: {
      type: 'category',
      data: chartsData.xAxis,
    },
    legend: {
      left: 'left',
      data: (() =>
        series.map((x) => {
          if (ObjectSortArr.find((y) => y.name === x.name)?.show) {
            return x.name;
          }
        }))(),
      itemHeight: 8,
      show: false,
    },
    series,
    ...echartsCommonOptions,
  };
};

const chartOption = ref(getChartOption());

const handleHighlight = (v) => {
  const item = props.dataSource[v.dataIndex];

  const { timestamp } = item;

  timeParamsRef.value = {
    timestamp,
  };
  emit('onChangeTimeParams', timeParamsRef.value);
};

watch(
  () => props.dataSource,
  (newValue) => {
    chartOption.value = getChartOption();
  }
);
</script>

<style lang="less" scoped>
:deep(.tooltip-wrap) {
  min-width: 200px;
  .tooltip-content {
    margin-bottom: 12px;
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .row {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    margin-bottom: 8px;
    p {
      margin-right: 60px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #121f2c;
      line-height: 20px;
    }
    span {
      font-size: 12px;
      font-weight: 400;
      color: #606972;
      line-height: 18px;
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
</style>
