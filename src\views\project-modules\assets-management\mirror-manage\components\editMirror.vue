<template>
  <a-modal :body-style="{ 'margin-left': '-12px' }" :open="editVisible" :mask-closable="false" :confirm-loading="confirmLoading" ok-text="确认" class="modal-height" :width="492" @ok="handleOk" @cancel="onClose">
    <template #title>
      <div class="title-box">{{ titleText }}</div>
    </template>
    <a-form ref="addUser" v-model="formData" :colon="false" :label-col="labelCol" :wrapper-col="wrapperCol" :model="formData">
      <a-form-item name="imageGroupName" label="镜像名称">
        <span class="name">{{ formData.imageGroupName }}</span>
      </a-form-item>
      <a-form-item name="fileId" label="镜像文件ID">
        <div class="row-content">{{ formData.imageId }}</div>
      </a-form-item>
      <a-form-item
        name="tags"
        label="镜像标签"
        class="form-version"
        :rules="[
          {
            message: '',
            trigger: 'change',
            required: true,
            validator: checkTags,
          },
        ]"
      >
        <!-- <span class="form-version-icon">*</span> -->
        <mirror-version-input :exist-tags="formData.tags" style="line-height: 32px" @getMirrorVersion="getMirrorVersion" @versionError="versionErrorExist"></mirror-version-input>
        <div v-if="versionError" class="input-tip error">
          {{ versionText }}
        </div>
        <div v-else class="input-tip">请添加1-3个标签，12个字符以内的大小写字母、数字、中划线、下划线、小数点</div>
      </a-form-item>
      <a-form-item name="address" label="地址">
        <div class="row-content">{{ formData.address }}</div>
      </a-form-item>
      <a-form-item
        name="description"
        label="镜像描述"
        :rules="[
          {
            trigger: 'change',
            required: true,
            validator: checkDescription,
          },
        ]"
      >
        <a-textarea v-model:value="formData.description" class="textarea-box" placeholder="请输入" :maxlength="100" :rows="4" />
        <div class="show-count">
          <span>{{ formData.description?.split(' ').join('').length }}</span>
          / 100
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { message } from 'ant-design-vue';
import { mirrorManageApi } from '@/apis';
import mirrorVersionInput from './mirrorVersionInput.vue';
import { imageErrorUseBackMsg } from '@/constants/image';

export default {
  name: 'CreateMirrorGroup',
  components: {
    mirrorVersionInput,
  },
  props: {
    editVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    editMirrorInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['childFn'],
  data() {
    return {
      titleText: '编辑镜像',
      labelCol: { span: 5 },
      wrapperCol: { span: 20 },
      formData: {},
      limit: 24,
      confirmBtnDisabled: false,
      errorText: '',
      confirmLoading: false,
      versionError: false,
      versionText: '',
    };
  },
  created() {
    this.formData = JSON.parse(JSON.stringify(this.editMirrorInfo));
  },
  methods: {
    checkDescription(rule, value) {
      if (!value || value?.split(' ').join('') === '') {
        return Promise.reject('请输入');
      } else {
        if (value?.includes(' ')) {
          return Promise.reject('不允许输入空格');
        } else {
          return Promise.resolve();
        }
      }
    },
    checkTags(rule, value) {
      if (this.versionError) {
        return Promise.reject('');
      }
      return Promise.resolve();
    },
    onClose() {
      this.$emit('childFn');
    },
    getMirrorVersion(data) {
      this.formData.tags = data;
      if (data.length === 0) {
        this.versionError = true;
        this.versionText = '请添加';
      } else {
        this.versionError = false;
        this.versionText = '';
      }
    },

    versionErrorExist(flag) {
      this.versionError = flag;
      this.versionText = flag ? '请添加1-3个标签，12个字符以内的大小写字母、数字、中划线、下划线、小数点' : '';
    },
    handleOk() {
      this.$refs.addUser.validateFields().then(() => {
        this.confirmLoading = true;
        const sendData = {
          id: this.formData.id,
          description: this.formData.description?.split(' ').join(''),
          tags: this.formData.tags,
        };
        mirrorManageApi.updateMirror(sendData).then((res) => {
          this.confirmLoading = false;
          if (res.code === 0) {
            message.success('镜像文件编辑成功');
            this.$emit('childFn', true);
          } else {
            if (imageErrorUseBackMsg.includes(res.code)) {
              message.error(`当前镜像下标签${res.msg}重复`);
            } else {
              message.error('镜像文件编辑失败，请稍后再试');
            }
          }
        })
        .catch((err) => {
          this.confirmLoading = false;
          message.error('镜像文件编辑失败，请稍后再试');
        });
      })
      .catch((error) => {
        throw new Error(error);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.title-box {
  margin-bottom: 32px;
}
.form-version {
  position: relative;
  max-height: 90px;
  &-icon {
    position: absolute;
    color: #ff4d4f;
    left: -98px;
    top: 7px;
    font-size: 20px;
  }
}
:deep(.ant-form-item-control-input-content) {
  position: relative;
  .textarea-box {
    width: 100%;
  }
  .show-count {
    position: absolute;
    right: 7px;
    bottom: 2px;
    color: #999;
  }
}

:deep(.ant-modal-footer) {
  margin-top: 30px;
}

.input-tip {
  line-height: 18px;
  margin-top: 2px;
  color: #999;

  &.error {
    color: #ff4d4f;
  }
}

.row-content {
  margin-top: 6px;
}

// .name {
//   font-size: 16px;
//   font-weight: 600;
//   color: #121f2c;
// }

// :deep(.ant-form-item) {
//   margin-bottom: 10px;
// }

:deep(.ant-btn) {
  font-size: 14px;
}

:deep(.ant-input) {
  font-size: 14px;
  resize: none;
}

:deep(.ant-modal-content) {
  min-width: 600px;
  box-sizing: border-box;
}

:deep(.ant-form) {
  margin-left: 36px;
}

.ant-input {
  width: 376px;
}

:deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: bold;
}

:deep(.ant-form-item label) {
  font-size: 14px;
  font-weight: 400;
  color: #606972;
}

// :deep(.ant-col-4) {
//   margin-right: 16px;
// }

.fgray {
  color: #a0a6ab;
  margin-top: -14px;
}

:deep(div.ant-modal-footer) {
  padding: 16px 24px !important;

  .ant-btn {
    height: 32px;
  }
}

:deep(.ant-form-item-label) {
  line-height: 20px;
}

:deep(.ant-form-item-control) {
  line-height: 20px;
}

:deep(.ant-form-item .ant-form-item-label > label) {
  color: #00141a;
}
</style>
