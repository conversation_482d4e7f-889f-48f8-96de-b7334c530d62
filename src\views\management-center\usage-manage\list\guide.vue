<template>
  <div v-if="!closed" class="activity-manage-guide">
    <h2>欢迎使用用量管理！</h2>
    <div v-if="collapse" class="guide-content">
      <p>用量管理支持对所有项目空间的用量进行管理，包括加速卡资源组的各类AI加速卡资源使用时长（卡·小时）、CPU资源组的CPU资源使用时长（核·小时）、存储/镜像用量（TB·小时）等，支持按照不同维度进行统计</p>
    </div>
    <a-space :size="8" class="operation-btns">
      <jt-icon :type="collapse ? 'iconshouqi' : 'iconzhankai'" class="iconfont" @click="handleToggle" />
      <jt-icon type="iconguanbi" class="iconfont" @click="handleClose" />
    </a-space>
    <div v-if="collapse" style="display: flex; flex-direction: row">
      <div class="content-item" style="flex: 1">
        <div class="number-box">
          <img src="@/assets/images/details/gpucard.png" alt="" />
          <div class="title">
            <p class="m-b-8">加速卡资源</p>
            <p>用量</p>
          </div>
          <div class="number-content">
            <div>
              <span class="used">{{ formatNumber(usageSummary.latestMonthUsage?.accCardUsage) }}</span>
              <span class="total"> 卡·小时</span>
              <span class="total">&nbsp;/&nbsp;{{ formatNumber(usageSummary.totalUsage?.accCardUsage) }}</span>
              <span class="total"> 卡·小时</span>
            </div>
            <div style="padding-top: 4px">近一月&nbsp;/&nbsp;累计</div>
          </div>
        </div>
      </div>
      <div class="content-item" style="flex: 1">
        <div class="number-box">
          <img src="@/assets/images/details/cpucard.png" alt="" />
          <div class="title">
            <p class="m-b-8">CPU资源</p>
            <p>用量</p>
          </div>
          <div class="number-content">
            <div>
              <span class="used">{{ formatNumber(usageSummary.latestMonthUsage?.cpuUsage) }} </span>
              <span class="total"> 核·小时</span>
              <span class="total">&nbsp;/&nbsp;{{ formatNumber(usageSummary.totalUsage?.cpuUsage) }} </span>
              <span class="total"> 核·小时</span>
            </div>
            <div style="padding-top: 4px">近一月&nbsp;/&nbsp;累计</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getUsageSummary } from '@/apis/usageManage';
import { formatNumber } from '@/utils/number';

// const bannerImg = require('@/assets/images/activity-manage/banner.png');
const storageKey = 'KUNLUN-USAGE-MANAGE';
const guideState = sessionStorage.getItem(storageKey);

const collapse = ref(guideState === '' ? false : true);
const closed = ref(guideState === 'closed' ? true : false);

const handleToggle = () => {
  collapse.value = !collapse.value;
  sessionStorage.setItem(storageKey, collapse.value ? 'collapse' : '');
};

const handleClose = () => {
  closed.value = true;
  sessionStorage.setItem(storageKey, 'closed');
};

onMounted(() => {
  getSummary();
});

const usageSummary = ref({});

async function getSummary() {
  const res = await getUsageSummary();
  if (res.code === 0) {
    usageSummary.value = res.data;
  }
};

// 单位为卡·小时或核·小时，每三位用逗号隔开，保留2位小数，当数值超过100万时，单位显示为万卡·小时或万核·小时
// const formatNumber = (number) => {
//   if (number === null || number === undefined) return '--';

//   const isMillion = number >= 1000000;
//   const formattedNumber = isMillion ? (number / 10000).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',') : number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

//   return `${formattedNumber}${isMillion ? '万' : ''}`;
// };
</script>

<style lang="less" scoped>
.activity-manage-guide {
  // min-height: 210px;
  padding: 20px;
  margin: 8px 0 20px;
  position: relative;
  background-color: #fff;
  flex-shrink: 0;
  border-radius: 4px;
  box-shadow: @jt-box-shadow;
  .banner-img {
    position: absolute;
    bottom: 0px;
    right: 0px;
    height: 204px;
  }
  .guide-content {
    display: flex;
    p {
      width: 1052px;
      margin-top: 12px;
      margin-bottom: 24px;
      color: rgba(0, 20, 26, 0.7);
      line-height: 22px;
    }
  }
  h2 {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin-bottom: 0;
  }
  .operation-btns {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    span {
      background-color: #fff;
      display: inline-block;
      height: 24px;
      line-height: 26px;
      width: 24px;
      border-radius: 2px;
      border: 1px solid rgba(0, 20, 26, 0.15);
      text-align: center;
      cursor: pointer;
      color: rgba(0, 20, 26, 0.45);
      transition: 0.3s all;
      &:hover {
        color: @jt-primary-color;
        border-color: @jt-primary-color;
      }
    }
  }
}

.content-item {
  padding-bottom: 12px;
  .title {
    margin-left: 12px;
    margin-right: 20px;
    margin-top: 10px;
    font-size: 16px;
    color: #121f2c;
  }
  img {
    width: 56px;
  }
  .number-box {
    display: flex;
    .number-content {
      span {
        color: #00141a;
      }
      .used {
        font-size: 32px;
        font-weight: 600;
        line-height: 32px;
      }
      .total {
        font-size: 16px;
        font-weight: 500;
      }
      div {
        color: rgba(0, 20, 26, 0.45);
      }
    }
  }
}
.m-b-8 {
  margin-bottom: 8px;
}
</style>
