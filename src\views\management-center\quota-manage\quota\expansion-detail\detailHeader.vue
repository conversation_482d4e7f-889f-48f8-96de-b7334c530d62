<template>
  <div class="content-Box-Item">
    <jt-loading v-if="loading"></jt-loading>
    <div class="description-item">
      <div class="sub-title">工单信息</div>
      <div class="button-class">
        <a-space v-if="expansionInfo?.status === 1 && editPermission" :size="12">
          <a-button class="edit-button" danger :icon="h(StopOutlined)" @click="handleReject">驳回</a-button>
          <a-button class="edit-button" :icon="h(CheckOutlined)" type="primary" ghost @click="handlePass">通过</a-button>
        </a-space>
      </div>
    </div>
    <div class="sapce-detail">
      <a-descriptions :label-style="{ display: 'inline-block', width: '130px', textAlign: 'right' }">
        <a-descriptions-item label="工单号">{{ expansionInfo?.id }} </a-descriptions-item>
        <a-descriptions-item label="申请时间">{{ expansionInfo?.applyTime ? getFormatTime(expansionInfo?.applyTime) : '--' }}</a-descriptions-item>
        <a-descriptions-item label="状态"><TicketStatusTag :status="expansionInfo?.status" /></a-descriptions-item>
        <a-descriptions-item label="申请人">{{ expansionInfo?.applicant }} </a-descriptions-item>
        <a-descriptions-item label="联系方式">
          <span class="with-icon">{{ phone.text }} </span>
          <div v-if="phone.text !== '--'" class="with-icon-btn" @click="handleEyeClick('phone')">
            <jt-icon :type="phone.eyeClicked ? 'iconeye' : 'icona-bianzu16beifen4'" class="iconfont" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="邮箱信息">
          <span class="with-icon">{{ email.text }} </span>
          <div v-if="email.text !== '--'" class="with-icon-btn" @click="handleEyeClick('email')">
            <jt-icon :type="email.eyeClicked ? 'iconeye' : 'icona-bianzu16beifen4'" class="iconfont" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="项目空间名称">{{ expansionInfo?.projectName }}</a-descriptions-item>
        <a-descriptions-item label="项目空间负责人">{{ expansionInfo?.projectLeader }}</a-descriptions-item>
        <a-descriptions-item> </a-descriptions-item>
        <a-descriptions-item label="加速卡">{{ gpuInfoText }}</a-descriptions-item>
        <a-descriptions-item label="CPU">{{ cpuInfoText }}</a-descriptions-item>
        <a-descriptions-item label="镜像资源">{{ imageInfoText }}</a-descriptions-item>
        <a-descriptions-item label="普通文件存储">{{ commonFileInfoText }}</a-descriptions-item>
        <a-descriptions-item label="高性能文件存储">{{ highPerformanceFileInfoText }}</a-descriptions-item>
        <a-descriptions-item label="对象存储">{{ objInfoText }}</a-descriptions-item>
        <a-descriptions-item label="扩容说明" :span="3">{{ expansionInfo?.applyDesc || '--' }}</a-descriptions-item>
        <!-- 只有处理过的情况才会显示 -->
        <template v-if="expansionInfo?.status === STATUS_TICKET_MAP.PASS || expansionInfo?.status === STATUS_TICKET_MAP.REJECT">
          <a-descriptions-item label="处理人">{{ expansionInfo.processor || '--' }} </a-descriptions-item>
          <a-descriptions-item label="通知用户">{{ formatNotify(expansionInfo?.notify) }}<a @click="openDrawer" style="padding-left: 12px">详情</a> </a-descriptions-item>
          <a-descriptions-item label="处理时间">{{ getFormatTime(expansionInfo.processTime) }} </a-descriptions-item>
          <a-descriptions-item :span="3" label="处理说明">{{ expansionInfo.processDesc || '--' }} </a-descriptions-item>
        </template>
      </a-descriptions>
    </div>
    <DetailModal idType="ticketId" :quota-log-id="expansionInfo?.id" :visible-handle="detailVisibleHandle" @close="closeDetailModal" />
    <RejectModal :expansion-info="expansionInfo" :visible-reject="visibleReject" @close="closeRejectModal" />
    <HandleModal :expansion-info="expansionInfo" :visible-handle="visibleHandle" @close="closeHandleModal" />
  </div>
</template>

<script setup>
import { ref, onMounted, h } from 'vue';
import { StopOutlined, CheckOutlined } from '@ant-design/icons-vue';

import TicketStatusTag from '../../components/ticketStatusTag.vue';
import { getProcessEmailPhoneApi } from '@/apis/quota';
import RejectModal from './rejectModal';
import HandleModal from './handleModal';
import DetailModal from '../details-tables/quotaChangeLogList/detailModal.vue';
import { getUnit } from '@/utils/quota';
import { getFormatTime } from '@/utils';

import { GB_STORE_ARRAY, TB_STORE_ARRAY, APV_QUATO_EDIT_AUTH } from '@/constants/quota';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';
import { dataToSizeConversion } from '@/utils/storage';
import { STATUS_TICKET_MAP } from '@/constants/ticket';

const sendTypeText = {
  message: '站内信',
  sms: '短信',
  email: '邮件',
};

const props = defineProps({
  expansionInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

defineEmits(['close']);

const editPermission = checkPermissionByApvAndMgtItem(APV_QUATO_EDIT_AUTH);
const loading = ref(false);
const getExpansionInfos = ({ key, arr }) => {
  return arr.find((item) => item.key === key);
};

const cpuInfoText = ref('');
const gpuInfoText = ref('');
const imageInfoText = ref('');
const commonFileInfoText = ref('');
const highPerformanceFileInfoText = ref('');
const objInfoText = ref('');
const email = ref({
  text: '',
  oriText: '',
  eyeClicked: false,
});
const phone = ref({
  text: '',
  oriText: '',
  eyeClicked: false,
});

const formatNotify = (notify) => {
  if (notify && notify.length > 0) {
    return notify
      .map((val) => {
        return sendTypeText[val];
      })
      .join('、');
  } else {
    return '--';
  }
};

const updateInfo = (key, textRef) => {
  if (props.expansionInfo) {
    const info = getExpansionInfos({ key, arr: props.expansionInfo?.content || [] });
    if (info) {
      let { oldValue, newValue } = info;
      if (GB_STORE_ARRAY.includes(key) || TB_STORE_ARRAY.includes(key)) {
        oldValue = oldValue > 1024 ? dataToSizeConversion(oldValue).sizeTxt + dataToSizeConversion(oldValue).units : oldValue;
        newValue = newValue > 1024 ? dataToSizeConversion(newValue).sizeTxt + dataToSizeConversion(newValue).units : newValue;
        textRef.value = `${oldValue} -> ${newValue}`;
      } else {
        const unit = getUnit(key);
        textRef.value = `${oldValue} -> ${newValue}${unit}`;
      }
    } else {
      textRef.value = `--`;
    }
  } else {
    textRef.value = '';
  }
};

const getExpInfo = () => {
  email.value.text = props?.expansionInfo?.email || '--';
  email.value.oriText = props?.expansionInfo?.email || '--';
  phone.value.text = props?.expansionInfo?.phone || '--';
  phone.value.oriText = props?.expansionInfo?.phone || '--';
  email.value.eyeClicked = props?.expansionInfo?.email?.length === 0;

  phone.value.eyeClicked = props?.expansionInfo?.phone?.length === 0;

  updateInfo('cpu', cpuInfoText);
  updateInfo('acce_card', gpuInfoText);
  updateInfo('image', imageInfoText);
  updateInfo('general_stor', commonFileInfoText);
  updateInfo('hs_stor', highPerformanceFileInfoText);
  updateInfo('object_stor', objInfoText);
};

const { visibleReject, visibleHandle } = {
  visibleReject: ref(false),
  visibleHandle: ref(false),
};
const closeRejectModal = () => {
  visibleReject.value = false;
};
const closeHandleModal = () => {
  visibleHandle.value = false;
};

const detailVisibleHandle = ref(false);
const closeDetailModal = () => {
  detailVisibleHandle.value = false;
}
const openDrawer = () => {
  detailVisibleHandle.value = true;
};


const handleEyeClick = async (type) => {
  if (type === 'email' && email.value.eyeClicked) {
    email.value.text = email.value.oriText;
    email.value.eyeClicked = false;
    return false;
  }
  if (type === 'phone' && phone.value.eyeClicked) {
    phone.value.text = phone.value.oriText;
    phone.value.eyeClicked = false;
    return false;
  }
  const params = { id: props.expansionInfo.id, type };
  // loading.value = true;
  try {
    const res = await getProcessEmailPhoneApi(params);
    if (res.code === 0) {
      if (type === 'email') {
        email.value.text = res.data;
        email.value.eyeClicked = true;
      } else if (type === 'phone') {
        phone.value.text = res.data;
        phone.value.eyeClicked = true;
      }
    } else {
      message.error(res.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    // loading.value = false;
  }
};

onMounted(async () => {
  getExpInfo();
});

watch(
  () => props.expansionInfo,
  () => {
    getExpInfo();
  }
);

const handlePass = () => {
  visibleHandle.value = true;
};
const handleReject = () => {
  visibleReject.value = true;
};
</script>

<style lang="less" scoped>
.content-Box-Item {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
.description-item {
  display: flex;
  border-bottom: 1px solid rgba(0, 20, 26, 0.08);
  padding-bottom: 8px;
  margin-bottom: 20px;
}
.sub-title {
  flex: 1;
  font-size: @jt-font-size-lg;
  font-weight: 600;
  color: #00141A;
}
:deep(.ant-descriptions-item-content) {
  align-items: center !important;
}
:deep(.ant-descriptions-item-label) {
  align-items: center !important;
}
.with-icon {
  align-items: center;
  display: flex;
}
.with-icon-btn {
  height: 22px;
  padding: 0;
  width: 22px;
  border: none;
  padding-left: 8px;
  padding-top: 2px;
  cursor: pointer;
  color: #666;
  &:hover {
    color: @jt-primary-color;
  }
}
.edit-button {
  width: 82px;
  height: 32px;
}
</style>
