import { keycloak, getKeyc<PERSON>akClientId, getApvAndMgtPermissionByPoolId, getAccessPools, getPreviewEnabled, getDataPreviewEnabled ,getPlatformAccessEnabledByPlatItem } from '@/keycloak';
import { KEYCLOAK_PATH, POOL_APV_MGT_PERMISSION, POOL_SETTING_VISIBLE_PERMISSION, MENU_TABS_CONFIG, PLAT_FORM_APV_MGT_PERMISSION } from '@/config/menu';
import { PREVIEW_MENU_DATA } from '@/config/previewMenu';
import { DATA_PREVIEW_MENU_DATA } from '@/config/dataPreviewMenu';
import store from '@/store';
import { MENU_DATA } from '@/config/menuData';
import { SASAC_PROJECT_MENU_WHITE } from '@/config/sasacMenu';
import { isEntranceBySasac } from '@/utils';

export function checkApvAndMgtPermission(path) {
  const checkItems = POOL_APV_MGT_PERMISSION[path];
  // 该path不需要进行校验
  if (!checkItems || checkItems.length === 0) {
    return true;
  }
  let poolId = store.state.poolInfo.id;

  if (!poolId) {
    const poolList = store.state.poolList;
    if (poolList.length > 0) {
      poolId = poolList[0].id;
    } else {
      return false;
    }
  }
  const authList = getApvAndMgtPermissionByPoolId(poolId);
  const isOk = authList.some((item) => {
    return checkItems.some((checkItem) => {
      if (item.type === checkItem.type && item.key === checkItem.key && item.value === checkItem.value) {
        return true;
      } else {
        return false;
      }
    });
  });
  return isOk;
}

export function checkKeycloakPathAuth(path) {
  if (!keycloak.authenticated) {
    return true;
  }
  const dataPreparationItem = MENU_DATA.find(item => item.title === "数据准备");
  const dataModulesLink = dataPreparationItem?.subs?.map(sub => sub.link?.split('/')[1]) || [];
  const dataPreviewEnabled = getDataPreviewEnabled();
  const previewEnabled = getPreviewEnabled();
  // 读取kc中的灰度预览权限及可预览菜单,没有数据功能预览权限的用户看不到DATA_PREVIEW_MENU_DATA里的菜单
  if (dataModulesLink.includes(path) && !dataPreviewEnabled) {
    return !DATA_PREVIEW_MENU_DATA.includes(path);
  }
  // 读取kc中的灰度预览权限及可预览菜单,没有预览权限的用户看不到PREVIEW_MENU_DATA里的菜单（不包括数据功能）
  if (!previewEnabled) {
    return !PREVIEW_MENU_DATA.includes(path);
  }
  // const authCollections = keycloak.resourceAccess[getKeycloakClientId()]?.roles || [];
  // if (KEYCLOAK_PATH[authType]) {
  //   return authCollections.includes(KEYCLOAK_PATH[authType]);
  // }
  return true;
}

export function checkPoolSettings(path) {
  const matchedSettingName = POOL_SETTING_VISIBLE_PERMISSION[path];
  const menuTabs = MENU_TABS_CONFIG[path];
  if (matchedSettingName) {
    const authCheck = store.state.poolPolicy?.[matchedSettingName] === '1';
    if (menuTabs && authCheck) {
      return menuTabs.some((v) => store.state.poolPolicy?.[v] === '1');
    }
    return authCheck;
  }
  return true;
}

export function checkKeycloakAuth(authType) {
  if (!keycloak.authenticated) {
    return true;
  }
  // const authCollections = keycloak.resourceAccess[getKeycloakClientId()]?.roles || [];
  // return authCollections.includes(authType);
  return true;
}

export function checkPathByRole(path) {
  let menuItem = null;
  MENU_DATA.forEach((item) => {
    if (item.subs.length > 0) {
      item.subs.forEach((subItem) => {
        if (subItem.link.slice(1) === path) {
          menuItem = subItem;
        }
      });
    } else if (item.link.slice(1) === path) {
      menuItem = item;
    }
  });
  if (!menuItem) {
    return true;
  }
  const pathRole = menuItem.role || [];
  const pathSpace = menuItem.space || '';
  // 资源池菜单不需要进行权限校验
  if (pathSpace === 'pool') {
    return true;
  }
  if (pathSpace === 'project') {
    return pathRole.some((item) => store.state.projectRole.includes(item));
  }
  return false;
}

// 针对国资进行菜单白名单过滤,递归过滤菜单(entrance-sasac国资委)
export function checkPathBySasac(path) {
  if (isEntranceBySasac()) {
    return SASAC_PROJECT_MENU_WHITE.includes(path);
  }
  return true;
}

// 针对运营管理菜单地址做鉴权判断
export function checkPathByPlatform(path) {
  let menuItem = null;
  MENU_DATA.forEach((item) => {
    if (item.subs.length > 0) {
      item.subs.forEach((subItem) => {
        if (subItem.link.slice(1) === path) {
          menuItem = subItem;
        }
      });
    } else if (item.link.slice(1) === path) {
      menuItem = item;
    }
  });
  if (!menuItem) {
    return true;
  }
  // const pathRole = menuItem.role || [];
  const pathSpace = menuItem.space || '';
  // 运管平台菜单
  if (pathSpace === 'platform') {
    const checkItems = PLAT_FORM_APV_MGT_PERMISSION[path];
    // 该path不需要进行校验
    if (!checkItems || checkItems.length === 0) {
      return true;
    }
    return checkItems.some((checkItem) => getPlatformAccessEnabledByPlatItem(checkItem));
    // return true;
  }
  // 进行角色权限校验
  // if (pathSpace === 'platform') {
  //   return pathRole.some((item) => store.state.projectRole.includes(item));
  // }
  return false;
}

// 模块显隐控制对于菜单页面的功能模块显隐
export function checkResourceAuth(authAlias = '') {
  if (!authAlias) return false;
  if (!Array.isArray(authAlias)) {
    authAlias = [authAlias];
  }
  return authAlias.some((v) => store.state.poolPolicy?.[v] === '1');
}
