<template>
  <jt-container>
    <jt-iframe-container :link="getLink" />
  </jt-container>
</template>

<script setup>
import { useRoute } from 'vue-router';

const route = useRoute();

const queryDataRedirectUrl = () => {
  const regex = /[?&]redirectUrl=([^]*)/;
  const redirectUrl = window.location.href.match(regex)?.[0]?.split('?redirectUrl=')?.[1] || '';
  return decodeURIComponent(redirectUrl?.split?.('&')[0]);
};

const getLink = computed(() => {
  const redirectUrl = queryDataRedirectUrl();
  if (redirectUrl) {
    return redirectUrl;
  }
  return route.meta?.link;
});

</script>
 
<style lang="less" scoped>
#container-wrap {
  color: @jt-text-color-primary;
}
</style>
