<template>
  <div class="history-record">
    <span class="title">历史记录</span>
    <div v-if="isSearch" class="operate-btn">
      <search-input v-model="keyword" placeholder="请输入对话标题" @change="handleIptChange" @blur="onSearchBlur" />
    </div>
    <div v-else class="operate-btn">
      <span class="add-dialog-btn" @click="addChat"><jt-icon type="icontianjia" class="add-icon" />新增对话</span>
      <span class="search-btn" @click="toSearch"><jt-icon type="iconsousuo" /></span>
    </div>

    <div v-if="historyList.length" class="history-list">
      <div v-for="(item, index) in historyList" :key="index" :class="currentGroupId == item.groupId ? 'list-case-selected' : ''">
        <div v-show="activeInput == index" class="list-case list-case-edit">
          <a-input :ref="(el) => (inputRef[index] = el)" v-model:value="item.name" placeholder="请输入20个字符以内" :maxlength="20" class="case-input" @blur="onEditBlur(item.groupId, $event)" />
        </div>
        <div v-show="activeInput !== index" class="list-case list-case-show">
          <img :src="messageImg" alt="" @click="toDetail(item.groupId)" />
          <p class="case-txt" @click="toDetail(item.groupId)">{{ item.name }}</p>
          <span :class="['case-bth', { 'case-bth-show': delConfirmList[index] }]">
            <jt-icon type="iconbianji1" class="edit-icon icon-btn" @click="editCase(item.groupId, index, $event)" />
            <a-popconfirm title="确定要删除该对话记录吗？" description="删除后将无法恢复，请谨慎操作" placement="topRight" ok-text="确认" cancel-text="取消" overlay-class-name="del-popconfirm" :get-popup-container="getPopupContainer" @confirm="delHistoryCase(item.groupId)" :open="delConfirmList[index]" @openChange="openChange(index, $event)"><jt-icon type="iconshanchu1" class="del-icon icon-btn" /></a-popconfirm>
          </span>
        </div>
      </div>
    </div>

    <div v-else class="emtpty-container">
      <empty-box :keyword="keyword" :title="'历史记录'" />
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, nextTick, watch, defineProps } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import messageImg from '@/assets/images/model-experience/message.png';
import SearchInput from './searchInput.vue';
import EmptyBox from './empty.vue';
import { reqHistoryList, reqDelHistoryCase, reqEditHistoryCase } from '@/apis/modelExperience.js';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const store = useStore();
const isSearch = ref(false);
const keyword = ref('');
const historyList = ref([]);
const inputRef = ref([]);
const activeInput = ref(1.1);
const currentGroupId = ref('');
const delConfirmList = ref([]);
const modelType = computed(() => store.state.modelExperience.modelType);
const serviceList = computed(() => store.state.modelExperience.serviceList);

const addChat = () => {
  store.commit('updateServiceList', []);
  store.commit('updateParamInfo', []);
  router.push('/model-experience');
};

const toSearch = () => {
  isSearch.value = true;
};

const handleIptChange = (val) => {
  keyword.value = val;
  getHistoryList();
};

const onSearchBlur = () => {
  isSearch.value = false;
};

const onEditBlur = (groupId, event) => {
  const relatedTarget = event.relatedTarget;
  const isOutsideButtons = !relatedTarget || !relatedTarget.matches('.edit-icon');
  if (isOutsideButtons) {
    activeInput.value = 1.1;
  }

  const newName = event.target.value;
  //失焦即保存，如果修改值是空，显示原来标题
  if (newName.trim()) {
    renameCase(groupId, newName);
  } else {
    cancelRenameCase();
  }
};

const editCase = (targetId, index, event) => {
  event.preventDefault();
  activeInput.value = index;
  nextTick(() => {
    if (inputRef.value[index]) {
      inputRef.value[index].focus();
    }
  });
};

const openChange = (index, event) => {
  delConfirmList.value[index] = event; //通过设置变量去控制编辑和删除按钮的显示和隐藏
};

const getPopupContainer = () => {
  const targetNode = document.querySelector('.history-record');
  return targetNode;
};

const renameCase = async (groupId, name) => {
  try {
    const res = await reqEditHistoryCase({ groupId, changedName: name });
    const { data } = res || {};
    if (data) {
      getHistoryList();
    } else {
      message.error('历史对话修改名称失败');
      getHistoryList();
    }
  } catch (error) {
    console.log(error);
    message.error('历史对话修改名称失败');
    getHistoryList();
  }
};

const cancelRenameCase = () => {
  getHistoryList();
};

const toDetail = (groupId) => {
  store.commit('updateIsAdd', false);
  router.push(`/model-experience/detail/${groupId}`);
};

const getHistoryList = async () => {
  try {
    const param = {
      chatType: modelType.value,
      keyword: keyword.value,
    };
    const res = await reqHistoryList(param);
    const { data } = res || {};
    let newArr = data.map(function (obj) {
      obj.editStatus = false;
      return obj;
    });
    historyList.value = newArr;
  } catch (error) {
    console.log(error);
  }
};

const delHistoryCase = async (targetId) => {
  try {
    const res = await reqDelHistoryCase(targetId, '');
    const { data } = res || {};
    if (data) {
      getHistoryList();
      message.success('删除成功');
      if (targetId == currentGroupId.value) {
        addChat();
      }
    } else {
      message.error('删除失败，请稍后再试');
    }
  } catch (error) {
    message.error('删除失败，请稍后再试');
    console.log(error);
  }
};

watch(
  () => route.params.groupId,
  async (newId, oldId) => {
    if (newId !== oldId) {
      currentGroupId.value = newId;
      !oldId && getHistoryList(); //新增对话要更新历史记录
    }
  },
  { immediate: false, deep: true }
);

watch(
  () => modelType.value,
  () => {
    getHistoryList();
  }
);

watch(
  () => serviceList.value,
  () => {
    if (serviceList.value.length == 0) {
      getHistoryList();
      router.push('/model-experience');
    }
  }
);

onMounted(() => {
  currentGroupId.value = route.params.groupId;
  getHistoryList();
});
</script>

<style lang="less" scoped>
.history-record {
  background-color: #fff;
  padding: 24px 0 16px;
  width: 190px;
  height: 100%;

  .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    padding: 0 20px;
  }
  .operate-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 16px;
    padding: 0 20px;

    .add-dialog-btn {
      width: 110px;
      color: #00a0cc;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
      border: 1px solid #00a0cc;
      border-radius: 2px;
      .add-icon {
        margin-right: 6px;
      }
    }
    .search-btn {
      width: 32px;
      text-align: center;
      height: 32px;
      line-height: 32px;
      border: 1px solid rgba(0, 20, 26, 0.15);
      border-radius: 2px;
      cursor: pointer;
    }
  }
  .history-list {
    height: calc(100% - 91px);
    overflow-y: auto;
  }

  .list-case {
    padding: 11px 20px;
    display: flex;
    align-items: center;
    line-height: 26px;
    &:hover {
      background: rgba(0, 20, 26, 0.04);
    }
  }

  .list-case-show {
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      cursor: pointer;
    }
    .case-txt {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
    .case-bth {
      opacity: 0;
      white-space: nowrap;
      .icon-btn {
        font-size: 14px;
        margin-left: 10px;
        :hover {
          color: #00a0cc;
          cursor: pointer;
        }
      }
    }
    .case-bth-show {
      opacity: 1;
      .del-icon {
        color: #00a0cc;
      }
    }
    &:hover .case-bth {
      opacity: 1;
    }
  }
  .list-case-edit {
    .case-input {
      width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .list-case-selected {
    background: rgba(0, 20, 26, 0.04);
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 94px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    width: 4px;
    height: 94px;
    background: rgba(0, 20, 26, 0.15);
    border-radius: 4px;
  }
}
</style>
<style lang="less">
.del-popconfirm {
  .ant-popconfirm-description {
    font-size: 12px;
  }
  .ant-btn.ant-btn-sm {
    font-size: 12px;
    padding: 0 6px;
  }
  .ant-popover-content {
    width: 366px;
    margin-right: -6px;
  }
  .ant-popover-inner {
    border-radius: 4px;
  }
}
</style>

