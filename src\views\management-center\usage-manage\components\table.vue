<template>
  <div class="table-search">
    <div class="table-title">用量查询</div>
    <div class="search">
      <slot></slot>
    </div>
  </div>
  <div v-if="activeKey === '2'" class="total-info">
    <div class="info-main">
      <div class="info-type">资源用量总计</div>
      <a-row class="info-row">
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">加速卡资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.accCardUsage) }}卡·小时</div>
          </div>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">CPU资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.cpuUsage) }}核·小时</div>
          </div>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">普通文件存储资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.generalStorageUsage) }}TB·小时</div>
          </div>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">高性能文件存储资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.hsStorageUsage) }}TB·小时</div>
          </div>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">对象存储资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.objectStorageUsage) }}TB·小时</div>
          </div>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">镜像资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.imageUsage) }}TB·小时</div>
          </div>
        </a-col>
      </a-row>
    </div>
    <div class="hr"></div>
    <div class="info-main">
      <div class="info-type">加速卡类型用量</div>
      <a-row class="info-row">
        <a-col v-for="(item, key) in gpuCardType" :key="key" class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">{{ item.title }}资源用量：</div>
            <div class="box-text">{{ totalData.accCardUsageDetail ? formatNumber(totalData.accCardUsageDetail?.[item.key]) : '0.00' }}卡·小时</div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
  <div v-else class="table-info">
    <div class="info-main">
      <div class="info-type">资源用量总计</div>
      <a-row class="info-row">
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">加速卡资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.accCardUsage) }}卡·小时</div>
          </div>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">CPU资源用量：</div>
            <div class="box-text">{{ formatNumber(totalData.cpuUsage) }}核·小时</div>
          </div>
        </a-col>
      </a-row>
    </div>
    <div class="info-main">
      <div class="info-type">加速卡类型用量</div>
      <a-row class="info-row">
        <a-col v-for="(item, key) in gpuCardType" :key="key" class="gutter-row" :span="8">
          <div class="info-content">
            <div class="box-label">{{ item.title }}资源用量：</div>
            <div class="box-text">{{ totalData.accCardUsageDetail ? formatNumber(totalData.accCardUsageDetail?.[item.key]) : '0.00' }}卡·小时</div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
  <div class="table-name">资源用量列表</div>
  <a-configProvider>
    <template #renderEmpty>
      <jt-empty title="用量信息" :show-operation="emptyShowOperation" />
    </template>
    <a-table :dataSource="dataSource" :columns="columns" :pagination="false" :scroll="{ x: 1600 }" @change="handleTableChange">  
      <template #bodyCell="{ column, record }">
        <!-- 项目空间ID -->
        <template v-if="column.dataIndex === 'projectId'">
          <a-tooltip placement="top" :title="record.projectId">
            <span class="txt-ellipsis cursor">{{ record.projectId }}</span>
          </a-tooltip>
        </template>
        <!-- 项目空间名称 -->
        <template v-if="column.dataIndex === 'projectName'">
          <a-tooltip placement="top" :title="record.projectName">
            <span class="link-id-name text-ellipsis cursor" @click="() => ToDetail(record)">{{ record.projectName }}</span>
          </a-tooltip>
        </template>
        <!-- 项目活动名称 -->
        <template v-if="column.dataIndex === 'bussName'">
          <a-tooltip placement="top" :title="record.bussName">
            <span class="link-id-name text-ellipsis cursor" @click="() => handleToDetail(record)">{{ record.bussName || '--' }}</span>
          </a-tooltip>
        </template>
        <!-- 活动类型 -->
        <template v-if="column.dataIndex === 'bussModuleDesc'">
          <div>
            <activeTag :status="getStatus(record.bussModuleDesc)" />
          </div>
        </template>
      </template>
    </a-table>
  </a-configProvider>
  <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
</template>

<script setup>
import activeTag from './activeTag.vue';
import { ACTIVE_TYPE_NEW } from '@/constants/activeType';
import { defineEmits, defineExpose } from 'vue';
import { formatNumber } from '@/utils/number';
import { openInNewTab } from '@/utils';
import { useRouter } from 'vue-router';
const router = useRouter();

const props = defineProps({
  activeKey: {
    type: String,
    default: '',
  },
  dataSource: {
    type: Array,
    default: () => [],
  },
  gpuCardType: {
    type: Array,
    default: () => [],
  },
  totalData: {
    type: Object,
    default: () => {},
  },
  columns: {
    type: Array,
    default: () => [],
  },
  paginationOptions: {
    type: Object,
    default: () => {},
  },
});

const emptyShowOperation = ref(true);

// 进入运营看板详情
const ToDetail = (record) => {
  const routeUrl = router.resolve({ path: `/res-dashboard/project-detail/${record.projectId}` });
  openInNewTab(routeUrl.href);
};

const getStatus = (value) => {
  return Object.keys(ACTIVE_TYPE_NEW).find((key) => ACTIVE_TYPE_NEW[key] === value);
};

const emit = defineEmits(['onDetail', 'onPageNumChange', 'onPageSizeChange', 'onTableChange']);

// 进入活动明细详情
const handleToDetail = (record) => {
  emit('onDetail', record);
};

const changePageNum = (pageNum) => {
  emit('onPageNumChange', pageNum);
};

const changePageSize = (size) => {
  emit('onPageSizeChange', size);
};

const handleTableChange = (pagination, filters, sorter) => {
  emit('onTableChange', { filters, sorter });
};

defineExpose({
  emptyShowOperation,
});
</script>

<style lang="less" scoped>
.table-search {
  display: flex;
  justify-content: space-between;
  .table-title {
    font-weight: 600;
    font-size: 16px;
    color: #00141a;
    line-height: 32px;
  }
}

.total-info {
  margin: 20px 0 40px;
  padding: 20px 20px 8px;
  background: rgba(0, 20, 26, 0.02);
  border-radius: 2px;
  .info-main {
    width: 100%;
    display: flex;
    // margin-bottom: 16px;
    .info-type {
      font-weight: 400;
      font-size: 14px;
      color: #00141a;
      width: 98px;
      // text-align: right;
    }
    .info-row {
      width: calc(100% - 100px);
    }
    .info-content {
      display: flex;
      margin-bottom: 16px;
      .box-label {
        width: 220px;
        text-align: right;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 20, 26, 0.7);
      }
      .box-text {
        font-weight: 400;
        font-size: 14px;
        color: #00141a;
        padding-left: 8px;
      }
    }
  }
  .hr {
    width: 100%;
    height: 1px;
    background: rgba(0, 20, 26, 0.08);
    margin: 4px 0 20px;
  }
}

.table-info {
  margin: 20px 0 32px;
  padding: 20px 20px 28px;
  background: rgba(0, 20, 26, 0.02);
  border-radius: 2px;
  .info-main {
    width: 100%;
    display: flex;
    margin-bottom: 16px;
    .info-type {
      font-weight: 400;
      font-size: 14px;
      color: #00141a;
      width: 100px;
      text-align: right;
    }
    .info-row {
      width: calc(100% - 100px);
    }
    .info-content {
      display: flex;
      .box-label {
        width: 220px;
        text-align: right;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 20, 26, 0.7);
      }
      .box-text {
        font-weight: 400;
        font-size: 14px;
        color: #00141a;
        padding-left: 8px;
      }
    }
  }
}
.table-name {
  font-weight: 600;
  font-size: 14px;
  color: #00141a;
  margin-bottom: 20px;
  &::before {
    display: inline-block;
    content: '';
    width: 4px;
    height: 14px;
    background: #00a0cc;
    margin-right: 8px;
    margin-bottom: 2px;
    vertical-align: middle;
  }
}
.txt-ellipsis {
  display: inline-block;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cursor:hover {
  cursor: pointer;
}
</style>
