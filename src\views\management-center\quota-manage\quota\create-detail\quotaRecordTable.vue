<template>
  <a-table :columns="changeQuotaColumn" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleChange">
    <template #emptyText>
      <div style="margin-top: 30px">
        <img :src="emptyImg" alt="" width="80" />
        <div style="padding-top: 16px; margin-bottom: 50px; color: rgba(0, 20, 26, 0.7); font-size: 14px">暂无配额变更记录</div>
      </div>
    </template>
    <template #bodyCell="{ column, record, text }">
      <template v-if="column.key === 'id'">
        <a-button style="padding-left: 0px" type="link" @click="() => openDetail(record)">详情</a-button>
      </template>
      <template v-if="column.key === 'quota'">
        {{ record.quota.crprj }}
      </template>
      <template v-if="column.key === 'reqDesc'">
        <a-tooltip v-if="text">
          <template #title>
            <div style="max-height: 256px; overflow-y: auto">{{ text }}</div>
          </template>
          <span>{{ text }}</span>
        </a-tooltip>
        <span v-else>--</span>
      </template>
    </template>
  </a-table>
  <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  <detail-drawer :edit-auth="editAuth" v-model:open="drawerVisible" :id="operationItem.id" />
</template>

<script setup>
import { quotaApi } from '@/apis';
import { useRoute } from 'vue-router';
import DetailDrawer from '../../components/detailDrawer/index.vue';
import { getFormatTime } from '@/utils';
import { APV_QUATO_EDIT_AUTH, MGT_QUATO_EDIT_AUTH, QUOTA_MANAGE_TABS } from '@/constants/quota';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';

const emptyImg = require('@/assets/images/activity-manage/empty.png');
const route = useRoute();
const loading = ref(false);

let sortField = undefined;
let isAsc = undefined;

const props = defineProps({
  reload: {
    type: Boolean,
  },
});

const changeQuotaColumn = [
  {
    title: '处理时间',
    key: 'updateTime',
    dataIndex: 'updateTime',
    customRender({ text }) {
      return text ? getFormatTime(text) : '--';
    },
  },
  {
    title: '处理人',
    key: 'updateUser',
    dataIndex: 'updateUser',
  },
  {
    title: '新建项目配额',
    key: 'quota',
    dataIndex: 'quota',
  },
  {
    title: '申请时间',
    key: 'createTime',
    dataIndex: 'createTime',
    sorter: true,
    customRender({ record, text }) {
      return record.ticketId ? getFormatTime(text) : '--';
    },
  },
  {
    title: '申请人',
    key: 'reqUser',
    dataIndex: 'reqUser',
    customRender: ({ record, text }) => {
      return record.ticketId ? text : '--';
    },
  },
  {
    title: '需求名称',
    key: 'reqTitle',
    dataIndex: 'reqTitle',
    customRender: ({ text }) => {
      return text || '--';
    },
  },
  {
    title: '需求描述',
    key: 'reqDesc',
    dataIndex: 'reqDesc',
    ellipsis: true,
    customRender: ({ text }) => {
      return text || '--';
    },
  },
  {
    title: '详情',
    key: 'id',
    dataIndex: 'id',
  },
];
const dataSource = ref([]);
const drawerVisible = ref(false);
const operationItem = ref({});
const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});

const editAuth = checkPermissionByApvAndMgtItem(route.meta.type === QUOTA_MANAGE_TABS.CREATE_PROJECT ? APV_QUATO_EDIT_AUTH : MGT_QUATO_EDIT_AUTH);

// onMounted(() => {
//   getTable();
// });

watch(
  () => props.reload,
  () => {
    paginationOptions.value.pageNum = 1;
    getTable();
  }
);

const handleChange = (pagination, filters, sorter) => {
  if (sorter.order) {
    sortField = sorter.field;
    isAsc = sorter.order === 'ascend' ? true : false;
  } else {
    sortField = undefined;
    isAsc = undefined;
  }
  getTable();
};

const getTable = async () => {
  loading.value = true;
  const res = await quotaApi.getQuotaChangeList({
    userName: route.query.userName,
    type: 'CreateProject',
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
    sortField,
    isAsc,
  });
  if (res.code === 0) {
    dataSource.value = res.data.data;
    paginationOptions.value.total = res.data.total;
  } else {
    dataSource.value = [];
    paginationOptions.value.total = 0;
  }
  loading.value = false;
};
const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getTable();
};
const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getTable();
};
const openDetail = (record) => {
  operationItem.value = record;
  drawerVisible.value = true;
};

watch(
  () => route.query.userName,
  () => {
    getTable();
  }, {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.quota-text-ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
