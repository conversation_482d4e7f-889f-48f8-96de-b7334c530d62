<template>
  <div class="content-Box-Item">
    <jt-loading v-if="loading"></jt-loading>
    <div class="description-item">
      <div class="sub-title">基本信息</div>
      <div v-if="editPermission" class="button-class">
        <a-space :size="12">
          <a-button class="edit-button" type="primary" ghost @click="handleAdjustQuota">
            <template #icon>
              <span class="iconfont icona-icon-shipinliuguanlizuodaohang"></span>
            </template>
            <span class="button-text">调整配额</span>
          </a-button>
        </a-space>
      </div>
    </div>
    <div class="sapce-detail">
      <a-descriptions :label-style="{ display: 'inline-block', width: '130px', textAlign: 'right' }">
        <a-descriptions-item label="项目空间名称">{{ quotaInfo?.name }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ getFormatTime(quotaInfo?.createTime) }}</a-descriptions-item>
        <a-descriptions-item></a-descriptions-item>
        <a-descriptions-item label="负责人">{{ quotaInfo?.leader }}</a-descriptions-item>
        <a-descriptions-item label="联系方式">
          <span class="with-icon">{{ phoneNum.text }} </span>
          <div v-if="phoneNum.text !== '--'" class="with-icon-btn" @click="handleEyeClick('phone')">
            <jt-icon :type="phoneNum.eyeClicked ? 'iconeye' : 'icona-bianzu16beifen4'" class="iconfont" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="邮箱信息">
          <span class="with-icon">{{ email.text }} </span>
          <div v-if="email.text !== '--'" class="with-icon-btn" @click="handleEyeClick('email')">
            <jt-icon :type="email.eyeClicked ? 'iconeye' : 'icona-bianzu16beifen4'" class="iconfont" />
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="加速卡">{{ gpuInfoText }}</a-descriptions-item>
        <a-descriptions-item label="CPU">{{ cpuInfoText }}</a-descriptions-item>
        <a-descriptions-item label="镜像资源">{{ imageInfoText }}</a-descriptions-item>
        <a-descriptions-item label="普通文件存储">{{ commonFileInfoText }}</a-descriptions-item>
        <a-descriptions-item label="高性能文件存储">{{ highPerformanceFileInfoText }}</a-descriptions-item>
        <a-descriptions-item label="对象存储">{{ objInfoText }}</a-descriptions-item>
        <a-descriptions-item label="项目空间描述" :span="3">
          <span :title="quotaInfo?.desc">{{ quotaInfo?.desc }}</span>
        </a-descriptions-item>
      </a-descriptions>
    </div>
    <HandleScaleModal :quota-info="quotaInfo" :visible-handle="visibleHandle" @close="closeHandleScaleModal" />
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { message } from 'ant-design-vue';

import { GB_STORE_ARRAY, TB_STORE_ARRAY, DYNAMIC_DATA_MAP, MGT_QUATO_EDIT_AUTH } from '@/constants/quota';
import { getFormatTime } from '@/utils/index.js';
import HandleScaleModal from './handleScaleModal';
import { getLeaderEmailPhoneApi } from '@/apis/quota';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';
import { dataToSizeConversion } from '@/utils/storage';

const props = defineProps({
  quotaInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const editPermission = checkPermissionByApvAndMgtItem(MGT_QUATO_EDIT_AUTH);
const loading = ref(false);
const cpuInfoText = ref('');
const gpuInfoText = ref('');
const imageInfoText = ref('');
const commonFileInfoText = ref('');
const highPerformanceFileInfoText = ref('');
const objInfoText = ref('');
const email = ref({
  text: '',
  oriText: '',
  eyeClicked: false,
});
const phoneNum = ref({
  text: '',
  oriText: '',
  eyeClicked: false,
});

const getQuotaInfos = ({ key, obj }) => {
  return obj[key];
};

const updateInfo = (key, textRef) => {
  if (props.quotaInfo) {
    const info = getQuotaInfos({ key, obj: props.quotaInfo?.quotaStatus || {} });
    if (info) {
      let { used, total } = info;
      if (GB_STORE_ARRAY.includes(key) || TB_STORE_ARRAY.includes(key)) {
        used = used > 1024 ? dataToSizeConversion(used).sizeTxt + dataToSizeConversion(used).units : used;
        total = total > 1024 ? dataToSizeConversion(total).sizeTxt + dataToSizeConversion(total).units : total;
        textRef.value = `${used} / ${total}（已用/总共）`;
      } else {
        textRef.value = `${used} / ${total}${DYNAMIC_DATA_MAP[key].unit}（已用/总共）`;
      }
    } else {
      textRef.value = `--`;
    }
  } else {
    textRef.value = '';
  }
};

const getExpInfo = () => {
  email.value.text = props?.quotaInfo?.email || '--';
  email.value.oriText = props?.quotaInfo?.email || '--';
  phoneNum.value.text = props?.quotaInfo?.phoneNum || '--';
  phoneNum.value.oriText = props?.quotaInfo?.phoneNum || '--';

  email.value.eyeClicked = props?.quotaInfo?.email === '--' || typeof props?.quotaInfo?.email === 'undefined';
  phoneNum.value.eyeClicked = props?.quotaInfo?.phoneNum === '--' || typeof props?.quotaInfo?.phoneNum === 'undefined';

  updateInfo('cpu', cpuInfoText);
  updateInfo('acce_card', gpuInfoText);
  updateInfo('image', imageInfoText);
  updateInfo('general_stor', commonFileInfoText);
  updateInfo('hs_stor', highPerformanceFileInfoText);
  updateInfo('object_stor', objInfoText);
};

const { visibleHandle } = {
  visibleHandle: ref(false),
};

const closeHandleScaleModal = () => {
  visibleHandle.value = false;
};

const handleEyeClick = async (type) => {
  if (type === 'email' && email.value.eyeClicked) {
    email.value.text = email.value.oriText;
    email.value.eyeClicked = false;
    return false;
  }
  if (type === 'phone' && phoneNum.value.eyeClicked) {
    phoneNum.value.text = phoneNum.value.oriText;
    phoneNum.value.eyeClicked = false;
    return false;
  }
  const params = { projectId: props.quotaInfo.id, type };
  // loading.value = true;
  try {
    const res = await getLeaderEmailPhoneApi(params);
    if (res.code === 0) {
      if (type === 'email') {
        email.value.text = res.data;
        email.value.eyeClicked = true;
      } else if (type === 'phone') {
        phoneNum.value.text = res.data;
        phoneNum.value.eyeClicked = true;
      }
    } else {
      message.error(res.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    // loading.value = false;
  }
};

onMounted(async () => {
  getExpInfo();
});

watch(
  () => props.quotaInfo,
  () => {
    getExpInfo();
  }
);

const handleAdjustQuota = () => {
  visibleHandle.value = true;
};
</script>

<style lang="less" scoped>
.content-Box-Item {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
.description-item {
  display: flex;
  border-bottom: 1px solid rgba(0, 20, 26, 0.08);
  padding-bottom: 8px;
  margin-bottom: 20px;
}
.sub-title {
  flex: 1;
  font-size: @jt-font-size-lg;
  font-weight: 600;
  color: #00141A;
}
.button-text {
  vertical-align: top;
}
:deep(.ant-descriptions-item-content) {
  align-items: center !important;
}
:deep(.ant-descriptions-item-label) {
  align-items: center !important;
}
.with-icon-btn {
  display: inline;
  height: 22px;
  padding: 0;
  width: 22px;
  vertical-align: middle;
  border: none;
  padding-left: 8px;
  color: #666;
  cursor: pointer;
  &:hover {
    color: @jt-primary-color;
  }
}

.btn-operate {
  border-radius: 2px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 21px;
  border: none;
}
.btn-blue {
  background: linear-gradient(90deg, #00cada 0%, #00a2f4 100%);
  color: #ffffff;
}
.btn-blue:hover {
  background: linear-gradient(90deg, #00cada 0%, #00a2f4 100%);
  color: #ffffff;
}
.btn-red {
  background: red;
  color: #ffffff;
}
.btn-red:hover {
  background: red;
  color: #ffffff;
}
.edit-button {
  .iconfont {
    margin-right: 6px;
  }
}
</style>
