import { getFormatDuringTime, formatTime } from '@/utils/index';

export const columns = [
  {
    title: '服务名称',
    dataIndex: 'instanceName',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
  {
    title: '服务类型',
    dataIndex: 'instanceType',
    filters: [  
      {
        text: '模型服务',
        value: 'model',
      },
      {
        text: '镜像服务',
        value: 'image',
      },
    ],
    customRender: ({ text, record }) => {
      return record.instanceType === 'model' ? '模型服务' : '镜像服务';
    },
  },
  {
    title: '服务调用量',
    dataIndex: 'callCount',
    sorter: true,
    customRender: ({ text, record }) => {
      return text.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') || '--';
    },
  },
  {
   title: '运行时长',
   dataIndex: 'runningTime',
   sorter: true,
   customRender: ({ text, record }) => {
     return text ? formatTime(text) : '--';
   },
  },
  {
    title: '所属项目空间',
    dataIndex: 'projectName',
    customRender: ({ text, record }) => {
      return text;
    },
  },
  {
    title: '负责人',
    dataIndex: 'leader',
    customRender: ({ text, record }) => {
      return text;
    },
  },
  {
    title: '所属资源组',
    dataIndex: 'resourceGroupName',
    filters: [],
    customRender: ({ text, record }) => {
      return text;
    },
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    customRender: ({ text, record }) => {
      return text;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true,
    customRender: ({ text, record }) => {
      return text;
    },
  },
];
