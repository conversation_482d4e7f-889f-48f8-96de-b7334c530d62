<template>
  <div class="config-title-box">
    <div class="config-title">{{ props.title }}</div>
    <div class="config-status config-status-open" v-if="props.open">开启</div>
    <div class="config-status config-status-close" v-else>关闭</div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  open: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="less" scoped>
.config-title-box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  .config-title {
    font-weight: 600;
    font-size: 16px;
    color: #00141a;
    margin-right: 12px;
  }
  .config-status {
    width: 44px;
    height: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 12px;
    position: relative;
    top: 1px;
  }
  .config-status-open {
    background: #e8ffea;
    border: 1px solid #83dd8f;
    color: #13aa36;
  }
  .config-status-close {
    background: rgba(0, 20, 26, 0.02);
    border: 1px solid rgba(0, 20, 26, 0.15);
    color: rgba(0, 20, 26, 0.7);
  }
}
</style>
