<template>
  <div class="tooltip">
    <div class="header">
      <div class="name">集群</div>
      <div class="count">60000<span>卡</span></div>
    </div>
    <div class="content">
      <div class="detail-item">
        <div class="label label1">节点名称:</div>
        <div class="text">sfsfgaksfakf</div>
      </div>
      <div class="detail-item detail-status">
        <div class="label label2">状态:</div>
        <div class="status">不可用</div>
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="less" scoped>
.tooltip {
  position: absolute;
  top: 35%;
  left: 40%
}
.header {
  width: 313px;
  height: 89px;
  background: url('~@/assets/images/platform/model_title.png') no-repeat;
  background-size: 100% 100%;
  .name {
    font-weight: 400;
    font-size: 17px;
    color: #FFFFFF;
    line-height: 24px;
    text-shadow: 0px 0px 9px rgba(0,228,255,0.47);
    text-align: center;
  }
  .count {
    font-weight: 800;
    font-size: 45px;
    color: #FFFFFF;
    line-height: 58px;
    text-shadow: 0px 0px 13px rgba(0,0,0,0.54);
    text-align: center;
    background: linear-gradient(to bottom, #FFFFFF 0%, #F8FEFF 32%, #60EEFF 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    span {
      font-weight: 400;
      font-size: 28px;
      display: inline-block;
    }
  }
}
.content {
  width: 599px;
  height: 261px;
  background: url('~@/assets/images/platform/model_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 35px 59px 35px 27px;
  margin-top: 67px;
  .detail-item {
    .label {
      color: #fff;
      font-size: 31px;
      font-weight: 500;
    }
    .label::before {
      content: '';
      width: 9px;
      height: 23px;
      margin-right: 14px;
      display: inline-block;
    }
    .label1::before {
      background-color: #00FFCA;
    }
    .label2::before {
      background-color: #FBF3F1;
    }
    .text {
      color: #fff;
      font-size: 31px;
      font-weight: 500;
      margin-top: 24px;
    }
  }
  .detail-status {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    .status {
      font-weight: 400;
      font-size: 27px;
      color: #FFFFFF;
      line-height: 40px;
    }
  }
}
</style>