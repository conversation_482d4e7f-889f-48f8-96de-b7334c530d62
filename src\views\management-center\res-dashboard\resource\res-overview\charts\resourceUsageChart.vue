<template>
  <div>
    <v-chart :option="chartOption" autoresize style="height: 300px; width: 100%" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
import VChart from 'vue-echarts';
import { transferNumber } from '@/utils/number'

use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);

const props = defineProps({
  xData: {
    type: Array,
    default: () => [],
  },
  yData: {
    type: Array,
    default: () => [],
  },
});

const getChartOption = () => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    valueFormatter: (data) => {
      return data = `${data}(卡·小时)`;
    },
  },
  grid: {
    left: '2%',
    right: '25%',
    top: '5%',
    bottom: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: false,
    },
  },
  yAxis: {
    type: 'category',
    inverse: true,
    axisLabel: {
      fontSize: 12,
      color: 'rgba(0, 20, 26, 0.7)',
      width: 80,
      overflow: 'truncate',
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(0,20,26,0.08)',
      },
    },
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    data: props.xData,
  },
  series: [
    {
      name: '占用情况',
      type: 'bar',
      data: props.yData,
      itemStyle: {
        color: {
          type: 'linear',
          x: 1,
          y: 0,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(250,165,72,1)', // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(250,165,72,0.48)', // 100% 处的颜色
            },
          ],
        },
        borderRadius: [0, 100, 100, 0],
      },
      barWidth: 8,
      label: {
        show: true,
        position: 'right',
        color: 'rgba(0,20,26,0.7)',
        formatter: (params) => {
          return transferNumber(params.data) + '(卡·小时)';
        },
      },
    },
  ],
});

const chartOption = ref(getChartOption());

watch(
  () => props.xData,
  (newValue) => {
    chartOption.value = getChartOption();
  }
);
</script>

<style scoped></style>
