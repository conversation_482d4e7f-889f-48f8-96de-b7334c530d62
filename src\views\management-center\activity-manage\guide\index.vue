<template>
  <div v-if="!closed" class="activity-manage-guide">
    <h2>Hi，欢迎使用项目活动管理！</h2>
    <div class="guide-content" v-if="collapse">
      <p>项目活动管理支持回收调度项目内资源，对该资源池下所有项目活动进行查看，支持单个和批量停止某一类型的项目活动，并通过发送站内信、邮件、短信的方式通知用户</p>
    </div>
    <a-button v-if="collapse" type="primary" disabled style="margin:8px 0px;">查看帮助</a-button>
    <a-space :size="8" class="operation-btns">
      <jt-icon :type="collapse ? 'iconshouqi' : 'iconzhankai'" class="iconfont" @click="handleToggle" />
      <jt-icon type="iconguanbi" class="iconfont" @click="handleClose" />
    </a-space>
    <img :src="bannerImg" v-if="collapse" class="banner-img" alt=""/>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const bannerImg = require('@/assets/images/activity-manage/banner.png');
const storageKey = 'KUNLUN-ACTIVITY-MANAGE';
const guideState = sessionStorage.getItem(storageKey);

const collapse = ref(guideState === '' ? false : true);
const closed = ref(guideState === 'closed' ? true : false);

const handleToggle = () => {
  collapse.value = !collapse.value;
  sessionStorage.setItem(storageKey, collapse.value ? 'collapse' : '');
};

const handleClose = () => {
  closed.value = true;
  sessionStorage.setItem(storageKey, 'closed');
};
</script>

<style lang="less" scoped>
.activity-manage-guide {
  padding: 20px;
  margin: 8px 20px 20px;
  position: relative;
  background-color: #fff;
  flex-shrink: 0;
  border-radius: 4px;
  box-shadow: @jt-box-shadow;
  .banner-img {
    position: absolute;
    bottom: 0px;
    right: 0px;
    height: 204px;
  }
  .guide-content {
    display: flex;
    margin-bottom: 12px;
    p {
      width: 762px;
      margin-top: 12px;
      margin-right: 60px;
      color: rgba(0, 20, 26, 0.7);
      line-height: 22px;
    }
  }
  h2 {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin-bottom: 0;
  }
  .operation-btns {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    span {
      background-color: #fff;
      display: inline-block;
      height: 24px;
      line-height: 26px;
      width: 24px;
      border-radius: 2px;
      border: 1px solid rgba(0, 20, 26, 0.15);
      text-align: center;
      cursor: pointer;
      color: rgba(0, 20, 26, 0.45);
      transition: 0.3s all;
      &:hover {
        color: @jt-primary-color;
        border-color: @jt-primary-color;
      }
    }
  }
}
</style>
