<template>
  <div class="tooltip">
    <div class="header">
      <div class="name">集群</div>
      <div class="count">60000<span>卡</span></div>
    </div>
    <div class="content">
      <div class="detail-title">佛挡杀佛</div>
      <div class="detail-content">
        <div class="detail-item" v-for="(item, index) in dataArr" :key="index">
          <div class="label" :class="`label${index+1}`">{{ item.label }}</div>
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const dataArr = [
  {
    label: 'AI加速卡数：',
    text: '3434'
  },
  {
    label: '加速卡类型：',
    text: '3434'
  },
  {
    label: '所属集群：',
    text: '3434'
  },
  {
    label: '训练时长：',
    text: '3434'
  },
];
</script>

<style lang="less" scoped>
.tooltip {
  position: absolute;
  top: 35%;
  left: 40%
}
.header {
  width: 313px;
  height: 89px;
  background: url('~@/assets/images/platform/model_title.png') no-repeat;
  background-size: 100% 100%;
  .name {
    font-weight: 400;
    font-size: 17px;
    color: #FFFFFF;
    line-height: 24px;
    text-shadow: 0px 0px 9px rgba(0,228,255,0.47);
    text-align: center;
  }
  .count {
    font-weight: 800;
    font-size: 45px;
    color: #FFFFFF;
    line-height: 58px;
    text-shadow: 0px 0px 13px rgba(0,0,0,0.54);
    text-align: center;
    background: linear-gradient(to bottom, #FFFFFF 0%, #F8FEFF 32%, #60EEFF 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    span {
      font-weight: 400;
      font-size: 28px;
      display: inline-block;
    }
  }
}
.content {
  width: 600px;
  height: 362px;
  background: url('~@/assets/images/platform/model_detail.png') no-repeat;
  background-size: 100% 100%;
  margin-top: 36px;
  .detail-title {
    font-size: 34px;
    color: #00E4FF;
    line-height: 52px;
    text-align: center;
    background: linear-gradient(to bottom, #FFFFFF 0%, #00E0FF 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .detail-content {
    padding: 45px 45px 35px;
    .detail-item:not(:first-child) {
      margin-top: 35px;
    }
    .detail-item {
      display: flex;
      justify-content: space-between;
      .label {
        color: #fff;
        font-size: 31px;
        font-weight: 500;
      }
      .label::before {
        content: '';
        width: 9px;
        height: 23px;
        margin-right: 14px;
        display: inline-block;
      }
      .label1::before {
        background-color: #00FFCA;
      }
      .label2::before {
        background-color: #FF8357;
      }
      .label3::before {
        background-color: #488BFF;
      }
      .label4::before {
        background-color: #488BFF;
      }
      .text {
        color: #fff;
        font-size: 27px;
        font-weight: 400;
      }
    }
  }
}
</style>