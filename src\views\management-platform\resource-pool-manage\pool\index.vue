<template>
  <div class="wrapper-box">
    <section class="content-box">
      <guide />
      <div class="content-wrap-box">
        <a-tabs v-model:active-key="activeKey" :destroy-inactive-tab-pane="true" :tabBarStyle="{borderBottom: 'unset'}" @change="handleTabChange">
          <template #rightExtra>
            <a-space>
              <jt-reload-icon-btn @click="handleReload" />
              <a-input v-model:value="searchText" class="input" :placeholder="searchPlaceholder" allow-clear @change="handleInput">
                <template #prefix>
                  <jt-icon type="iconsousuo" class="search-icon" style="color: #bec2c5"></jt-icon>
                </template>
              </a-input>
              <!-- <a-button class="kl-create-btn" type="primary" @click="handleCreate"><PlusOutlined /> 新增用户配额</a-button> -->
            </a-space>
          </template>

          <a-tab-pane v-if="poolListAuth" :key="POOL_MANAGE_TABS.POOLLIST" tab="资源池列表">
            <pool-list ref="poolListRef" :search-text="searchText" :reload="reloadTable"></pool-list>
          </a-tab-pane>
        </a-tabs>
      </div>
    </section>
  </div>
</template>

<script setup>
import debounce from 'lodash/debounce';

import { computed, ref, watch } from 'vue';
import { PLAT_POOL_VIEW_AUTH } from '@/constants/management-platform/auth.js';
import { POOL_MANAGE_TABS, POOL_MANAGE_SEARCH_PLACEHOLDER } from '@/constants/management-platform/poolManage.js';
import { useRoute, useRouter } from 'vue-router';
import poolList from './poolList/poolList.vue';
import Guide from '../guide/index.vue';
import { getPlatformAccessEnabledByPlatItem } from '@/keycloak';
// import { PlusOutlined } from '@ant-design/icons-vue';

const poolListAuth = getPlatformAccessEnabledByPlatItem(PLAT_POOL_VIEW_AUTH);

const router = useRouter();
const route = useRoute();
const { POOLLIST } = POOL_MANAGE_TABS;
const { tab } = route.query;

const searchText = ref(route.query.searchText || '');

const poolListRef = ref(null);

const handleTabChange = (tab) => {
  searchText.value = '';
  route.query.pageNum = 1;
  route.query.pageSize = 10;
  route.query.sortField = undefined;
  route.query.isAsc = undefined;
  router.replace({ query: { tab: tab } });
};

const initActiveKey = () => {
  if (tab) {
    return tab;
  }
  if (poolListAuth) {
    return POOLLIST;
  }
};

const activeKey = ref(initActiveKey());
const searchPlaceholder = computed(() => POOL_MANAGE_SEARCH_PLACEHOLDER[activeKey.value]);
const handleInput = debounce((event) => {
  searchText.value = event.target._value;
}, 400);

const reloadTable = ref(false);
const handleReload = () => {
  reloadTable.value = !reloadTable.value;
  searchText.value = '';
};
</script>

<style lang="less" scoped>
.wrapper-box {
  .content-box {
    padding: 20px;
    margin-top: -10px;
    .content-wrap-box {
      position: relative;
      margin-bottom: 20px;
      padding: 20px;
      padding-bottom: 48px;
      background-color: #fff;
      min-height: ~'calc(100% - 20px)';
      border-radius: 4px;
      :deep(.ant-tabs) {
        .ant-tabs-ink-bar{
          display: none;
        }
        .ant-tabs-nav::before {
          border-bottom: unset;
        }
        .ant-tabs-tab.ant-tabs-tab-active {
          font-family: PingFangSC, PingFang SC, sans-serif;
          font-weight: 600;
          font-size: 16px;
          color: #00141A;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          .ant-tabs-tab-btn {
            color: #00141A;
          }
        }
      }
    }
  }
}
:deep(.ant-tabs-top-bar) {
  border: 0;
  margin-bottom: 16px;
}
:deep(.ant-tabs .ant-tabs-large-bar .ant-tabs-tab) {
  height: 33px;
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  padding: 0 0 10px 0;
}

:deep(.ant-tabs-extra-content) {
  height: 33px;
  display: flex;
  align-items: center;

  .reload-icon {
    font-size: 18px;
    margin-right: 10px;
    cursor: pointer;
  }

  .input {
    width: 240px;
    height: 32px;
    border-radius: 2px;
    font-size: 14px;

    .search-icon {
      color: #bec2c5;
      position: relative;
      top: 0px;
    }
  }

  .cre-pro-input {
    width: 290px;
  }

  .add {
    height: 32px;
    background: @jt-primary-color;
    border-radius: 2px;
    font-size: 14px;
    color: #fff;
    margin-left: 12px;
    display: flex;
    align-items: center;

    &-icon {
      color: #fff;
      margin-right: 4px;
    }
  }
}
.tab-bar {
  margin-right: 6px;
}
:deep .ant-btn-primary.ant-btn-background-ghost:disabled,
.ant-btn-default.ant-btn-dangerous:disabled {
  background: rgba(0, 20, 26, 0.04);
  border: 1px solid rgba(0, 20, 26, 0.15);
  color: rgba(0, 20, 26, 0.25);
}


</style>
