export default [
  {
    path: '/project-log-manage',
    name: 'project-log-manage',
    component: () => import('@/views/project-modules/operations-management/project-log-manage/index.vue'),
    children: [
      {
        path: '',
        name: 'project-log-manage-list',
        component: () => import('@/views/project-modules/operations-management/project-log-manage/list/index.vue'),
        meta: {
          header: [
            {
              name: '项目空间管理',
              path: '/project-space',
            },
            {
              name: '运营运维',
            },
            {
              name: '操作日志',
            },
          ],
        },
      },
      {
        path: 'detail/:id',
        name: 'project-log-manage-detail',
        component: () => import('@/views/project-modules/operations-management/project-log-manage/detail/index.vue'),
        meta: {
          header: [
            {
              name: '项目空间管理',
              path: '/project-space',
            },
            {
              name: '运营运维',
            },
            {
              name: '操作日志',
              path: '/project-log-manage',
              query: {
                tab: 'activityLog',
              },
            },
            {
              name: '操作日志详情',
            },
          ],
        },
      },
    ],
  },
];
