<template>
  <a-modal v-model:open="open" title="导出服务调用量明细表" width="500px" :body-style="{ 'margin-top': '32px' }" :mask-closable="false" @ok="handleOk" @cancel="handleCancel">
    <a-form ref="formRef" :model="form" :rules="formRules" :label-col="{ span: 5 }" :wrapper-col="{ span: 18, offset: 1 }" autocomplete="off">
      <a-form-item label="创建起止时间" name="time">
        <a-range-picker :locale="locale" style="width: 100%" v-model:value="form.time" show-time :disabled-date="disabledDate" format="YYYY-MM-DD HH:mm:ss" />
      </a-form-item>
      <a-form-item class="multiple-item" label="运行时长" required>
        <div class="select-item">
          <a-form-item name="runtimeComparator">
            <a-select v-model:value="form.runtimeComparator" placeholder="请选择" class="left-select" :options="symbolOptions" :get-popup-container="(el) => el.parentNode"></a-select>
          </a-form-item>
          <a-form-item name="runningTime">
            <a-input-number v-model:value="form.runningTime" placeholder="请输入" :controls="false" :min="0" :precision="0" class="input-number">
              <template #addonAfter>
                <span class="unit">小时</span>
              </template>
            </a-input-number>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item class="multiple-item" label="服务调用量" required>
        <div class="select-item">
          <a-form-item name="callCountComparator">
            <a-select v-model:value="form.callCountComparator" placeholder="请选择" class="left-select" :options="symbolOptions" :get-popup-container="(el) => el.parentNode"></a-select>
          </a-form-item>
          <a-form-item name="callCount">
            <a-input-number v-model:value="form.callCount" placeholder="请输入" :controls="false" :min="0" :precision="0" class="input-number"></a-input-number>
          </a-form-item>
        </div>
      </a-form-item>
      <a-form-item label="所属资源组" name="resourceGroupIds">
        <a-select v-model:value="form.resourceGroupIds" mode="multiple" placeholder="请选择" show-search show-arrow :filter-option="filterResourceOption" :max-tag-count="2" :options="resGroupList" :field-names="{ label: 'text', value: 'value' }" :get-popup-container="(el) => el.parentNode" @change="handleResGroupChange">
          <template #dropdownRender="{ menuNode: menu }">
            <v-nodes :vnodes="menu" />
            <a-divider style="margin: 0px" />
            <a-checkbox v-model:checked="resGroupChecked" style="padding-left: 12px; margin: 8px 0px" @change="handleResGroupSelectAll">全选</a-checkbox>
          </template>
          <template #maxTagPlaceholder="omittedValues">
            <a-tooltip placement="top">
              <span>+{{ omittedValues.length }} ...</span>
              <template #title>
                {{ omittedValues.map((item) => item.label).join('，') }}
              </template>
            </a-tooltip>
          </template>
        </a-select>
      </a-form-item>
      <a-form-item label="项目空间名称" name="projectIds">
        <a-select v-model:value="form.projectIds" mode="multiple" placeholder="请选择" show-search show-arrow :filter-option="filterProjectOption" :max-tag-count="2" :options="projectOptions" :field-names="{ label: 'name', value: 'id' }" :get-popup-container="(el) => el.parentNode" @change="handleProjectChange">
          <template #dropdownRender="{ menuNode: menu }">
            <v-nodes :vnodes="menu" />
            <a-divider style="margin: 0px" />
            <a-checkbox v-model:checked="projectChecked" style="padding-left: 12px; margin: 8px 0px" @change="handleProjectSelectAll">全选</a-checkbox>
          </template>
          <template #maxTagPlaceholder="omittedValues">
            <a-tooltip placement="top">
              <span>+{{ omittedValues.length }} ...</span>
              <template #title>
                {{ omittedValues.map((item) => item.label).join('，') }}
              </template>
            </a-tooltip>
          </template>
        </a-select>
      </a-form-item>
      <a-form-item label="服务类型" name="serviceType">
        <a-select v-model:value="form.serviceType" mode="multiple" placeholder="请选择" show-arrow :options="serviceOptions" :get-popup-container="(el) => el.parentNode"></a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { defineProps, defineEmits, reactive, ref } from 'vue';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
dayjs.locale('zh-cn');
import { getProjectSpaceAllList } from '@/apis/operationLog';
import { exportFilePost } from '@/utils/file';

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

const props = defineProps({
  resGroupList: {
    type: Array,
    default: () => [],
  },
});

const formRef = ref();

const open = defineModel('open', { required: true, default: false });

const form = reactive({
  time: [],
  runtimeComparator: null,
  runningTime: '',
  callCountComparator: null,
  callCount: '',
  resourceGroupIds: undefined,
  projectIds: undefined,
  serviceType: undefined,
});

const resGroupChecked = ref(false);
const projectChecked = ref(false);

const symbolOptions = [
  { label: '大于等于', value: 'gte' },
  { label: '小于', value: 'lt' },
];

const projectOptions = ref([]);

const serviceOptions = [
  { label: '模型服务', value: 'model' },
  { label: '镜像服务', value: 'image' },
];
  
const disabledDate = (current) => {
  return current && current > dayjs().endOf('minute');
};

const filterResourceOption = (input, option) => {
  return option.text.indexOf(input) >= 0;
};

const filterProjectOption = (input, option) => {
  return option.name.indexOf(input) >= 0;
};

const validateRunning = async () => {
  if (!form.runtimeComparator) {
    return Promise.reject('请选择范围');
  } else if (!form.runningTime) {
    return Promise.reject('请输入运行时长');
  } else {
    return Promise.resolve();
  }
};

const validateCount = async () => {
  if (!form.callCountComparator) {
    return Promise.reject('请选择范围');
  } else if (!form.callCount) {
    return Promise.reject('请输入服务调用量');
  } else {
    return Promise.resolve();
  }
};

const formRules = reactive({
  time: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  runtimeComparator: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  runningTime: [
    { message: '请输入', required: true, trigger: ['blur', 'change'] },
  ],
  callCountComparator: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  callCount: [
    { message: '请输入', required: true, trigger: ['blur', 'change'] },
  ],
  resourceGroupIds: [{ required: true, message: '请选择', trigger: ['change'] }],
  projectIds: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  serviceType: [{ required: true, message: '请选择', trigger: ['change'] }],
});

const handleResGroupChange = () => {
  if (form.resourceGroupIds.length === props.resGroupList.length) {
    resGroupChecked.value = true;
  } else {
    resGroupChecked.value = false;
  }
};

const handleResGroupSelectAll = () => {
  if (resGroupChecked.value) {
    form.resourceGroupIds = props.resGroupList.map((item) => item.value);
  } else {
    form.resourceGroupIds = [];
  }
};

const handleProjectChange = () => {
  if (form.projectIds.length === projectOptions.value.length) {
    projectChecked.value = true;
  } else {
    projectChecked.value = false;
  }
};

const handleProjectSelectAll = () => {
  if (projectChecked.value) {
    form.projectIds = projectOptions.value.map((item) => item.id);
  } else {
    form.projectIds = [];
  }
};

const handleCancel = () => {
  formRef.value.resetFields();
  resGroupChecked.value = false;
  projectChecked.value = false;
  open.value = false;
  form.value.runtimeComparator = null;
  form.value.callCountComparator = null;
};

const handleOk = async () => {
  formRef.value
    .validate()
    .then(() => {
      const params = {
        startTime: form.time[0],
        endTime: form.time[1],
        runtimeComparator: form.runtimeComparator,
        runningTime: form.runningTime * 3600,
        callCountComparator: form.callCountComparator,
        callCount: form.callCount,
        resourceGroupIds: form.resourceGroupIds,
        projectIds: form.projectIds,
        serviceType: form.serviceType,
      };
      exportFilePost({ url: '/web/admin/serving/v1/call/download', method: 'POST', params });
      handleCancel();
    })
    .catch((el) => {
      throw new Error(e);
    });
};

const getProjectList = async () => {
  const res = await getProjectSpaceAllList();
  if (res.code === 0) {
    projectOptions.value = res.data;
  } else {
    projectOptions.value = [];
  }
};

// 打开的时候默认选择全部
watch(
  () => open.value,
  () => {
    if (open.value) {
      resGroupChecked.value = true;
      projectChecked.value = true;
      form.resourceGroupIds = props.resGroupList.map((item) => item.value);
      form.projectIds = projectOptions.value.map((item) => item.id);
    }
  }
);

onMounted(() => {
  getProjectList();
});
</script>

<style lang="less" scoped>
:deep .ant-form-item {
  margin-bottom: 32px;
}
.multiple-item {
  margin-bottom: 0;
}
.select-item {
  display: flex;
  .left-select {
    width: 120px;
    :deep .ant-select-selector {
      width: 120px;
    }
  }
  :deep .ant-form-item {
    margin-bottom: 32px;
  }
}
.unit {
  color: rgba(0, 20, 26, 0.45);
}
.input-number {
  width: 220px;
}
:deep .ant-form-item .ant-form-item-label > label::after {
  margin-inline-end: 0;
}
:deep .ant-select-multiple .ant-select-selection-overflow-item {
  max-width: 120px;
}
:deep .ant-input-number-group .ant-input-number-group-addon {
  border-radius: 0;
}
</style>
