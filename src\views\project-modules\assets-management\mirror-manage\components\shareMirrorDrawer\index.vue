<template>
  <a-drawer class="share-mirror-drawer" :open="visible" :body-style="{ paddingTop: '9px', paddingBottom: '0' }" width="1028" title="分享镜像" destroy-on-close @close="handleCancel">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="share" tab="镜像文件分享">
        <share-detail ref="shareDetailRef" :mirror-group-info="mirrorGroupInfo" @change="fileChange" />
      </a-tab-pane>
      <a-tab-pane key="cancel" tab="分享白名单管理">
        <cancel-share-detail :mirror-group-info="mirrorGroupInfo" ref="cancelShareDetailRef" @change="fileChange" />
      </a-tab-pane>
    </a-tabs>
    <template #footer>
      <a-flex style="height: 100%" justify="space-between" align="center">
        <div class="file-selected">
          已选镜像文件：<span class="num">{{ selectedFilesAll.length }}</span>
          <jt-icon v-if="selectedFilesAll.length >= 1" type="iconshanchu1" class="iconfont clear-file" @click.stop="clearFile"/>
        </div>
        <a-space>
          <a-button class="btn-share-cancel" @click="handleCancel">取消</a-button>
          <a-button v-if="activeKey === 'share'" class="kl-create-btn" type="primary" :loading="loading" @click="onSubmit">分享</a-button>
          <a-button v-else :class="!selectedFilesAll?.length ? 'drawer-share-disabled' : 'kl-create-btn'" :disabled="!selectedFilesAll?.length" type="primary" :loading="loading" @click="onCancelShare">移除分享</a-button>
        </a-space>
      </a-flex>
    </template>
  </a-drawer>
</template>
<script setup>
import shareDetail from './shareDetail.vue';
import cancelShareDetail from './cancelShareDetail.vue';
import { shareImage, cancelShareImage } from '@/apis/mirrorManage';
import { message } from 'ant-design-vue';
import ErrCode from '@/constants/errorCode';
import { imageProjectNotExistCode } from '@/constants/image';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  mirrorGroupInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['close']);

const activeKey = ref('share');
const shareDetailRef = ref();
const cancelShareDetailRef = ref();
const selectedFilesAll = ref([]);
const loading = ref(false);

const handleCancel = () => {
  emit('close');
  activeKey.value = 'share';
}

const fileChange = (files) => {
  console.log(files, 'files==fileChange');
  selectedFilesAll.value = [ ...files ];
};

const clearFile = () => {
  if (activeKey.value === 'share') {
    shareDetailRef.value?.clearSelectedFiles();
    return;
  }
  cancelShareDetailRef.value?.clearSelectedFiles();
};

const onSubmit = () => {
  shareDetailRef.value.validateFields().then(async (res) => {
    if (res) {
      loading.value = true;
      try {
        const { selectedFiles, objectProject } = res;
        if (selectedFiles?.length && objectProject) {
          const { code } = await shareImage({
            shareImageId: selectedFiles?.[0],
            sharedProjectId: objectProject,
          });
          loading.value = false;
          if (code === 0) {
            message.success({
              content: '镜像文件分享成功',
              duration: 1,
              onClose: () => {
                activeKey.value = 'cancel';
                cancelShareDetailRef.value?.getShareList();
              },
            });
          } else if (code === imageProjectNotExistCode) { // 项目不存在
            shareDetailRef.value.projectNotExist();
          } else {
            message.error(ErrCode[code] || '镜像文件分享失败，请稍后重试');
          }
        }
      } catch (e) {
        loading.value = false;
        throw new Error(e);
      }
    }
  });
};

const onCancelShare = async () => {
  loading.value = true;
  try {
    const { code } = await cancelShareImage({ ids: selectedFilesAll.value });
    loading.value = false;
    if (code === 0) {
      message.success({
        content: '镜像文件移除分享成功',
        duration: 1,
        onClose: () => {
          cancelShareDetailRef.value?.resetParams();
          cancelShareDetailRef.value?.getShareList();
          clearFile();
        },
      });
    } else {
      message.error('镜像文件移除分享失败，请稍后重试');
    }
  } catch (e) {
    loading.value = false;
    throw new Error(e);
  }
};

// 切换tab时清空数据
watch(
  () => activeKey.value,
  () => {
    clearFile();
    shareDetailRef.value?.clearProjectId();
    selectedFilesAll.value = [];
  },
);
</script>
<style lang="less" scoped>
.file-selected {
  color: @jt-text-color-primary-opacity07;
  .num {
    color: @jt-primary-color;
  }
  .clear-file {
    margin-left: 12px;
    cursor: pointer;
    color: rgba(0, 20, 26, 0.45);
    font-size: 14px;
    &:hover {
      color: @jt-primary-color;
    }
  }
}
</style>
<style lang="less">
.share-mirror-drawer {
  .drawer-share-disabled {
    color: @jt-color-white;
    border-color: transparent;
    background: rgba(0, 20, 26, 0.15);
  }
  .btn-share-cancel {
    box-shadow: none;
  }
}
</style>