<template>
  <div>
    <a-configProvider>
      <template #renderEmpty>
        <jt-empty :title="'镜像'" :show-operation="emptyShowOperation">
          <template #description> 立即<a-button type="link" ghost @click="uploadMirror">导入镜像</a-button> </template>
        </jt-empty>
      </template>
      <a-table :columns="typeAColumns" :scroll="{ x: 1160 }" :data-source="typeADataShow" :pagination="false" :loading="tableALoading" row-key="id" @change="tableAChange">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'imageGroupName'">
            <span>
              <a-tooltip placement="topLeft">
                <template #title>
                  <span>{{ record.source === 0 ? record.fileName : text }}</span>
                </template>

                <span class="location-span">{{ record.source === 0 ? record.fileName : text }}</span>
              </a-tooltip>
            </span>
          </template>
          <template v-else-if="column.dataIndex === 'tags'">
            <div class="label-box">
              <a-tooltip v-for="item in text" :key="item" placement="top">
                <template #title>
                  <span>{{ item }}</span>
                </template>
                <div class="label-item">{{ item }}</div>
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'totalSize'">
            <span>{{ getSize(record) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'updateTime'">
            <span>{{ getTime(text) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'source'">
            <span>{{ getSource(text) }}</span>
          </template>

          <template v-else-if="column.dataIndex === 'status'">
            <span class="location-span">{{ getStatus(record) }}</span>
            <a-tooltip v-if="record.status < 0" placement="top">
              <template #title>
               <div>{{ getImportMsgByBackendStatus(record.source, record.status).message }}</div>
              </template>
              <jt-icon type="iconquestion-circle" class="status-icon" />
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'progress'">
            <div class="progress-box">
              <div class="time-box">{{ getDuringTime(record) }}</div>
              <a-progress :percent="text" :status="getProgessStatus(record)" :stroke-color="getStrokeColor(record)"/>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <span class="operation">
              <span v-if="record.source === 0 && record.status < 0" class="operation-item" @click="retry(record)">
                <jt-icon type="iconshuaxin" class="operation-icon"></jt-icon>
                <span class="operation-text">重试</span>
              </span>
              <span class="operation-item" @click="deleteMirror(record)">
                <jt-icon type="iconshanchu1" class="operation-icon"></jt-icon>
                <span class="operation-text">删除</span>
              </span>
            </span>
          </template>
        </template>
      </a-table>
    </a-configProvider>

    <jt-pagination v-if="typeACount > 0" :page-num="typeACurrentPage" :page-size="typeAPageSize" :total="typeACount" @changePageNum="onTypeAPageChange" @changePageSize="onTypeAShowSizeChange"></jt-pagination>

    <div v-if="deleteVisible">
      <delete-mirror-modal :delete-visible="deleteVisible" :delete-mirror-info="deleteMirrorInfo" @childFn="closeModal"> </delete-mirror-modal>
    </div>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import moment from 'moment';
import deleteMirrorModal from './deleteMirror.vue';
import { getFormatDuringTime, getFormatTime } from '@/utils';
import { dataToSizeConversion } from '@/utils/storage';
import { mirrorManageApi } from '@/apis';
import { IMPORT_IMAGE_STATUS, getImportMsgByBackendStatus } from '@/constants/image';

const LOAD_TYPE = {
  INIT: 'init',
  NO_LOADING: 'noLoading'
};

export default {
  components: {
    deleteMirrorModal
  },
  props: {
    searchText: {
      type: String,
      default: '',
    },
  },
  emits: ['createNew', 'getCount'],
  data() {
    return {
      mirrorId: this.$route.params.id,
      emptyShowOperation: true,
      typeAColumns: [
        {
          title: '镜像文件ID',
          dataIndex: 'imageGroupName',
          key: 'imageGroupName',
          ellipsis: true,
          width: 120,
        },
        {
          title: '镜像标签',
          dataIndex: 'tags',
          key: 'tags',
          width: 120,
        },
        // {
        //   title: '镜像来源',
        //   dataIndex: 'source',
        //   key: 'source',
        //   width: 2,
        //   filters: [
        //     {
        //       value: 0,
        //       text: '开发环境保存',
        //     },
        //     {
        //       value: 1,
        //       text: '对象存储导入',
        //     }
        //   ],
        // },
        {
          title: '大小',
          dataIndex: 'totalSize',
          key: 'totalSize',
          width: 100,
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime',
          key: 'updateTime',
          sorter: true,
          width: 150,
        },
        {
          title: '上传人',
          dataIndex: 'createBy',
          key: 'createBy',
          width: 110,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 80,
        },
        {
          title: '进度',
          dataIndex: 'progress',
          key: 'progress',
          width: 120,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          width: 180,
          fixed: 'right',
        },
      ],
      typeADataShow: [],
      typeAPageSize: 5,
      typeACount: 0,
      typeACurrentPage: 1,
      tableALoading: false,
      updateTimeDesc: true,
      // sourceList: [],

      timer: null,

      deleteVisible: false,
      deleteMirrorInfo: {},

      devStatusList: [
        {
          value: IMPORT_IMAGE_STATUS.COMPLETED.code,
          label: '完成',
        },
        {
          value: IMPORT_IMAGE_STATUS.IMPORTING.code,
          label: '创建中',
        },
      ],
      objStatusList: [
        {
          value: IMPORT_IMAGE_STATUS.COMPLETED.code,
          label: '完成',
        },
        {
          value: IMPORT_IMAGE_STATUS.IMPORTING.code,
          label: '导入中',
        },
      ],
      sourceMap: {
        0: '对象存储导入',
        1: '开发环境保存',
      },
      // 临时全量列表数据
      tempDataList: [],
      initList: [],
      getImportMsgByBackendStatus,
    };
  },
  watch: {
    searchText(val) {
      this.emptyShowOperation = val ? false : true;
      this.typeACurrentPage = 1;
      this.getTableInfo();
    },
    tempDataList: {
      handler(val) {
        this.$store.commit('UPDATE_MIRROR_UPLOADING_LIST', val);
      },
      deep: true,
    }
  },
  created() {
    this.initList = this.$store.state.mirrorUploadingList || JSON.parse(localStorage.getItem('mirrorUploadingList')) || [];
    // 发起数据初始化的请求；
    this.getTableInfo(LOAD_TYPE.INIT);
  },
  beforeUnmount() {
    clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    getTime(value) {
      return getFormatTime(value);
    },
    getSource(value) {
      if (value === undefined) return '--';
      return this.sourceMap[value] || '--';
    },
    getDuringTime(record) {
      if (record.createTime === undefined || record.status < 0) return '--';
      const create = moment(record.createTime);
      const now = moment(new Date());
      const sub = parseInt((now - create) / 1000);
      const formatTime = getFormatDuringTime(sub);
      return formatTime;
    },
    createTimerByFileStatus() {
      const hasUploadingFiles = this.tempDataList.some(item => item.status === IMPORT_IMAGE_STATUS.IMPORTING.code);
      if (this.tempDataList?.length !== 0 && hasUploadingFiles) {
        this.timer = setInterval(() => {
          this.getTableInfo(LOAD_TYPE.NO_LOADING);
        }, 10000);
      }
    },
    getTableInfo(param) {
      const sendData = {
        imageGroupId: this.mirrorId,
        status: [IMPORT_IMAGE_STATUS.NOT_DELETION_AND_NOT_SUCCESS.code],
        // sourceList: this.sourceList,
        searchField: this.searchText,
        page: this.typeACurrentPage,
        pageSize: this.typeAPageSize,
        updateTimeDesc: this.updateTimeDesc,
      };
      if (param !== LOAD_TYPE.NO_LOADING) {
        this.tableALoading = true;
      }
      mirrorManageApi.getMirrorList(sendData).then((res) => {
        this.tableALoading = false;
        if (res.code === 0) {
          // 初始化数据进度赋值
          if (param === LOAD_TYPE.INIT) {
            if (res.data.data.length === 0) {
              this.typeADataShow = [];
            } else {
              this.typeADataShow = res.data.data.map(item => {
                return {
                  ...item,
                  progress: this.getPercent(item, this.initList, param),
                }
              })
            }
            // 更新与保存临时数据
            this.tempDataList = [...this.typeADataShow];
          } else {
            if (res.data.data.length === 0) {
              this.typeADataShow = [];
            } else {
              // 用上一次保存的临时数据为进度赋值
              this.typeADataShow = res.data.data.map(item => {
                return {
                  ...item,
                  progress: this.getPercent(item, this.tempDataList, param),
                }
              })
            }
            // 搜索模式下临时数据的更新，需要考虑到搜索没有匹配到的那部分数据
            if (this.searchText) {
              this.tempDataList = this.concatList(this.typeADataShow, this.tempDataList, param);
            } else {
              this.tempDataList = [...this.typeADataShow];
            }
          }
          this.typeACount = res.data.total;
          if (param === LOAD_TYPE.NO_LOADING || this.searchText === '') {
            this.$emit('getCount');
          }
          // 根据镜像文件的状态处理定时器
          !this.timer && this.createTimerByFileStatus();
          this.clearTimerByFileStatus();
        }
      });
    },
    clearTimerByFileStatus() {
      const hasUploadingFiles = this.tempDataList.some(item => item.status === IMPORT_IMAGE_STATUS.IMPORTING.code);
      if (this.tempDataList?.length === 0 || !hasUploadingFiles) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    retry(record) {
      mirrorManageApi.getVersion({ imageGroupId: this.mirrorId }).then((res) => {
        if (res.code == 0) {
          const version = res.data;
          const { imageGroupId, imageGroupName, filePath, fileName, tags, description, totalSize } = record;
          const sendData = {
            imageGroupId,
            imageGroupName,
            version,
            tags,
            filePath,
            fileName,
            description,
            totalSize,
          };
          mirrorManageApi.uploadMirror(sendData).then((res) => {
            if (res.code === 0) {
              this.getTableInfo();
            } else {
              message.error(res.msg || '重试失败，请稍后再试');
            }
          });
        } else {
          message.error(res.msg || '重试失败，请稍后再试');
        }
      });
    },
    getSize(record) {
      if (record.source === 1) return '-';
      if (record.totalSize === undefined) return '-';
      const { sizeTxt, units } = dataToSizeConversion(record.totalSize, 2);
      return `${sizeTxt}${units}`;
    },
    uploadMirror() {
      this.$emit('createNew');
    },
    getPercent(record, list, param) {
      if (record.status == IMPORT_IMAGE_STATUS.COMPLETED.code) return 100;
      if (record.status < 0) {
        const temp = list.filter(item => item?.id === record.id);
        if (temp.length > 0) return temp[0].progress || 50;
        return 50;
      }
      if (record.status == IMPORT_IMAGE_STATUS.IMPORTING.code) {
        const temp = list.filter(item => item?.id === record.id);
        return this.calculateStep(temp, param);
      }
    },
    concatList(dataList, tempList, param) {
      const temp = [...dataList];
      let temp1 = [];
      if (tempList.length > 0) {
        for(const item of tempList) {
          const list = temp.filter(item1 => item1?.id === item.id);
          if (list.length === 0) {
            const tempItem = {
              ...item,
              progress: this.getPercent(item, tempList, param),
            };
            temp1.push({...tempItem});
          }
        }
      }
      return [...temp, ...temp1];
    },
    // 若是1M的文件，默认每次轮询增加10%的进度
    calculateStep(record, param) {
      const { totalSize, progress } = record[0] || {};
      const defaultStep = 10;
      const baseStep = 5; // 没有大小的镜像文件，默认每次轮询增加5%的进度
      if (!totalSize) { // 开发环境保存镜像没有totalSize
        if (record.length > 0 && progress) {
          return this.getRandom(progress, baseStep);
        }
        return this.getRandom(0, baseStep);
      }
      const ratio = totalSize / 1024 / 1024 / 1024;
      const sizeStep = Math.floor(defaultStep / (ratio < 1 ? 1 : ratio));
      if (record.length > 0 && progress) {
        return this.getRandom(progress, sizeStep);
      }
      return this.getRandom(0, sizeStep); 
    },
    getRandom(value, step) {
      const ran = Math.random();
      if (ran < 0.6) return value + step > 95 ? 95 : value + step;
      if (ran < 0.8) return value + step + 1 > 95 ? 95 : value + step + 1;
      return value + step + 2 > 95 ? 95 : value + step + 2;
    },
    getProgessStatus(record) {
      if (record.status == IMPORT_IMAGE_STATUS.COMPLETED.code) return '';
      if (record.status == IMPORT_IMAGE_STATUS.IMPORTING.code) return 'active';
      if (record.status < 0) return 'exception';
    },
    getStrokeColor(record) {
      if (record.status == IMPORT_IMAGE_STATUS.IMPORTING.code) return '#00a0cc';
    },
    getStatus(record) {
      const { source, status } = record;
      if (status < 0) return '失败';
      if (source == 0) {
        const obj = this.objStatusList?.find((item) => item.value == status);
        return obj?.label || '--';
      } else {
        const obj = this.devStatusList?.find((item) => item.value == status);
        return obj?.label || '--';
      }
    },

    closeModal(data) {
      this.deleteVisible = false;
      this.deleteMirrorInfo = {};
      if (data) {
        this.getTableInfo();
        this.$emit('getCount');
      }
    },
    deleteMirror(record) {
      this.deleteVisible = true;
      this.deleteMirrorInfo = record;
    },

    onTypeAShowSizeChange(pageSize) {
      this.typeAPageSize = pageSize;
      this.typeACurrentPage = 1;
      this.getTableInfo();
    },
    // 跳至xx页
    onTypeAPageChange(pageIndex) {
      this.typeACurrentPage = pageIndex;
      this.getTableInfo();
    },

    // table排序
    tableAChange(pagination, filters, sorter) {
      // this.sourceList = filters.source || []
      if (sorter.order) {
        this.updateTimeDesc = sorter.order === 'ascend' ? false : true;
      } else {
        this.updateTimeDesc = true;
      }
      this.typeACurrentPage = 1;
      this.getTableInfo();
    },
  },
};
</script>

<style lang="less" scoped>
.progress-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .time-box {
    margin-bottom: 0px;
  }
}
.label-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .label-item {
    margin-bottom: 4px;
    height: 20px;
    width: 100%;
    background: #eaf2ff;
    border-radius: 2px;
    border: 1px solid #99beff;
    line-height: 20px;
    font-size: 12px;
    font-weight: 400;
    color: #337dff;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.operation {
  display: flex;
  align-items: center;

  .operation-item {
    cursor: pointer;
    margin-right: 40px;
    display: flex;
    align-items: center;

    .operation-icon {
      color: @jt-primary-color;
      margin-right: 4px;
    }

    .disabled-gray {
      cursor: not-allowed;
      color: #a0a6ab;
    }
  }
}

.status-icon {
  margin-left: 6px;
  font-size: 16px;
  vertical-align: sub;
  color: @jt-text-color-primary-opacity05;
}

:deep(.ant-table-placeholder .ant-table-cell) {
  border-bottom: 0 !important;
}
</style>
