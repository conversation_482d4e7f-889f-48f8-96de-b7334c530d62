<template>
  <a-modal v-model="open" :title="null" :footer="null" class="stop-modal" @cancel="emit('close')">
    <div class="m-title"><jt-icon type="iconwarn-full" class="warn" />确定停止{{ TASK_TYPE_MSG[type] }}{{ taskInfo?.name }}吗？</div>
    <div v-if="type === TASK_TYPE.DEV" class="m-content">
      <p class="red">停止后，在运行镜像中安装的软件及依赖将丢失</p>
      <p>如需持久化保存，请先前往保存镜像</p>
      <p>如已保存过镜像，可直接停止</p>
    </div>
    <div v-else class="m-content">
      <p>停止后您可再次运行{{ TASK_TYPE_MSG[type] }}</p>
    </div>
    <div class="m-buttons">
      <a-space :size="8">
        <a-button danger @click.stop="stop(taskInfo)">停止</a-button>
        <a-button v-if="type === TASK_TYPE.DEV" type="primary" @click.stop="emit('save')">保存镜像</a-button>
        <a-button @click="emit('close')">取消</a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script setup>
import { defineProps, defineModel, defineEmits } from 'vue';
import { useStore } from 'vuex';
import { TASK_TYPE, TASK_TYPE_MSG, taskApiInfo } from '@/constants/trainTask';
import ErrorCode from '@/constants/errorCode';
import { message } from 'ant-design-vue';
import { requestWithProjectId } from '@/request';
const { GET } = requestWithProjectId;

const store = useStore();
const open = defineModel();
const props = defineProps({
  type: {
    type: String,
    default: TASK_TYPE.DEV,
  },
  taskInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['close', 'save', 'reload']);
// 停止开发环境或任务
const stop = () => {
  const { id, name } = props.taskInfo || {};
  store.dispatch('updateGlobalLoading', true);
  GET(taskApiInfo(props.type).stop, { id }).then((res) => {
    store.dispatch('updateGlobalLoading', false);
    emit('close');
    if (res.code === 0) {
      message.success(`${TASK_TYPE_MSG[props.type]}${name}停止中`);
      emit('reload');
    } else {
      message.error(ErrorCode[res.code] || `${TASK_TYPE_MSG[props.type]}${name}停止失败，请稍后再试`);
    }
  }).catch((err) => {
    store.dispatch('updateGlobalLoading', false);
    message.error(`${TASK_TYPE_MSG[props.type]}${name}停止失败，请稍后再试`);
  });
};
</script>

<style lang="less" scoped>
.stop-modal {
  .anticon {
    font-size: 20px;
    vertical-align: sub;
  }
  .warn {
    color: @jt-warn-color;
  }
  .red {
    color: red;
  }
  .m-title {
    font-size: @jt-font-size-lg;
    font-weight: @jt-font-weight-medium;
    .warn {
      margin-right: 10px;
    }
  }
  .m-content {
    margin: 10px 0 20px 30px;
    line-height: 25px;
  }
  .m-buttons {
    text-align: right;
  }
}
</style>
