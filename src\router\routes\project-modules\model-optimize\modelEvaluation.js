// 模型调优-模型评估
const MODEL_EVALUATION = [
  {
    path: '/model-evaluation',
    name: 'model-evaluation',
    redirect:  '/model-evaluation/auto',
  },
  {
    path: '/model-evaluation/auto',
    name: 'model-evaluation-list-auto',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/list/index.vue'),
  },
  {
    path: '/model-evaluation/manual',
    name: 'model-evaluation-list-manual',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/list/index.vue'),
  },
  {
    path: '/model-evaluation/create',
    name: 'model-evaluation-create',
    redirect: '/model-evaluation/create/auto'
  },
  
  {
    path: '/model-evaluation/create/auto',
    name: 'model-evaluation-create-auto',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/createTask.vue'),
  },
  {
    path: '/model-evaluation/create/manual',
    name: 'model-evaluation-create-manual',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/createTask.vue'),
  },
  {
    path: '/model-evaluation/detail',
    name: 'model-evaluation-detail',
    redirect: '/model-evaluation/detail/auto',
  },
  
  {
    path: '/model-evaluation/detail/auto',
    name: 'model-evaluation-detail-auto',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/details/index.vue'),
  },
  
  {
    path: '/model-evaluation/detail/manual',
    name: 'model-evaluation-detail-manual',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/details/index.vue'),
  },
  {
    path: '/model-evaluation/detail/manual/update',
    name: 'model-evaluation-detail-manual-update',
    component: () => import('@/views/project-modules/model-optimize/model-evaluation/details/evaluation-manual-update.vue'),
  },
];

export default MODEL_EVALUATION;
