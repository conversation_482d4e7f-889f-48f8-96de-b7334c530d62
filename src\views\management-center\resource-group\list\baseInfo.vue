<template>
  <div>
    <a-descriptions :label-style="descripLabelStyle">
      <a-descriptions-item label="节点名称">{{ nodeInfo?.nodeName }}</a-descriptions-item>
      <a-descriptions-item label="所属集群">{{ nodeInfo?.clusterName }}</a-descriptions-item>
      <a-descriptions-item label="节点状态">
        <span class="item-content">
          <nodeStatusTag :status="nodeInfo?.nodeStatus" /> 
        </span>
      </a-descriptions-item>
      <a-descriptions-item label="所属资源组">
        <span>
          <jt-tag v-if="nodeInfo.attribute" rounded="small" :color="nodeInfo.attribute === 'public' ? 'blue' : 'orange'">{{ nodeInfo.attribute === 'public' ? `公共` : '专属' }}</jt-tag>
          {{ nodeInfo?.groupName ? nodeInfo.groupName : '--' }}
        </span>
      </a-descriptions-item>
      <a-descriptions-item label="资源类型">{{ nodeInfo.resourceType === 'gpucard' ? '加速卡' : nodeInfo.resourceType === 'cpu' ? 'CPU' : '--' }}</a-descriptions-item>
      <a-descriptions-item v-if="nodeInfo.resourceType === 'gpucard'" label="加速卡类型">{{ nodeInfo?.gpuCardType ? nodeInfo?.gpuCardType : '--' }}</a-descriptions-item>
      <a-descriptions-item v-if="nodeInfo.resourceType === 'gpucard'" label="已用 / 总加速卡">{{ nodeInfo?.useGpuCard }} / {{ nodeInfo?.totalGpuCard }}卡</a-descriptions-item>
      <a-descriptions-item label="已用 / 总CPU">{{ nodeInfo?.useCpu }} / {{ nodeInfo?.totalCpu }}核</a-descriptions-item>
      <a-descriptions-item label="已用 / 总内存">{{ nodeInfo?.useMemory }} / {{ nodeInfo?.totalMemory }}GB</a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<script setup>
import { defineProps, ref, watch, defineEmits } from 'vue';
import nodeStatusTag from './components/nodeStatusTag.vue';

const props = defineProps({
  nodeInfo: {
    type: Object,
    default: () => {},
  },
});

const descripLabelStyle = {
  justifyContent: 'flex-end',
  minWidth: '120px',
};
</script>

<style lang="less" scoped>
:deep(.ant-descriptions-item-label) {
  color: rgba(0, 20, 26, 0.7);
}
.bianji-icon {
  color: #00a0cc;
}
</style>
