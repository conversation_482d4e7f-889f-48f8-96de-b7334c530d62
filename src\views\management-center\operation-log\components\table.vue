<template>
  <div class="tab-top">
    <a-space>
      <jt-search-input v-model:value="search.searchKey" placeholder="操作人/操作/操作对象" allow-clear style="width: 200px" />
      <a-button ghost type="primary" :disabled="isDisabled" @click="exportExcel">导出</a-button>
    </a-space>
  </div>
  <div class="table-search">
    <div class="table-left">
      <span class="table-title">日志查询</span>
      <span><jt-icon type="iconwarning-circle-fill" style="color: #ff8c19; margin-right: 9px" />仅保留近3个月内日志记录</span>
    </div>
    <a-space>
      <div class="space-select">
        <a-select v-model:value="search.eventResult" placeholder="操作结果" allow-clear style="width: 98px" :options="resultOptions" :get-popup-container="(el) => el.parentNode"></a-select>
        <a-select v-model:value="search.eventModule" mode="multiple" :max-tag-count="1" placeholder="操作模块" allow-clear style="width: 177px" show-arrow :options="modulesOptions" :get-popup-container="(el) => el.parentNode"></a-select>
        <a-select v-if="props.activeKey == '1'" v-model:value="search.projectId" placeholder="操作项目空间" show-search allow-clear style="width: 128px" :options="projectOptions" :filter-option="filterOption" :field-names="{ label: 'name', value: 'id' }" :get-popup-container="(el) => el.parentNode"></a-select>
      </div>
      <!-- <a-select v-model:value="rangePreset" style="width: 85px" :options="rangePresets" :get-popup-container="(el) => el.parentNode" @change="changePreset"></a-select> -->
      <!-- <a-range-picker v-model:value="rangeTime" show-time format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :allow-clear="false" :disabled="!rangePickerAvailable" :disabled-date="disabledDates" style="width: 300px" :get-popup-container="(el) => el.parentNode" @change="changeTimePicker"> -->
      <a-range-picker v-model:value="rangeTime" show-time format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm" :allow-clear="false" :disabled-date="disabledDates" style="width: 300px" :get-popup-container="(el) => el.parentNode" @change="changeTimePicker">
        <template #renderExtraFooter>
          <a-space>
            <a-button type="link" @click.stop="changePreset('lastDay')">近一天</a-button>
            <a-button type="link" @click.stop="changePreset('lastWeek')">近一周</a-button>
            <a-button type="link" @click.stop="changePreset('lastMonth')">近一月</a-button>
            <a-button type="link" @click.stop="changePreset('lastThreeMonth')">近三月</a-button>
          </a-space>
        </template>
      </a-range-picker>
    </a-space>
  </div>
  <a-configProvider>
    <template #renderEmpty>
      <jt-empty :title="emptyTitle" :show-operation="emptyShowOperation" />
    </template>
    <a-table :data-source="dataSource" :columns="columns" :loading="loading" :pagination="false" @change="handleTableChange">
      <template #bodyCell="{ column, record }">
        <!-- 操作时间 -->
        <template v-if="column.dataIndex === 'eventTime'">
          <!-- <a-tooltip placement="top" :title="record.eventTime"> -->
          <span class="link-id-name" @click="() => ToDetail(record)">{{ dayjs(record.eventTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
          <!-- </a-tooltip> -->
        </template>
        <!-- 操作结果 -->
        <template v-if="column.dataIndex === 'eventResult'">
          <jt-tag rounded="large" :color="OPE_TYPE_TAG_COLOR[record.eventResult]">{{ OPE_TYPE_MAP[record.eventResult] }}</jt-tag>
        </template>
        <!-- 操作对象 -->
        <template v-if="column.dataIndex === 'eventObject'">
          <a-tooltip :title="record.eventObject">
            <div class="txt-ellipsis">
              {{ record.eventObject || '--' }}
            </div>
          </a-tooltip>
        </template>
      </template>
    </a-table>
  </a-configProvider>
  <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
</template>

<script setup>
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { getEventLogs, getModulesList, getProjectSpaceAllList } from '@/apis/operationLog';
import { OPE_TYPE_MAP, OPE_TYPE_TAG_COLOR } from '@/constants/operationLog';
import { exportFilePost } from '@/utils/file.js';
import { debounce } from 'lodash';
import notification from '@/utils/notification';
import { onMounted, onBeforeUnmount, defineProps, ref, watch } from 'vue';
import { useStore } from 'vuex';
import { AUTH_CHECKS } from '@/constants/management-platform/poolManage';
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router';
import { checkResourceAuth } from '@/utils/auth';
const router = useRouter();
const route = useRoute();
const store = useStore();

const routeParam1 = computed(() => store.state.opeLog.params1);
const routeParam2 = computed(() => store.state.opeLog.params2);

const props = defineProps({
  activeKey: {
    type: String,
    default: '',
  },
  columns: {
    type: Array,
    default: () => [],
  },
});

const emptyTitle = ref('');
const loading = ref(false);
const rangePreset = ref('lastDay');
const rangePresets = [
  {
    label: '近一天',
    timeValue: [dayjs().add(-1, 'day').startOf('minute'), dayjs().startOf('minute')],
    value: 'lastDay',
  },
  {
    label: '近一周',
    timeValue: [dayjs().add(-1, 'week').startOf('minute'), dayjs().startOf('minute')],
    value: 'lastWeek',
  },
  {
    label: '近一月',
    timeValue: [dayjs().add(-30, 'day').startOf('minute'), dayjs().startOf('minute')],
    value: 'lastMonth',
  },
  {
    label: '近三月',
    timeValue: [dayjs().add(-90, 'day').startOf('minute'), dayjs().startOf('minute')],
    value: 'lastThreeMonth',
  },
  // {
  //   label: '近半年',
  //   timeValue: [dayjs().add(-180, 'day').startOf('minute'), dayjs().startOf('minute')],
  //   value: 'lastHalfYear',
  // },
  {
    label: '自定义',
    timeValue: [dayjs().add(-1, 'day').startOf('minute'), dayjs().startOf('minute')],
    value: 'custom',
    disabledDate: (current) => {
      return current && (current > dayjs().startOf('minute') || current < dayjs().add(-95, 'day').startOf('minute'));
    },
  },
];
const search = ref({
  searchKey: '',
  eventResult: null,
  eventModule: [],
  projectId: null,
});
const sort = ref(false);
const rangeTime = ref([dayjs().add(-1, 'day').startOf('minute'), dayjs().startOf('minute')]);
const rangePickerAvailable = computed(() => rangePreset.value === 'custom');

const emptyShowOperation = ref(true);
const paginationOptions = ref({
  total: 0,
  pageNum: 1,
  pageSize: 10,
});

const resultOptions = ref([
  { label: '成功', value: '1' },
  { label: '失败', value: '0' },
]);
const modulesOptions = ref([]);
const projectOptions = ref([]);
const dataSource = ref([]);
const isDisabled = ref(false);

const filterOption = (input, option) => {
  return option.name.indexOf(input) >= 0;
};

const disabledDates = (current) => {
  // const disabledDates = computed(() => {
  // const currentRangePreset = rangePresets.find((item) => item.value === rangePreset.value);
  // return currentRangePreset?.disabledDate;
  return current && (current > dayjs() || current < dayjs().add(-95, 'day').startOf('minute'));
};

const changePreset = (key) => {
  rangeTime.value = rangePresets.find((item) => item.value === key).timeValue;
  paginationOptions.value.pageNum = 1;
  getList();
};

const changeTimePicker = (date) => {
  rangeTime.value = date;
  paginationOptions.value.pageNum = 1;
  getList();
};

const getModules = async () => {
  const res = await getModulesList({ eventPlatform: props.activeKey });
  if (res.code === 0) {
    modulesOptions.value = [...new Set(res.data)]
      .filter((v) => {
        return AUTH_CHECKS.every((check) => checkResourceAuth(check.auth) || v !== check.value);
      })
      .map((item) => {
        return {
          label: item,
          value: item,
        };
      });
  } else {
    notification.error({
      message: `请求出错`,
      description: res.msg,
    });
  }
};

const getProjectSpace = async () => {
  const res = await getProjectSpaceAllList();
  if (res.code === 0) {
    projectOptions.value = res.data;
  } else {
    notification.error({
      message: `请求出错`,
      description: res.msg,
    });
  }
};

const isEnter = ref(false);

const getList = async () => {
  loading.value = true;
  const param = {
    eventPlatform: props.activeKey,
    startTime: dayjs(rangeTime.value[0]).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(rangeTime.value[1]).format('YYYY-MM-DD HH:mm:ss'),
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
    sortFiled: 'eventTime',
    isAsc: sort.value,
  };
  const res = await getEventLogs({ ...param, ...search.value });
  if (res.code === 0) {
    if (search.value.searchKey) {
      if (!res.data.data || res.data?.data.length === 0) {
        emptyShowOperation.value = false;
      } else {
        emptyShowOperation.value = true;
      }
    } else {
      emptyShowOperation.value = true;
    }
    dataSource.value = res.data.data;
    paginationOptions.value.total = res.data.total;
    if (dataSource.value.length === 0) {
      isDisabled.value = true;
    } else {
      isDisabled.value = false;
    }
  } else {
    dataSource.value = [];
    paginationOptions.value.total = 0;
    isDisabled.value = true;
    notification.error({
      message: `请求出错`,
      description: res.msg,
    });
  }
  loading.value = false;
  isEnter.value = false;
  const searchParams = {
    ...search.value,
    rangeTime: rangeTime.value,
    rangePreset: rangePreset.value,
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
  };
  if (props.activeKey === 1) {
    store.dispatch('updateParams1', searchParams);
  } else {
    store.dispatch('updateParams2', searchParams);
  }
};

const handleTableChange = (pagination, filters, sorter) => {
  if (Object.keys(sorter).length !== 0) {
    const order = sorter.order;
    sort.value = order === 'ascend' ? true : false;
  }
  paginationOptions.value.pageNum = 1;
  getList();
};

const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getList();
};

const changePageNum = (num) => {
  paginationOptions.value.pageNum = num;
  getList();
};

const exportExcel = () => {
  const param = {
    eventPlatform: props.activeKey,
    startTime: dayjs(rangeTime.value[0]).format('YYYY-MM-DD HH:mm:ss'),
    endTime: dayjs(rangeTime.value[1]).format('YYYY-MM-DD HH:mm:ss'),
    // pageNum: paginationOptions.value.pageNum,
    // pageSize: paginationOptions.value.pageSize,
    sortFiled: 'eventTime',
    isAsc: sort.value,
  };
  const params = { ...param, ...search.value };
  exportFilePost({ url: '/web/admin/eventrack/v1/event-log/export-event-log-detail', method: 'POST', params });
};

// 进入详情
const ToDetail = (record) => {
  const searchParams = {
    ...search.value,
    rangeTime: rangeTime.value,
    rangePreset: rangePreset.value,
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
  };
  if (props.activeKey === 1) {
    store.dispatch('updateParams1', searchParams);
  } else {
    store.dispatch('updateParams2', searchParams);
  }
  const path = props.activeKey == 1 ? 'space-detail' : 'manage-detail';
  router.push({
    path: `/operation-log/${path}/${props.activeKey}/${record.id}`,
  });
};

watch(
  () => props.activeKey,
  (val) => {
    if (val === 1) {
      emptyTitle.value = '项目空间操作日志信息';
      getProjectSpace();
    } else {
      emptyTitle.value = '运管中心操作日志信息';
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

watch(
  () => search.value,
  debounce(() => {
    if (!isEnter.value) {
      paginationOptions.value.pageNum = 1;
    }
    getList();
  }, 1000),
  {
    deep: true,
    // immediate: true,
  }
);

onMounted(() => {
  loading.value = true;
  getModules();
  isEnter.value = true;
  if (props.activeKey === 1) {
    if (Object.keys(routeParam1.value).length !== 0) {
      search.value.searchKey = routeParam1.value?.searchKey || search.value.searchKey;
      search.value.eventResult = routeParam1.value?.eventResult || search.value.eventResult;
      search.value.eventModule = routeParam1.value?.eventModule || search.value.eventModule;
      search.value.projectId = routeParam1.value?.projectId || search.value.projectId;
      rangeTime.value = routeParam1.value?.rangeTime || rangeTime.value;
      rangePreset.value = routeParam1.value?.rangePreset || rangePreset.value;
      paginationOptions.value.pageSize = routeParam1.value?.pageSize || paginationOptions.value.pageSize;
      paginationOptions.value.pageNum = routeParam1.value?.pageNum || paginationOptions.value.pageNum;
    } else {
      getList();
    }
  }
  if (props.activeKey === 2) {
    if (Object.keys(routeParam2.value).length !== 0) {
      search.value.searchKey = routeParam2.value?.searchKey || search.value.searchKey;
      search.value.eventResult = routeParam2.value?.eventResult || search.value.eventResult;
      search.value.eventModule = routeParam2.value?.eventModule || search.value.eventModule;
      search.value.projectId = routeParam2.value?.projectId || search.value.projectId;
      rangeTime.value = routeParam2.value?.rangeTime || rangeTime.value;
      rangePreset.value = routeParam2.value?.rangePreset || rangePreset.value;
      paginationOptions.value.pageSize = routeParam2.value?.pageSize || paginationOptions.value.pageSize;
      paginationOptions.value.pageNum = routeParam2.value?.pageNum || paginationOptions.value.pageNum;
    } else {
      getList();
    }
  }
});

onBeforeRouteLeave((to, from, next) => {
  if (to.name !== 'space-log-detail' && to.name !== 'manage-log-detail') {
    store.dispatch('updateParams1', {});
    store.dispatch('updateParams2', {});
  }
  next();
});
</script>

<style lang="less" scoped>
.tab-top {
  position: absolute;
  right: 0;
  top: -60px;
}
.table-search {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  .table-left {
    line-height: 32px;
    font-size: 14px;
    color: rgba(0,20,26,0.7);
    .table-title {
      min-width: 70px;
      font-weight: 600;
      font-size: 16px;
      color: #00141a;
      margin-right: 13px;
    }
  }
  .space-select {
    display: flex;
    margin-right: 2px;
    :deep .ant-select {
      margin-right: -2px;
    }
  }
  .time-select {
    :deep .ant-select {
      margin-right: -1px;
    }
  }
}
.link-id-name {
  cursor: pointer;
  &:hover {
    color: @jt-primary-color;
  }
}
.txt-ellipsis {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep .ant-picker.ant-picker-disabled {
  background-color: rgba(0, 20, 26, 0.02);
}
:deep .ant-select-multiple .ant-select-selection-overflow-item {
  max-width: 80px;
}
</style>
