<template>
  <div>
    <a-configProvider>
      <template #renderEmpty>
        <jt-empty :show-operation="!keyword" title="资源池"> </jt-empty>
      </template>
      <a-table :columns="columns" :scroll="{ x: '100% ' }" :data-source="pooldatas" :loading="loading" :pagination="false" row-key="id" @change="tableChange">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'poolName'">
            <a-tooltip placement="top" :title="record.name">
              <span class="link-id-name expansion-text-ellipsis" @click="() => handleToDetail(record)">{{ record.poolName }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="['cluster', 'gpuCardType'].includes(column.dataIndex)">
            <a-tooltip>
              <template #title>{{ text }}</template>
              <span class="ellipsis-text">{{ text }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'gpuCardNodeUse'">
            <span>{{ showText(text) }}</span
            > / <span>{{ showText(record.gpuCardNodeTotal) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'gpuCardUse'">
            <span>{{ showText(text) }}</span
            > / <span>{{ showText(record.gpuCardTotal) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'cpuNodeUse'">
            <span>{{ showText(text) }}</span
            > / <span>{{ showText(record.cpuNodeTotal) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'cpuUse'">
            <span>{{ showText(text) }}</span
            > / <span>{{ showText(record.cpuTotal) }}</span>
          </template>
          <span v-else>{{ text }}</span>
        </template>
      </a-table>
    </a-configProvider>
    <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { resourcePoolApi } from '@/apis';
import ErrorCode from '@/constants/errorCode';
import { POOL_MANAGE_TABS, RESOURCE_TYPE_FILTERS, RESOURCE_POOL_SORT_OBJ } from '@/constants/management-platform/poolManage.js';

const router = useRouter();
const route = useRoute();

const props = defineProps({
  searchText: { type: String, default: '' },
  reload: {
    type: Boolean,
  },
});
const { pageNum, pageSize, sortField, isAsc, gpuCardStatus, resourceStatus } = route.query;

const gpuCardType = ref(gpuCardStatus?.split(',') || undefined);
const resourceType = ref(resourceStatus?.split(',') || undefined);
const keyword = ref(props.searchText);

const tableParams = ref({});
const gpuCards = ref([]);
const pooldatas = ref([]);
const paginationOptions = ref({
  total: 0,
  pageSize: pageSize ? Number(pageSize) : 10,
  pageNum: pageNum ? Number(pageNum) : 1,
});
const loading = ref(false);

const columns = computed(() => {
  return [
    {
      title: '资源池名称',
      dataIndex: 'poolName',
      key: 'poolName',
      width: '134px',
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '集群',
      dataIndex: 'cluster',
      key: 'cluster',
      width: '180px',
      ellipsis: true,
    },
    {
      title: '资源类型',
      dataIndex: 'resourceType',
      key: 'resourceType',
      width: '132px',
      ellipsis: true,
      filters: RESOURCE_TYPE_FILTERS,
    },
    {
      title: '加速卡类型',
      dataIndex: 'gpuCardType',
      key: 'gpuCardType',
      width: '248px',
      ellipsis: true,
      filters: gpuCards.value,
    },
    {
      title: '加速卡节点数',
      dataIndex: 'gpuCardNodeUse',
      key: 'gpuCardNodeUse',
      width: '146px',
      sorter: true,
      // defaultFilteredValue: statusFilter.value,
    },
    {
      title: '加速卡数',
      dataIndex: 'gpuCardUse',
      key: 'gpuCardUse',
      width: '126px',
      sorter: true,
    },
    {
      title: 'CPU节点数',
      dataIndex: 'cpuNodeUse',
      key: 'cpuNodeUse',
      width: '150px',
      sorter: true,
    },
    {
      title: 'CPU核数',
      dataIndex: 'cpuUse',
      key: 'cpuUse',
      width: '121px',
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: '170px',
      sorter: true,
      // customRender({ text }) {
      //   return text || '--';
      // },
    },
  ];
});

const showText = (text) => {
  if (text >= 0) return text;
  return '-';
};

if (sortField) {
  tableParams.value.sortField = sortField;
  tableParams.value.sortOrder = isAsc === 'ascend' ? 'asc' : 'desc';
}

const tableChange = (pagination, filters, sorter) => {
  gpuCardType.value = filters.gpuCardType || undefined;
  resourceType.value = filters.resourceType || undefined;
  if (sorter.order) {
    tableParams.value.sortField = RESOURCE_POOL_SORT_OBJ[sorter.field];
    tableParams.value.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
  } else {
    tableParams.value.sortField = undefined;
    tableParams.value.sortOrder = undefined;
  }
  changePageNum(1);
};

const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getTableData();
};
const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getTableData();
};

const handleToDetail = (record) => {
  const query = {
    category: record.category,
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
    title: record.poolName,
    id: record.id,
    tab: POOL_MANAGE_TABS.POOLLIST,
  };
  if (tableParams.value.sortField) {
    query.sortField = tableParams.value.sortField;
    query.isAsc = tableParams.value.sortOrder ? 'ascend' : 'descend';
  }
  router.push({
    path: `/resource-pool-manage/pool-detail/${record.id}`,
    query,
  });
};

const getTableData = async () => {
  try {
    loading.value = true;
    const param = {
      pageNum: paginationOptions.value.pageNum,
      pageSize: paginationOptions.value.pageSize,
      keyWord: keyword.value,
      gpuCardType: gpuCardType.value,
      resourceType: resourceType.value,
      ...tableParams.value,
    };
    const res = await resourcePoolApi.getResourcePoolList(param);
    if (res.code === 0 && res.data?.data) {
      pooldatas.value = res.data.data || [];
      paginationOptions.value.total = res.data.total;
    } else {
      message.error(ErrorCode[res.code] || ErrorCode[202503001]);
    }
  } catch (error) {
    message.error(ErrorCode[202503001]);
    throw new Error(error);
  } finally {
    loading.value = false;
  }
};

const getGpuCards = async () => {
  const res = await resourcePoolApi.getGPUCardsByPoolId({});
  if (res.code === 0 || res.data) {
    gpuCards.value = res.data.map((card) => {
      return {
        text: card.gpuCardTypeName,
        value: card.gpuCardTypeName,
      };
    });
  } else {
    message.error(ErrorCode[res.code] || ErrorCode[202503005]);
  }
};

const getTableList = () => {
  changePageNum(1);
};

watch(
  () => props.searchText,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      keyword.value = newValue;
      changePageNum(1);
    }
  }
);
watch(
  () => props.reload,
  () => {
    changePageNum(1);
  }
);

onMounted(() => {
  getTableData();
  getGpuCards();
});

defineExpose({
  getTableList,
});
</script>

<style lang="less" scoped>
.expansion-text-ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.link-id-name {
  cursor: pointer;
  &:hover {
    color: @jt-primary-color;
  }
}
.time {
  width: 148px;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
</style>
