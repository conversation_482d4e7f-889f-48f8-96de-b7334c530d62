<template>
  <a-table row-key="id" :pagination="paginationOptions" :selections="onSelectChange" :row-selection="{ type: 'radio', selectedRowKeys: selectedRowKeys,  onChange: onSelectChange }" :columns="columns" :data-source="tableData" :loading="tableLoading" @change="onTableChange">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'imageId'">
        <a-tooltip placement="top" :title="record.imageId">
          <span class="ellipsis-text cursor">
            {{ record.imageId }}
          </span>
        </a-tooltip>
      </template>
      <template v-if="column.dataIndex === 'totalSize'">
        <span>{{ getSize(record.totalSize) }}</span>
      </template>
    </template>
    <template #emptyText>
      <light-empty title="暂无镜像文件" />
    </template>
  </a-table>
</template>

<script setup>
import { dataToSizeConversion } from '@/utils/storage';
import { getMirrorList } from '@/apis/mirrorManage';
import LightEmpty from '@/views/management-center/activity-manage/components/light-empty.vue';

const props = defineProps({
  mirrorGroupInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['onTableChange']);

const paginationOptions = ref({
  showSizeChanger: false, // 是否可以改变每页条数
  showQuickJumper: false, // 是否可以快速跳转至某页
  pageSize: 5, // 每页条数
  hideOnSinglePage: true, // 只有一页时是否隐藏分页器
  total: 0, // 初始化数据总数
  current: 1, // 当前页数
  simple: true,
  position: ['bottomRight'],
});

const tableData = ref([]); // 镜像文件列表
const tableLoading = ref(false);

const columns = [
  {
    title: '镜像文件ID',
    dataIndex: 'imageId',
    ellipsis: true,
    width: '20%',
  },
  {
    title: '版本',
    dataIndex: 'version',
  },
  {
    title: '大小',
    dataIndex: 'totalSize',
  },
  {
    title: '上传人',
    dataIndex: 'createBy',
    ellipsis: true,
  },
  {
    title: '最新推送时间',
    dataIndex: 'updateTime',
    sorter: (a, b) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
    width: 200,
  },
];

const selectedRowKeys = defineModel('selectedRowKeys', { type: Array, default: () => [] });

const onSelectChange = (_selectedRowKeys, selectedRows) => {
  console.log(..._selectedRowKeys , '..._selectedRowKeys ');
  selectedRowKeys.value = [ ..._selectedRowKeys ];
};

const onTableChange = (pagination, filters, sorter, extra) => {
  const { current } = pagination;
  const { order } = sorter;
  paginationOptions.value.current = current;
  getMirrorShareList(order !== 'ascend');
};

const getSize = (value) => {
  if (value === undefined) return '--';
  const { sizeTxt, units } = dataToSizeConversion(value, 2);
  return `${sizeTxt}${units}`;
};


const getMirrorShareList = async (updateTimeDesc = true) => {
  tableLoading.value = true;
  const res = await getMirrorList({
    imageGroupId: props?.mirrorGroupInfo.imageGroupId,
    searchField: '',
    status: [0],
    page: paginationOptions.value.current,
    pageSize: paginationOptions.value.pageSize,
    updateTimeDesc,
  });
  const { code, data } = res;
  if (code === 0) {
    tableLoading.value = false;
    tableData.value = [ ...data?.data ];
    paginationOptions.value.total = data?.total;
  }
};

onMounted(() => {
  getMirrorShareList();
});

watch(
  () => [selectedRowKeys.value, tableData.value],
  () => {
    if (selectedRowKeys.value.length && tableData.value?.length) {
      const filteredList = selectedRowKeys.value.filter((x) => tableData.value.some((y) => y.id === x));
      if (filteredList.length !== selectedRowKeys.value.length) {
        selectedRowKeys.value = filteredList;
      }
    }
  }
);

</script>
  