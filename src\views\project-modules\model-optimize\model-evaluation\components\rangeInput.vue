<template>
  <div class="range-input">
    <a-input-number v-bind="$attrs" :controls="true" :placeholder="$attrs.placeholder"/>
  </div>
</template>

<script setup></script>

<style lang="less" scoped>
.range-input {
  position: relative;
  width: 480px;
  display: flex;
  justify-content: space-between;
  
  :deep(.ant-input-number) {
    width: 100%;
  }
  :deep(.ant-input-number-handler-wrap) {
    opacity: 1;
    display: block;
  }
  :deep(.ant-input-number-disabled .ant-input-number-handler-wrap) {
    background-color: rgba(0, 0, 0, 0.04);
  }
}
</style>
