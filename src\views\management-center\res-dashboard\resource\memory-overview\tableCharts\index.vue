<template>
  <div>
    <!-- <a-flex justify="space-between" style="margin-bottom: 10px">
      <div>项目空间占用分布</div>
      <div>
        <a-radio-group v-model:value="selectedType" @change="handleTimeChange">
          <a-radio-button value="OBJECT_STOR">对象存储</a-radio-button>
          <a-radio-button value="GENERAL_STOR">普通文件存储</a-radio-button>
          <a-radio-button value="HS_STOR">高性能文件存储</a-radio-button>
          <a-radio-button value="IMAGE_STOR">镜像资源</a-radio-button>
        </a-radio-group>
      </div>
    </a-flex> -->
    <a-flex>
      <div style="width: 457px">
        <div class="overview-title">项目空间占用分布</div>
        <DoughnutChart :data-pie-ref="dataPieRef" :selected-group-name="selectedGroupName" />
      </div>
      <div style="width: calc(100% - 457px)">
        <a-flex justify="space-between" style="margin-bottom: 10px">
          <div class="overview-title">项目空间占用明细</div>
          <a-select v-model:value="selectedUsedPercent" style="width: 200px" :get-popup-container="(el) => el.parentNode" @change="handleSelectChange">
            <a-select-option value="1">占用50%以下</a-select-option>
            <a-select-option value="2">占用[50%,70%]</a-select-option>
            <a-select-option value="3">占用[70%,95%]</a-select-option>
            <a-select-option value="4">占用[95%,100%]</a-select-option>
          </a-select></a-flex>

        <SpaceOccupyTable :selected-used-percent="selectedUsedPercent" :selected-group-name="selectedGroupName" />
      </div>
    </a-flex>
  </div>
</template>

<script setup>
import SpaceOccupyTable from './spaceOccupyTable/spaceOccupyTable.vue';
import DoughnutChart from './doughnutChart.vue';

defineProps({
  dataPieRef: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  selectedGroupName: {
    type: [String],
    default: () => {
      return 'OBJECT_STOR';
    },
  },
});

const selectedUsedPercent = ref('1');

const handleSelectChange = (value) => {
  selectedUsedPercent.value = value;
};
</script>

<style lang="less" scoped>
.overview-title {
  font-weight: 600;
  color: #121f2c;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}
.overview-title::before {
  content: '';
  width: 4px;
  height: 14px;
  background-color: @jt-primary-color;
  margin-right: 8px;
}
</style>
