<template>
  <div class="step-box">
    <div class="node-list">
      <div class="operation-bar">
        <a-flex align="center" justify="space-between">
          <a-flex>
            <span
              ><span class="title-name">节点</span>已选择节点：</span
            >
            <span class="count-blue">{{ `${formState.selectedRowKeys.length}` }}</span>
            <!-- <div v-if="formState.selectedRowKeys.length === 0 && validTriggered" class="selected-require danger" style="margin-left: 12px; margin-bottom: 0">请选择至少一个节点</div> -->
          </a-flex>
          <div>
            <a-space>
              <jt-reload-icon-btn @click="onReload" />
              <a-input class="input" placeholder="请输入节点名称" allow-clear @change="handleInput">
                <template #prefix>
                  <jt-icon type="iconsousuo" class="search-icon" style="color: #bec2c5"></jt-icon>
                </template>
              </a-input>
              <a-radio-group v-model:value="showSelectedTable">
                <a-radio-button :value="false">全部</a-radio-button>
                <a-radio-button :value="true">已选</a-radio-button>
              </a-radio-group>
            </a-space>
          </div>
        </a-flex>
      </div>
      <div v-show="showSelectedTable" class="data-table">
        <a-configProvider>
          <template #renderEmpty>
            <empty-data v-if="searchText" title="抱歉，没有找到相关节点" description="您可以换一个关键词试试"></empty-data>
            <empty-data v-else title="抱歉，没有找到相关节点"></empty-data>
          </template>
          <a-table :scroll="{ y: 240 }" :row-selection="rowSelection" :columns="columns" :data-source="dataSourceSelectedResult" :pagination="false" @change="handleTableChange">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <NodeStatusTag :status="record.status" />
              </template>
            </template>
          </a-table>
        </a-configProvider>
      </div>
      <div v-show="!showSelectedTable" class="data-table">
        <a-configProvider>
          <template #renderEmpty>
            <empty-data v-if="searchText" title="抱歉，没有找到相关节点" description="您可以换一个关键词试试"></empty-data>
            <empty-data v-else title="暂无符合条件的节点" description="您可尝试返回上一步，修改资源组资源类型、加速卡类型及所属集群"></empty-data>
          </template>
          <a-table :scroll="{ y: 240 }" :loading="loading" :row-selection="rowSelection" :columns="columns" :data-source="dataSource" :pagination="false" @change="handleTableChange">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <NodeStatusTag :status="record.status" />
                <!-- <NodeStatusTag :status="record.status === '0' ? '0' : record.status === '1' ? '1' : '2' " /> -->
              </template>
            </template>
          </a-table>
        </a-configProvider>
      </div>
    </div>
    <div class="node-config">
      <a-flex style="margin-bottom: 28px" align="center">
        <div>
          <span class="title-name"><span class="danger" style="margin-right: 6px">*</span>节点配置</span>
        </div>
        <a-alert type="warning" show-icon message="节点配置将决定开发环境、训练任务、推理服务的资源配置上限，创建后无法修改，请谨慎设置"></a-alert>
      </a-flex>

      <a-form ref="formRef" class="form-bottom" :model="formState" :label-col="labelCol" label-align="left" :rules="rules" layout="inline">
        <template v-if="baseInfoData?.resourceType === 'gpucard'">
          <a-form-item ref="maxCardNum" label="最高可用卡数" name="maxCardNum" style="margin-right: 48px"><a-input-number v-model:value="formState.maxCardNum" :placeholder="HELP_TEXT_DEFAULT" class="item-width" :precision="0" :min="1" :max="maxInputValue" /><span class="unit">卡</span> </a-form-item>
          <a-form-item ref="resRateCpu" label="资源配比" name="resRateCpu" style="margin-right: 24px"> <a-input-number v-model:value="formState.resRateCpu" :placeholder="HELP_TEXT_DEFAULT" class="item-width" :precision="0" :min="1" :max="maxInputValue" /><span class="unit">核 CPU/卡 </span></a-form-item>
          <a-form-item ref="resRateMemo" label="" name="resRateMemo"><a-input-number v-model:value="formState.resRateMemo" :placeholder="HELP_TEXT_DEFAULT" class="item-width" :precision="0" :min="1" :max="maxInputValue" /><span class="unit">GB 内存/卡 </span></a-form-item>
        </template>
        <template v-else>
          <a-form-item ref="maxCpuNum" label="最高可用核数" name="maxCpuNum" style="margin-right: 148px"> <a-input-number v-model:value="formState.maxCpuNum" class="item-width-cpu" :placeholder="HELP_TEXT_DEFAULT" :precision="0" :min="1" :max="maxInputValue" /><span class="unit">核 </span></a-form-item>
          <a-form-item ref="resRateMemoCore" label="资源配比" name="resRateMemoCore"> <a-input-number v-model:value="formState.resRateMemoCore" class="item-width-cpu" :placeholder="HELP_TEXT_DEFAULT" :precision="0" :min="1" :max="maxInputValue" /><span class="unit">GB 内存/核 </span></a-form-item>
        </template>
      </a-form>
    </div>
  </div>
</template>
<script setup lang="jsx">
import isEmpty from 'lodash/isEmpty';
import debounce from 'lodash/debounce';
import emptyData from '@/components/emptyData';
import { columnsNode } from './nodes.jsx';
import { getNodeListApi } from '@/apis/resourceGroups';
import NodeStatusTag from '../components/nodeStatusTag';

const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
  baseInfoData: {
    type: Object,
    default: () => {},
  },
});
const formState = reactive({
  resRateCpu: null,
  resRateMemo: null,
  maxCardNum: null,
  maxCpuNum: null,
  resRateMemoCore: null,
  selectedRowKeys: [],
});

let columns = ref(columnsNode);
if (props.baseInfoData?.resourceType === 'cpu') {
  columns.value = columns.value.filter((item) => {
    return item.dataIndex !== 'acce_card';
  });
}
const maxInputValue = ref(10000);
const loading = ref(false);
const queryParams = ref({});
// const validTriggered = ref(false);
const emit = defineEmits(['onDisabledNext']);

const formRef = ref(null);
const searchText = ref(null);

const HELP_TEXT_DEFAULT = '请输入';

const dataSource = ref([]);
const dataSourceSelected = ref([]);
const dataSourceSelectedResult = ref([]);

const fetchNodeList = async () => {
  loading.value = true;
  emit('onDisabledNext', true);
  try {
    const params = {
      clusterId: props.baseInfoData.cluster,
      ...queryParams.value,
    };
    if (props.baseInfoData.resourceType === 'gpucard') {
      params.nodeType = props.baseInfoData.acceleratorType;
    } else {
      params.nodeType = 'cpu';
    }
    if (searchText.value) {
      params.keyWord = searchText.value;
    }
    const res = await getNodeListApi(params);
    if (res.code === 0) {
      dataSource.value = res.data.map((item) => {
        return { ...item, key: item.id };
      });

      if (searchText.value) {
        const isExit = dataSourceSelectedResult.value.some((item) => item.nodeName.includes(searchText.value));
        if (isExit) {
          dataSourceSelectedResult.value = dataSourceSelectedResult.value.filter((item) => item.nodeName.includes(searchText.value));
        } else {
          dataSourceSelectedResult.value = dataSourceSelected.value.filter((item) => item.nodeName.includes(searchText.value));
        }
      } else {
        dataSourceSelectedResult.value = dataSourceSelected.value;
        if (dataSource.value.length === 0) {
          dataSourceSelectedResult.value = [];
        }
      }
    }
  } catch (error) {
  } finally {
    emit('onDisabledNext', false);
    loading.value = false;
  }
};

const showSelectedTable = ref(false);

const onReload = async () => {
  await fetchNodeList();
};

onMounted(async () => {
  await fetchNodeList();
  if (!isEmpty(props.defaultData)) {
    // formState.cardNum = props.defaultData.cardNum;
    formState.resRateCpu = props.defaultData.resRateCpu;
    formState.resRateMemo = props.defaultData.resRateMemo;
    formState.maxCardNum = props.defaultData.maxCardNum;
    formState.maxCpuNum = props.defaultData.maxCpuNum;
    formState.resRateMemoCore = props.defaultData.resRateMemoCore;
    formState.selectedRowKeys = props.defaultData.selectedRowKeys;
    dataSourceSelected.value = props.defaultData.selectedNodes;
  }
});
const labelCol = { style: {} };
const validatePositiveInteger = (rule, value) => {
  if (!value) {
    return Promise.reject('This field is required');
  }
  // const isPositiveInteger = /^[1-9]\d*$/.test(value);
  // if (!isPositiveInteger) {
  //   return Promise.reject('Please enter a positive integer');
  // }
  return Promise.resolve();
};

const rules = {
  // cardNum: [{ required: true, message: '请输入', trigger: ['blur', 'change'], validator: validatePositiveInteger }],
  resRateCpu: [{ required: true, message: '请输入', trigger: ['blur', 'change'], validator: validatePositiveInteger }],
  resRateMemo: [{ required: true, message: '请输入', trigger: ['blur', 'change'], validator: validatePositiveInteger }],
  maxCardNum: [{ required: true, message: '请输入', trigger: ['blur', 'change'], validator: validatePositiveInteger }],
  maxCpuNum: [{ required: true, message: '请输入', trigger: ['blur', 'change'], validator: validatePositiveInteger }],
  resRateMemoCore: [{ required: true, message: '请输入', trigger: ['blur', 'change'], validator: validatePositiveInteger }],
};

const onSelectChange = (changableRowKeys, selectedRows) => {
  formState.selectedRowKeys = changableRowKeys;
  dataSourceSelected.value = selectedRows;
  dataSourceSelectedResult.value = selectedRows;
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: formState.selectedRowKeys,
    onChange: onSelectChange,
  };
});

const handleTableChange = (pagination, filters) => {
  queryParams.value = { ...filters };
  // Object.assign(paginationOptions.value, filterFormat);
  fetchNodeList();
};
const handleValid = () => {
  console.log(formState.selectedRowKeys,'formState.selectedRowKeys')
  // validTriggered.value = false;
  let valid = true;
  valid = formState.selectedRowKeys.length > 0;
  console.log(valid,'valid')
  // if (!valid) {
  //   return false;
  // }
  return formRef.value.validate();
};
const getData = () => {
  return { ...formState, selectedNodes: dataSourceSelected.value };
};

const handleInput = debounce((event) => {
  searchText.value = event.target._value;
  fetchNodeList();
  dataSource.value = [];
}, 800);

defineExpose({
  validate: handleValid,
  getData: getData,
});
</script>

<style lang="less" scoped>
.title-name {
  margin-right: 16px;
}
.count-blue {
  font-weight: 500;
  font-size: 14px;
  color: #00a0cc;
}
.step-box {
  margin-bottom: 60px;
}
.node-list {
  padding-right: 100px;
  padding-left: 96px;
}
.node-config {
  padding-right: 100px;
  padding-left: 68px;
}
.overview-title {
  font-weight: 600;
  color: #121f2c;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}
.overview-title::before {
  content: '';
  width: 4px;
  height: 14px;
  background-color: @jt-primary-color;
  margin-right: 8px;
}
.data-table {
  margin: 16px 0 16px 56px;
}
.selected-require {
  margin-bottom: 16px;
  color: #f00;
}
.danger {
  color: #f00;
}
:deep(.ant-form-item .ant-form-item-control-input-content) {
  display: flex;
  align-items: center;
}

.item-width {
  width: auto;
  margin-right: 8px;
  :deep(.ant-input-number-input) {
    width: 140px;
  }
}
.item-width-cpu {
  width: auto;
  margin-right: 8px;
  :deep(.ant-input-number-input) {
    width: 200px;
  }
}

:deep(.ant-alert.ant-alert-warning) {
  width: calc(100% - 86px);
}
.form-bottom {
  padding-left: 80px;
}
:deep(.ant-table-body) {
  overflow-y: auto !important ;
}
:deep(.ant-empty) {
  margin-top: 29px;
  margin-bottom: 13px;
}
:deep(.ant-form-item .ant-form-item-label > label::after) {
  content: '';
  margin-inline-end: 16px;
}
:deep(.ant-table-cell) {
  color: rgba(0, 20, 26, 0.7);
}
:deep(.ant-form-inline .ant-form-item) {
  margin-inline-end: 0;
}
.unit {
  color: rgba(0, 20, 26, 0.45);
}
:deep(.ant-table-placeholder ant-table-cell) {
  border-bottom: 1px solid rgba(0, 20, 26, 0.08) !important;
}
</style>
