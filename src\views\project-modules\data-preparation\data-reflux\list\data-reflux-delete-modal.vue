<template>
  <a-modal v-model:open="modalVisible" :mask-closable="false" wrap-class-name="data-reflux-delete-modal-wrapper" :cancel-button-props="okButtonProps" :ok-button-props="cancelButtonProps" @cancel="handleCancel">
    <template #title>
      <div class="icon-box">
        <jt-icon type="iconwarning-circle-fill-copy" />
        <span class="title-box">确定要删除这个回流任务吗？</span>
      </div>
    </template>
    <template #footer>
      <a-space>
        <a-button type="primary" danger :loading="confirmLoading" @click="handleOk">删除</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </template>
    <div class="del-desc-box">
      <div class="line">确定要删除回流任务“{{ data.taskName }}“吗？删除后不可恢复，请谨慎删除。</div>
    </div>
  </a-modal>
</template>
<script setup>
import { delReflux } from '@/apis/dataReflux';
import { message } from 'ant-design-vue';
const props = defineProps({
  // modal是否展示
  visible: {
    type: Boolean,
    default: false,
  },
  // 要删除的数据
  data: {
    type: Object,
    default: () => {},
  },
});
const okButtonProps = {
  type: 'primary',
  danger: true,
};
const cancelButtonProps = {
  type: 'default',
};
const emits = defineEmits(['handleOkClick', 'handleCancelClick']);
// modal是否展示
const modalVisible = ref(false);
const confirmLoading = ref(false);
// 删除数据回流任务
const handleDelTask = async () => {
  try {
    const params = {
      taskId: props.data?.taskId,
    };
    const res = await delReflux(params);
    if (res?.code === 0) {
      console.log(res.data,'res.data')
      if (res.data) {
        message.success(`回流任务删除成功`);
        emits('handleOkClick');
      } else {
        message.error(res?.msg || `回流任务删除失败，请稍后再试`);
      }
    } else {
      message.error(res?.msg || `回流任务删除失败，请稍后再试`);
    }
    confirmLoading.value = false;
  } catch (e) {
    confirmLoading.value = false;
    throw new Error(e);
  }
};
// 点击确定
const handleOk = () => {
  confirmLoading.value = true;
  handleDelTask();
};
// 点击取消
const handleCancel = () => {
  emits('handleCancelClick');
};

watch(
  () => props.visible,
  (val) => {
    modalVisible.value = val;
  }
);
</script>
<style lang="less">
.data-reflux-delete-modal-wrapper {
  .title-box {
    font-weight: 600;
    font-size: 16px;
    color: #00141a;
    margin-left: 8px;
  }
  .icon-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 24px;
    color: red;
  }
  .del-desc-box {
    padding-left: 32px;
    margin-bottom: 32px;
    .line {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 20, 26, 0.7);
      margin-bottom: 10px;
    }
  }
  .ant-modal .ant-modal-content {
    padding-top: 24px;
  }
}
</style>
