<template>
  <div>
    <a-configProvider>
      <template #renderEmpty>
        <jt-empty :show-operation="!keyword" title="资源"> </jt-empty>
      </template>
      <a-table :columns="columns" :data-source="pooldatas" :loading="loading" :pagination="false" row-key="id" @change="tableChange">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'clusterName'">
            <a-tooltip>
              <template #title>{{ text }}</template>
              <span class="ellipsis-text">{{ text }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'useNode'">
            <span>{{ showText(text) }}</span
            > / <span>{{ showText(record.totalNode) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'useComputePower'">
            <span>{{ showText(text) }}</span
            > / <span>{{ showText(record.totalComputePower) }} {{ record.resourceType === '加速卡' ? '卡' : '核' }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a-button type="link" size="small" :disabled="record.manageNode !== 0" class="detail-btn" style="padding: 0px; height: auto; line-height: 1" ghost @click="deleteInfo(record)">
              <a-tooltip>
                <template v-if="record.manageNode !== 0" #title>
                  <span>资源类型已有纳管节点，无法删除</span>
                </template>
                <span class="ellipsis-text">删除</span>
              </a-tooltip>              
            </a-button>
          </template>
          <template v-else-if="column.dataIndex === 'manageNode'">
            <span>{{ showText(text) }}</span>
          </template>
          <span v-else>{{ text || '-' }}</span>
        </template>
      </a-table>
    </a-configProvider>
    <deletePoolModal v-model:open="open" :resource-info="resourceInfo" @ok="ok"></deletePoolModal>
    <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, watch } from 'vue';
import { message } from 'ant-design-vue';
import deletePoolModal from './../../components/deletePoolModal.vue';
import { RESOURCE_TYPE_FILTERS } from '@/constants/management-platform/poolManage.js';
import { PLAT_POOL_EDIT_AUTH } from '@/constants/management-platform/auth.js';
import { getPlatformAccessEnabledByPlatItem } from '@/keycloak';
import { useRoute } from 'vue-router';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { resourcePoolApi } from '@/apis';
import ErrorCode from '@/constants/errorCode';

const props = defineProps({
  searchText: { type: String, default: '' },
  reload: {
    type: Boolean,
  },
});

const emit = defineEmits(['ok']);
const route = useRoute();
const query = route.query;
const open = ref(false);
const resourceInfo = ref({});
const pooldatas = ref([]);
const loading = ref(false);
const gpuCards = ref([]);
const gpuCardType = ref(undefined);
const resourceType = ref(undefined);

const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});

const keyword = ref(props.searchText);

const columns = computed(() => {
  const list = [
    {
      title: '集群',
      dataIndex: 'clusterName',
      key: 'clusterName',
      ellipsis: true,
      width: '176px',
    },
    {
      title: '资源类型',
      dataIndex: 'resourceType',
      key: 'resourceType',
      width: '176px',
      ellipsis: true,
      filters: RESOURCE_TYPE_FILTERS,
    },
    {
      title: '加速卡类型',
      dataIndex: 'gpuCardType',
      key: 'gpuCardType',
      width: '178px',
      filters: gpuCards.value,
    },
    {
      title: '可纳管节点',
      dataIndex: 'manageNode',
      key: 'manageNode',
      width: '132px',
    },
    {
      title: '已用/总节点',
      dataIndex: 'useNode',
      key: 'useNode',
      width: '156px',
    },
    {
      title: '已用/总算力',
      dataIndex: 'useComputePower',
      key: 'useComputePower',
      width: '156px',
    },
  ];
  if (getPlatformAccessEnabledByPlatItem(PLAT_POOL_EDIT_AUTH)) {
    list.push(
      {
        title: '操作',
        width: '178px',
        dataIndex: 'operation',
        key: 'operation',
        fixed: 'right',
      }
    )
  }
  return list
});

const tableChange = (pagination, filters, sorter) => {
  gpuCardType.value = filters.gpuCardType || undefined;
  resourceType.value = filters.resourceType || undefined;
  changePageNum(1);
};

const showText = (text) => {
  if (text >= 0) return text;
  return '-';
};

const getGpuCards = async () => {
  const res = await resourcePoolApi.getGPUCardsByPoolId({});
  if (res.code === 0 || res.data) {
    gpuCards.value = res.data.map((card) => {
      return {
        text: card.gpuCardTypeName,
        value: card.gpuCardTypeName,
      };
    });
  } else {
    message.error(ErrorCode[res.code] || ErrorCode[202503005]);
  }
};

const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getTableData();
};
const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getTableData();
};

const getTableData = async () => {
  try {
    loading.value = true;
    const res = await resourcePoolApi.getResourceInfoList({
      pageNum: paginationOptions.value.pageNum,
      pageSize: paginationOptions.value.pageSize,
      keyWord: keyword.value,
      poolId: query.id,
      gpuCardType: gpuCardType.value,
      resourceType: resourceType.value,
    });
    if (res.code === 0) {
      pooldatas.value = res.data.data || [];
      paginationOptions.value.total = res.data.total;
    } else {
      message.error(ErrorCode[res.code] || ErrorCode[202503002]);
    }
  } catch (e) {
    message.error(ErrorCode[202503002]);
    throw new Error(e);
  } finally {
    loading.value = false;
  }
};

const deleteInfo = (record) => {
  if (record.manageNode !== 0) return false;
  resourceInfo.value = record;
  open.value = true;
};

const getTableList = () => {
  paginationOptions.value.pageNum = 1;
  getTableData();
};

const ok = async () => {
  emit('ok');
  getTableList();
};

watch(
  () => props.reload,
  () => {
    changePageNum(1);
  }
);

watch(
  () => open,
  (val) => {
    console.log('open', val);
  }
);

watch(
  () => props.searchText,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      keyword.value = newValue;
      changePageNum(1);
    }
  }
);

onMounted(() => {
  getTableData();
  getGpuCards();
});

defineExpose({
  getTableList,
});
</script>

<style lang="less" scoped>
// .expansion-text-ellipsis {
//   display: inline-block;
//   max-width: 100%;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// }
// .link-id-name {
//   cursor: pointer;
//   &:hover {
//     color: @jt-primary-color;
//   }
// }
// .time {
//   width: 148px;
// }
:deep(.ant-table-cell) {
  vertical-align: top;
}
.detail-btn:not(:disabled):hover {
  color: #00a0cc;
  cursor: pointer;
}
</style>
