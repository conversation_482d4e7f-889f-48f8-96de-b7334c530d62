<template>
  <div>
    <v-chart autoresize style="height: 360px; width: 100%" class="chart" :option="option" />
  </div>
</template>
  
<script setup>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import VChart from 'vue-echarts';
import { nextTick } from 'vue';
import { hex2Rgb } from '@/utils/color';
  
const props = defineProps({
  dataSource: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
});
  
use([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON>, TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent]);
  
// 适配给图表用的颜色
const colorMap = ['rgba(58,146,250,0.85)', 'rgba(56,192,85,0.85)', 'rgba(242,128,18,0.85)', 'rgba(141,101,248,0.85)'];
function getColor(index) {
  return colorMap[index % colorMap.length];
}
const echartsCommonOptions = {
  grid: {
    left: 120,
    right: 120,
    bottom: 20,
    top: 80,
  },
};
  
const getLinearColor = (index) => {
  return {
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: hex2Rgb(getColor(index), 0.15), // 100% 处的颜色
      },
      {
        offset: 1,
        color: hex2Rgb(getColor(index), 0.05), // 0% 处的颜色
      },
    ],
    global: false, // 缺省为 false
  };
};
const chartsData = reactive({
  xAxis: [],
  datas: {},
});
  
const chartColumns = ref([]);
function generateChartColumns() {
  chartColumns.value = props.columns.map((item) => {
    const newItem = {
      key: item.key,
      name: item.title.replace('（TB·小时）', ''),
    };
    return newItem;
  });
}
  
watch(
  () => props.columns,
  () => {
    generateChartColumns();
  },
);
  
const data = ref([]);
const option = ref({});

const end = ref(100);
  
watch(
  () => props.dataSource,
  () => {
    nextTick(() => {
      geteDiagramData();
    });
  }
);
//获取接口数据
function geteDiagramData() {
  data.value = props.dataSource;
  chartsData.xAxis = data.value.map((x) => x.time);

  for (let i = 0; i < chartColumns.value.length; i++) {
    const item = chartColumns.value[i];
    chartsData.datas[item.key] = data.value.map((x) => x[item.key]);
  }
  initChart();
}
  
function getSeries() {
  const series = [];
  for (let i = 0; i < chartColumns.value.length; i++) {
    const item = chartColumns.value[i];
    const itemData = chartsData.datas[item.key];
    series.push({
      name: item.name,
      data: itemData,
      type: 'line',
      symbolSize: 10,
      showSymbol: false,
      areaStyle: { color: getLinearColor(i) },
      //线现拐点实心圆
      symbol: 'circle',
      //折线弧度
      smooth: true,
      itemStyle: {
        color: getColor(i),
        borderWidth: 10,
      },
      lineStyle: { color: getColor(i) },
      yAxisIndex: item.key === 'imageUsage' ? 1 : 0,
    });
  }
  return series;
}

const initChart = () => {
  option.value = {
    color: colorMap,
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: end,
      },
      {
        type: 'slider',
        left: '200px',
        right: '200px',
        show: false,
      },
    ],
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: chartsData.xAxis,
        startValue: 0,
      },
    ],
    yAxis: [
      {
        position: `left`,
        type: 'value',
        nameGap: 20,
        name: `存储资源用量（TB·小时）`,
        nameTextStyle: {
          fontSize: 14,
          color: 'rgba(0, 20, 26, 0.45)',
          align: 'center',
        },
        //y轴刻度
        axisLabel: {
          show: true,
          formatter: '{value}',
        },
      },
      {
        position: `right`,
        type: 'value',
        nameGap: 20,
        name: `镜像资源用量（TB·小时）`,
        nameTextStyle: {
          fontSize: 14,
          color: 'rgba(0, 20, 26, 0.45)',
          align: 'center',
        },
        alignTicks: true,
        //y轴刻度
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
        label: {},
      },
      className: 'tooltip-box',
      // alwaysShowContent: true,
      formatter: (params, ticket) => {
        let rowString = ``;
        params.forEach((x, i) => {
          if (chartColumns.value[i].key === 'time') {
            rowString += '';
          } else {
            rowString += `<div class="row">
                        <div>
                        <span class="dot-common" style="background-color:${getColor(i)}"></span>
                        <span>${x.seriesName}</span>
                        </div>
                        <span class="right-text" style="display:inline-block;min-width:50px;margin-left:20px;color:#121f2c;font-size:14px;">${x.data + `TB·小时`}</span>
                    </div>`;
          }
        });
        return `
            <div class="tooltip-wrap">
            <div class="tooltip-content">
                ${rowString}
            </div>
            <div class="tooltip-footer">${params[0].axisValue}</div>
            </div>
            `;
      },
    },
    legend: {
      left: 'right',
      right: 0,
      top: '0',
      width: '90%',
      data: chartColumns.value.map((x) => x.name),
      //图例去小圆
      itemWidth: 12,
      itemHeight: 3,
      itemGap: 20,
      textStyle: {
        color: 'rgba(0, 20, 26, 0.7)',
        fontSize: 12,
      },
    },
    series: [],
    ...echartsCommonOptions,
};
  // 计算图例会有几行，然后设置grid的top
  const defaultLegendHeight = 50;
  const legendLineHeight = 20;
  const legendLineCount = Math.ceil(chartColumns.value.length / 9);
  const legendHeight = legendLineCount * legendLineHeight;
  option.value.grid.top = legendHeight + defaultLegendHeight;

  option.value.series = getSeries();
};
</script>
  
<style lang="less" scoped>
:deep(.tooltip-box) {
  .tooltip-wrap {
    min-width: 200px;
  }
  .tooltip-content {
    margin-bottom: 12px;
  }
  .flex {
    display: flex;
    &.justify-between {
      justify-content: space-between;
    }
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    p,
    div {
      margin-right: 60px;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #121f2c;
      line-height: 20px;
    }
    span {
      font-size: 12px;
      color: #606972;
      line-height: 18px;
    }
    .right-text {
      font-size: 14px;
      color: #121f2c;
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
</style>
  