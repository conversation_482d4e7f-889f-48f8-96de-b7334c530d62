import { dataToSizeConversion } from '@/utils/storage';
const getRate = (a, b) => {
  if (typeof a === 'undefined' || typeof a === 'object') {
    return 0;
  }
  if (a === '0') {
    return 0;
  }
  const result = ((parseInt(a, 10) / parseInt(b, 10)) * 100).toFixed(1);
  if (result === '0.0') {
    return 0;
  }
  return `${result}%`;
};

const getStoreUseTotal = (useStore, totalStore) => {
  const { sizeTxt: sizeTxtUse, units: unitsUse } = dataToSizeConversion(useStore, 1);
  let useTxt = useStore === 0 ? 0 : `${sizeTxtUse}${unitsUse}`;
  const { sizeTxt: sizeTxtTotal, units: unitTotal } = dataToSizeConversion(totalStore, 1);
  let totalTxt = totalStore === 0 ? 0 : `${sizeTxtTotal}${unitTotal}`;
  if (typeof useStore === 'undefined') {
    useTxt = '--';
  }
  if (typeof totalStore === 'undefined') {
    totalTxt = '--';
  }
  if (sizeTxtUse === '0.0') {
    useTxt = 0;
  }
  return `${useTxt} / ${totalTxt}`;
};
export const columns = [
  {
    title: '项目空间名称',
    dataIndex: 'projectName',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
  {
    title: '负责人',

    dataIndex: 'leader',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
  {
    title: '对象存储(已用/总共)',

    dataIndex: 'used',
    customRender: ({ text, record }) => {
      return getStoreUseTotal(record.used, record.quota);
    },
  },
  {
    title: '占用率',

    dataIndex: 'cpu',
    customRender: ({ text, record }) => {
      return getRate(record.used, record.quota);
    },
    sorter: true,
    sortOrder: null, // 切换时需重置，因此受控控制
  },
  {
    title: '存储桶名称',
    dataIndex: 'bucketName',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
  {
    title: '文件目录ID',
    dataIndex: 'shareId',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
];

export const imageColumn = [
  {
    title: '项目空间名称',
    dataIndex: 'spaceName',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
  {
    title: '负责人',
    dataIndex: 'createBy',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
  {
    title: '镜像资源(已用/总共)',

    dataIndex: 'quotaUsed',
    customRender: ({ text, record }) => {
      return getStoreUseTotal(record.quotaUsed, record.quotaTotal);
    },
  },
  {
    title: '占用率',
    dataIndex: 'usageRate',
    sorter: true,
    sortOrder: null, // 切换时需重置，因此受控控制
  },
  {
    title: '镜像仓库名称',
    dataIndex: 'projectName',
    customRender: ({ text, record }) => {
      return text || '--';
    },
  },
]
