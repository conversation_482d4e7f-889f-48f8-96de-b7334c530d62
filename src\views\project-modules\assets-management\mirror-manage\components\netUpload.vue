<template>
  <div>
    <jt-loading v-if="loadingShow"></jt-loading>
    <div class="form-box">
      <a-form ref="formRef" :model="form" :label-col="labelCol" :rules="rules" :wrapper-col="wrapperCol">
        <!-- 镜像名称 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="镜像名称" name="name">
              <span>{{ form.name }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 适用场景 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="适用场景" name="scene">
              <div class="scene-box">
                <div v-if="[IMAGE_SCENE_TYPE.TRAIN, IMAGE_SCENE_TYPE.ALL].includes(form.scene)" class="box train">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN] }}</div>
                <div v-if="[IMAGE_SCENE_TYPE.REASON, IMAGE_SCENE_TYPE.ALL].includes(form.scene)" class="box push">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON] }}</div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 导入方式 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="导入方式" name="source">
              <a-radio-group v-model:value="form.source" :default-value="0">
                <!-- <a-radio disabled :value="0"> 本地上传 </a-radio> -->
                <a-radio :value="0"> 对象存储导入 </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 镜像版本 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="镜像版本" name="version">
              <div class="content-box-wrap-version">{{ form.version }}</div>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 导入镜像 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="镜像文件" name="path" :rules="[{ required: true, validator: checkImagePath, trigger: 'change' }]">
              <!-- <span class="form-version-icon">*</span> -->
              <object-select style="width: 480px; height: 32px" @changeName="handleChangeName"></object-select>
              <div class="tips" :class="typeError ? 'error' : ''">支持.tar、.tgr或.gz格式文件</div>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 文件大小 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="文件大小" name="size">
              <span style="margin-right: 24px">{{ form.size }}</span>
              <span class="tips">建议扩容后上传，点击<a-button class="btn-expand" type="link" :disabled="!expandAble" @click="jumpExpand">立即扩容</a-button></span>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 剩余镜像资源 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="剩余镜像资源" name="remainder">
              <div class="remainder-box">
                <div :class="showTips ? 'error-text' : ''">{{ form.remainder }}</div>
                <div v-if="showTips" class="expand-tips">剩余镜像资源不足，请清理已有镜像或申请扩容</div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 镜像标签 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="镜像标签" name="tags" class="form-version">
              <!-- <span class="form-version-icon">*</span> -->
              <mirror-version-input :exist-tags="form.tags" @getMirrorVersion="getMirrorVersion" @versionError="versionErrorExist"></mirror-version-input>
              <div v-if="versionError" class="input-tip error">
                {{ versionText }}
              </div>
              <div v-else class="input-tip">请添加1-3个标签，12个字符以内的大小写字母、数字、中划线、下划线、小数点</div>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 描述 -->
        <a-row>
          <a-col :span="colSpan">
            <a-form-item label="描述" name="desc" class="form-desc">
              <a-textarea v-model:value="form.desc" class="textarea-box" style="width: 480px" placeholder="请输入" :maxlength="100" :rows="4" />
              <div class="show-count">
                已输
                <span>{{ form.desc.split(' ').join('').length }}</span>
                / 100 字
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-row>
      <a-col :span="colSpan">
        <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <a-button :disabled="submitDisabled" type="primary" style="width: 88px; height: 32px" :class="{ 'kl-create-btn': !submitDisabled }" @click="handleOk()">开始上传</a-button>
          <a-button style="margin-left: 10px; height: 32px" @click="handleCancel()">取消</a-button>
        </a-form-item>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { mirrorManageApi, projectSpaceApi } from '@/apis';
import { dataToSizeConversion } from '@/utils/storage';
import mirrorVersionInput from './mirrorVersionInput.vue';
import objectSelect from './objectSelect.vue';
import { IMAGE_SCENE_TYPE, IMAGE_SCENE_MSG } from '@/constants/image';
import { ROLE_KEYS } from '@/constants/projectSpace.js';
import ErrorCode from '@/constants/errorCode';

export default {
  components: {
    mirrorVersionInput,
    objectSelect,
  },
  props: {
    imageGroupId: {
      type: String,
      default: '',
    },
    imageGroupName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      colSpan: 24, // 布局有关
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      rules: {
        source: { required: true },
        version: { required: true },
        path: { required: false },
        tags: { required: true, trigger: ['change', 'blur'], validator: this.checkTags },
        desc: { required: true, trigger: ['change', 'blur'], validator: this.checkDescription },
      },
      form: {
        source: 0, //导入方式
        name: this.imageGroupName, //镜像组名称
        scene: this.$route.query.scene * 1, //适用场景
        version: '--', //镜像版本
        path: '', //镜像地址
        size: '- MB', //镜像大小
        remainder: '--', //剩余镜像资源
        tags: [], //镜像标签
        desc: '', //描述
      },
      fileName: '',
      fileSize: 0,
      remainderSize: 0,
      typeError: false,
      loadingShow: false,
      showTips: false,
      versionError: false,
      versionText: '',
      IMAGE_SCENE_TYPE,
      IMAGE_SCENE_MSG,
      expandRoleList: [ROLE_KEYS.LEADER, ROLE_KEYS.ADMIN],
      expandAble: false,
    };
  },
  computed: {
    submitDisabled() {
      return !(this.form.scene !== undefined && this.form.version !== '--' && this.form.path && !this.typeError && this.fileSize && this.remainderSize && !this.showTips && this.form.tags?.length && this.form.desc.split(' ').join('').length > 0 && !this.form.desc.includes(' '));
    },
  },
  created() {
    this.getData();
  },

  methods: {
    // 获取数据----入口
    getData() {
      this.loadingShow = true;
      this.generateVersion();
      this.getRemaind();
      this.getProjectRole();
    },
    // 查询当前登录用户在项目下的角色
    async getProjectRole () {
      const res = await projectSpaceApi.getProjectRole();
      if (res.code === 0) {
        for (const i in res.data) {
          if (this.expandRoleList.includes(res.data[i].key)) {
            this.expandAble = true;
            break;
          }
        }
      } else {
        message.error(ErrorCode[res.code] || res.msg);
      }
    },
    generateVersion() {
      mirrorManageApi.getVersion({ imageGroupId: this.imageGroupId }).then((res) => {
        if (res.code == 0) {
          this.form.version = res.data;
        }
      });
    },
    getRemaind() {
      mirrorManageApi.getMirrorRemaind().then((res) => {
        this.loadingShow = false;
        if (res.code == 0) {
          const temp = res.data * 1024 * 1024 * 1024;
          if (!isNaN(temp)) {
            this.remainderSize = temp;
            this.form.remainder = this.getSize(this.remainderSize);
            this.showTips = this.remainderSize <= 0;
          }
        }
      });
    },
    getSize(value) {
      if (value === undefined) return '--';
      if (value < 0) {
        const { sizeTxt, units } = dataToSizeConversion(value * -1, 2);
        return `-${sizeTxt} ${units}`;
      } else {
        const { sizeTxt, units } = dataToSizeConversion(value, 2);
        return `${sizeTxt} ${units}`;
      }
    },
    checkImagePath() {},
    handleChangeName(path, fileName, size, typeError) {
      this.form.path = path;
      this.fileName = fileName;
      this.fileSize = size;
      this.form.size = this.getSize(this.fileSize);
      this.typeError = typeError;
      this.setTipShow();
    },
    setTipShow() {
      this.showTips = this.fileSize > this.remainderSize;
    },
    getMirrorVersion(data) {
      this.form.tags = data;
      if (data.length === 0) {
        this.versionError = true;
        this.versionText = '请添加1-3个标签，12个字符以内的大小写字母、数字、中划线、下划线、小数点';
      } else {
        this.versionError = false;
        this.versionText = '';
      }
    },
    versionErrorExist(flag) {
      this.versionError = flag;
      this.versionText = flag ? '请添加1-3个标签，12个字符以内的大小写字母、数字、中划线、下划线、小数点' : '';
    },
    checkTags(rule, value) {},
    checkDescription(rule, value) {
      if (value === '' || value.split(' ').join('') === '') {
        return Promise.reject('请输入');
      } else {
        if (value.includes(' ')) {
          return Promise.reject('不允许输入空格');
        } else {
          return Promise.resolve();
        }
      }
    },
    // 跳转至扩容页面
    jumpExpand() {
      this.$router.push({
        path: '/project-space-details/upgrade-resource',
      });
    },
    // 提交版本
    handleOk() {
      this.loadingShow = true;
      const { version, path, tags, desc } = this.form;
      const sendData = {
        imageGroupId: this.imageGroupId,
        imageGroupName: this.imageGroupName,
        version,
        tags,
        filePath: path.split('/').length > 1 ? `${path.split('/').slice(0, -1).join('/')}/` : path.split('/').slice(0, -1).join('/'),
        fileName: this.fileName,
        description: desc,
        totalSize: this.fileSize,
      };
      mirrorManageApi.uploadMirror(sendData).then((res) => {
        this.loadingShow = false;
        if (res.code === 0) {
          this.$router.push({
            path: `/mirrorManage/detail/${this.imageGroupId}`,
            query: {
              name: this.imageGroupName,
              scene: this.$route.query.scene,
              tabIndex: 'uploading',
            },
          });
        } else {
          message.error(res.msg || '新增版本失败，请稍后再试');
        }
      });
    },
    // 取消按钮
    handleCancel() {
      this.$router.push({
        path: `/mirrorManage/detail/${this.imageGroupId}`,
        query: {
          name: this.imageGroupName,
          scene: this.$route.query.scene,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-box {
  margin-top: 20px;
  // margin-bottom: 30px;
  .scene-box {
    display: flex;
    align-items: center;

    .box {
      border-radius: 2px;
      margin-right: 8px;
      width: 40px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #b7eb8f;
    }

    .push {
      color: #389e0d;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
    }

    .train {
      color: #0958d9;
      background: #e6f4ff;
      border-color: #91caff;
    }
  }

  .remainder-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .error-text {
      color: #ff4d4f;
    }
    .expand-tips {
      color: #ff4d4f;
      font-size: 14px;
    }
  }

  .form-version {
    position: relative;
    max-height: 90px;
    &-icon {
      position: absolute;
      color: #ff4d4f;
      left: -82px;
      top: 4px;
      font-size: 20px;
    }

    .input-tip {
      line-height: 18px;
      margin-top: 2px;
      color: #999;
      font-size: 14px;

      &.error {
        color: #ff4d4f;
      }
    }
  }
  .tips {
    color: rgba(0, 20, 26, 0.45);
    font-size: 14px;
    .btn-expand {
      padding: 0;
    }
  }
  .error {
    color: #ff4d4f;
  }

  .form-desc {
    height: 120px;
  }

  :deep(.ant-form-item-control-input-content) {
    position: relative;

    .textarea-box {
      width: 100%;
    }

    .show-count {
      position: absolute;
      left: 388px;
      color: #999;
    }
  }

  :deep(.ant-input) {
    resize: none;
  }
}
:deep(.ant-form-item-label > label) {
  color: #00141a;
}
.content-box-wrap-version {
  width: 38px;
  height: 24px;
  background: #eaf2ff;
  border-radius: 2px;
  border: 1px solid #99beff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #0082ff;
}
</style>
