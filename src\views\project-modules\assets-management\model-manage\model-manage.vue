<template>
  <div class="wrapper-box">
    <jt-sub-header :bread-crumb="breadValue"></jt-sub-header>
    <div v-if="!hasAuth" class="content-box">
      <jt-welcome-page :data="guideData"></jt-welcome-page>
    </div>
    <section v-else class="content-box">
      <devBanner @createNew="handleNewModel" @viewNow="viewNow" />
      <div class="content-wrap">
        <!-- 页面内容 分tab栏-->
        <a-tabs default-active-key="prepareModel" :active-key="currentTab" size="large" @change="changeTab">
          <template #rightExtra>
            <a-space>
              <jt-reload-icon-btn @click="handelReload" />
              <a-input v-model:value="searchText" class="input" placeholder="请输入模型名称" allow-clear @change="handleInput">
                <template #prefix>
                  <jt-icon type="iconsousuo" class="search-icon" style="color: #bec2c5"></jt-icon>
                </template>
              </a-input>
              <a-button v-if="currentTab === 'customModel'" type="primary" class="kl-create-btn" @click="handleNewModel">
                新建模型
                <template #icon>
                  <jt-icon type="icontianjia" class="add-icon"></jt-icon>
                </template>
              </a-button>
            </a-space>
          </template>
          <a-tab-pane key="prepareModel" v-if="false">
            <template #tab>
              <span class="tab-bar"> 平台预置模型 </span>
              {{ prepareModelCount }}
            </template>
            <prepare-model-list ref="prepareList" :origin-category-list="originCategoryList" :search-text="searchText" :current-tab="currentTab"></prepare-model-list>
          </a-tab-pane>
          <a-tab-pane key="customModel">
            <template #tab>
              <span class="tab-bar"> 自定义模型 </span>
              {{ customModelCount }}
            </template>
            <custom-model-list ref="customList" :origin-category-list="originCategoryList" :search-text="searchText" :current-tab="currentTab" @createNew="handleNewModel" @getCount="getCount"></custom-model-list>
          </a-tab-pane>
        </a-tabs>
        <template v-if="addNewConfirmModalVisible">
          <model-add-confirm-modal :visible="addNewConfirmModalVisible" :confirm-btn-loading="confirmBtnLoading" @ok="handleAddConfirmModalOk" @cancel="handleAddConfirmModalCancel" @changeConfirmBtnLoading="handleConfirmBtnLoading"></model-add-confirm-modal>
        </template>
      </div>
    </section>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import debounce from 'lodash/debounce';
import { modelManageApi } from '@/apis';
import modelAddConfirmModal from './components/modelAddConfirmModal.vue';
import devBanner from './components/pageBanner.vue';
import prepareModelList from './components/prepareModelList.vue';
import customModelList from './components/customModelList.vue';
import { MODEL_TYPE_OPTIONS } from './util';
export default {
  components: {
    modelAddConfirmModal,
    devBanner,
    prepareModelList,
    customModelList,
  },
  data() {
    this.handleInput = debounce(this.handleInput, 1000);
    return {
      guideData: {
        title: '欢迎使用模型管理！',
        subTitle: '灵活支持多厂家、多框架模型，您可将平台训练或第三方模型导入平台进行统一托管',
        btnText: '进入模型管理',
      },
      hasAuth: false,
      confirmBtnLoading: false,
      addNewConfirmModalVisible: false, // 新建模型弹窗是否显示
      searchText: '',
      breadValue: [{ name: '项目空间管理', path: '/project-space' }, { name: '资产管理' }, { name: '模型管理' }],
      originCategoryList: MODEL_TYPE_OPTIONS,
      prepareModelCount: 0,
      customModelCount: 0,
      currentTab: 'customModel',
    };
  },
  created() {
    this.hasAuth = this.$store.state.projectId ? true : false;
    if (this.hasAuth) {
      // 发起数据初始化的请求；
      this.getData();
    }
  },
  methods: {
    getData() {
      modelManageApi.getModelType({ dictName: 'modelCategory' }).then((res) => {
        if (res.code == 0) {
          this.originCategoryList = res.data;
          this.getCount();
          this.getTableInfo();
        }
      });
    },
    getCount() {
      const sendData = {
        searchField: '',
        pageNum: 1,
        pageSize: 100,
        categoryList: [],
        labelList: [],
        frameworkList: [],
        updateTimeDesc: true,
      };
      modelManageApi.getPresetModelList(sendData).then((res) => {
        if (res.code === 0) {
          this.prepareModelCount = res.data.total || 0;
        }
      });
      const sendData1 = {
        pageNum: 1,
        pageSize: 100,
        searchCondition: '',
        isAsc: false,
        orderColumn: 'modifiedTime',
        categoryList: [],
        status: 0,
      };
      modelManageApi.getModelList(sendData1).then((res) => {
        if (res.code === 0) {
          this.customModelCount = res.data?.total || 0;
        }
      });
    },
    getTableInfo() {
      if (this.currentTab === 'prepareModel') {
        this.$refs.prepareList.getTableInfo();
      }
      if (this.currentTab === 'customModel') {
        this.$refs.customList.getTableInfo();
      }
    },
    // 新建模型文件
    handleNewModel() {
      this.currentTab = 'customModel';
      this.changeTab('customModel');
      this.addNewConfirmModalVisible = true;
    },
    // 立即查看
    viewNow() {
      this.$router.push({ path: '/model-square' });
    },
    // 新建模型弹窗取消
    handleAddConfirmModalCancel() {
      this.addNewConfirmModalVisible = false;
    },
    // 新建模型提交数据
    async handleAddConfirmModalOk(data) {
      try {
        const {
          name, // 模型名称
          type, // 模型分类
          frame, // 模型框架
        } = data || {};
        this.confirmBtnLoading = true;
        const formData = new FormData();
        formData.append('framework', frame);
        formData.append('name', name);
        formData.append('category', type);
        const res = await modelManageApi.createModel(formData);
        this.confirmBtnLoading = false;
        if (res.code === 0) {
          this.addNewConfirmModalVisible = false;
          message.success(`模型 ${name} 新建成功`);
          this.getTableInfo();
          this.getCount();
        } else {
          message.error(`模型 ${name} 新建失败，请稍后再试`);
        }
      } catch (e) {
        this.confirmBtnLoading = false;
      }
    },
    // 处理确定按钮的loadig状态
    handleConfirmBtnLoading(status) {
      this.confirmBtnLoading = status;
    },
    handleInput(event) {
      this.searchText = event.target._value;
    },
    handelReload() {
      this.getTableInfo();
    },
    // 切换tab栏
    changeTab(key) {
      this.currentTab = key;
      this.searchText = '';
      if (key === 'prepareModel') {
        this.$nextTick(() => {
          this.$refs.prepareList.getTableInfo();
        });
      }
      if (key === 'customModel') {
        this.$nextTick(() => {
          this.$refs.customList.getTableInfo();
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.wrapper-box {
  .content-box {
    padding: 8px 20px 20px;
    .content-wrap {
      position: relative;
      margin-bottom: 20px;
      padding: 20px 20px 48px;
      background-color: #fff;
      min-height: ~'calc(100% - 20px)';
      border-radius: 4px;
      box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        p {
          font-size: 16px;
          font-weight: 600;
          color: #121f2c;
        }

        div {
          display: flex;
          align-items: center;

          .add {
            height: 32px;
            // background: linear-gradient(90deg, #00cada 0%, #00a2f4 100%);
            border-radius: 2px;
            font-size: 14px;
            color: #fff;
            margin-left: 8px;
            display: flex;
            align-items: center;

            &-icon {
              color: #fff;
              margin-right: 4px;
            }
          }

          .input {
            width: 240px;
            height: 32px;
            border-radius: 2px;
            border: 1px solid #d6d9db;

            .search-icon {
              position: relative;
              top: 0px;
            }
          }
        }
      }
    }
  }
}

:deep(.ant-table-row) :hover {
  .location-name {
    color: @jt-primary-color;
    cursor: pointer;
  }
}

:deep(.ant-tabs-nav .ant-tabs-tab) {
  padding: 4px 12px 14px;
}
</style>
