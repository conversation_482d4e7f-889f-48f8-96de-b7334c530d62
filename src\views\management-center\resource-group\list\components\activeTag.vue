<template>
  <jt-tag class="jt-tag" :color="color">{{ statusText }}</jt-tag>
</template>

<script setup>
import { ACTIVE_TYPE_NEW, ACTIVE_TYPE_COLOR } from '@/constants/activeType';

const props = defineProps({
  status: {
    type: Number,
    default: 0,
  },
});

const statusText = computed(() => ACTIVE_TYPE_NEW[props.status] || '');

const color = computed(() => ACTIVE_TYPE_COLOR[props.status] || 'default');
</script>

<style lang="less" scoped>
  .jt-tag{
    font-size: 14px;
  }
</style>
