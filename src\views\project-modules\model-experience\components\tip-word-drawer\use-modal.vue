<template>
  <a-modal :open="openUse" title="引用Prompt" @cancel="closeUse" class="use-modal">
    <template #footer>
      <a-button key="back" @click="closeUse">取消</a-button>
      <a-button key="submit" type="primary" @click="toCopy">立即引用</a-button>
    </template>
    <div class="use-modal-content">
      <p style="word-break: break-all;white-space: pre-wrap;">{{ info }}</p>
      <div v-for="(item, index) in inputList" :key="index">
        <h4>
          <span>变量{{ index + 1 }}: </span>
          <span>{{ inputList[index] }}</span>
        </h4>
        <a-input v-model:value="inputValues[index]" placeholder="请填充变量，输入具体内容完善Prompt" />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { useStore } from 'vuex';
import { ref, watch, computed, defineProps, defineEmits } from 'vue';
import { getStrVar } from '../../utils.js';

const props = defineProps({
  openUse: {
    type: Boolean,
    default: false,
  },
  info: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['closeUse']);

const store = useStore();
const configParam = computed(() => store.state.modelExperience.configParam);
const inputList = ref([]);
const inputRef = ref([]);
const inputValues = ref(Array(inputList.value.length).fill(null));

const closeUse = () => {
  emit('closeUse');
};

const toCopy = () => {
  const pattern = /\{\{([^{}]*)\}\}/g;
  // /\{\{(.*?)\}\}/g
  const result = props.info.replace(pattern, function (match, content) {
    let valueToReplace = inputValues.value.shift();
    console.log(valueToReplace, `${valueToReplace}`,match,content)
    return valueToReplace ? `${valueToReplace}` : '';
  });
  // emit('closeUse', result.replace(/[^\w\s\u4e00-\u9fa5]/g, ''));
  emit('closeUse', result);
};

watch(
  () => props.info,
  () => {
    inputList.value = getStrVar(props.info, '{{', '}}');
  }
);
</script>

<style lang="less" scoped>
.use-modal {
  .use-modal-content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
  }
  p {
    margin-bottom: 16px;
  }
  h4 {
    font-size: 14px;
    margin: 24px 0 8px 0;
  }
}
</style>