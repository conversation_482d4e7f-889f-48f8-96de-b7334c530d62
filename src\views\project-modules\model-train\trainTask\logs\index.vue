<template>
  <div class="train-task-logs">
    <sub-header :bread-crumb="breadCrumb" />
    <div class="content-title">
      实例日志_{{ requestData.podName }}
      <a-space v-if="isNeedWebSocket" :size="8" class="operation-select">
        <a-select v-model:value="requestData.podName" style="width: 220px" @change="handleInstanceChange">
          <a-select-option v-for="(item, index) in instanceOptions" :key="index" :value="item.value">
            <a-tooltip>
              <template #title>{{ item.value }}</template>
              {{ `${item.value.slice(0, 15)}...${item.value.slice(item.value?.length - 6, item.value?.length)}` }}
            </a-tooltip>
          </a-select-option>
        </a-select>
        <a-button class="kl-create-btn btn-export" :disabled="!logsInfo?.podLogs?.length" @click="exportLogs">导出</a-button>
      </a-space>
    </div>
    <div class="log-container">
      <jt-socket-log v-if="isNeedWebSocket" :key="requestData.podName" :request-url="trainTaskLogUrl" :request-data="requestData" @change="getWebSocketLog" />
      <train-log v-else :pod-name="requestData.podName" :instance-options="instanceOptions" :submit-time="submitTime" :logs-info="logsInfo" :need-export="true" @search="searchLogs" @export="exportLogs" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import SubHeader from '@/components/subHeader';
import TrainLog from '@/views/components/trainLog';
import { message } from 'ant-design-vue';
import { trainTaskLogUrl } from '@/constants/socketUrl.js';
import { STATUS, TASK_TYPE, taskApiInfo } from '@/constants/trainTask';
import { getTaskPodLog, exportTaskPodLog } from '@/apis/modelTrain';
import { requestWithProjectId } from '@/request';
const { POST } = requestWithProjectId;

const route = useRoute();
const store = useStore();
const { id, taskStatus, resGroupId, podName, taskSubmitTime, pageNum, pageSize } = route.query;
const isNeedWebSocket = computed(() => {
  return [STATUS[TASK_TYPE.TASK].onSTART, STATUS[TASK_TYPE.TASK].RUNNING].includes(+taskStatus);
});
const instanceOptions = ref([]);
const requestData = ref({
  id,
  podName,
  type: 'log',
});
const submitTime = ref(taskSubmitTime);
const logsInfo = ref({
  podLogs: [],
  loadMore: false,
});
const pageParams = ref({
  pageNum: 1,
  pageSize: 500,
});
const searchParams = ref({
  startTime: '',
  endTime: '',
  key: [],
});

const breadCrumb = [
  {
    name: '项目空间管理',
    path: '/project-space',
  },
  {
    name: '模型训练',
  },
  {
    name: '训练任务',
    path: '/train-task',
  },
  {
    name: '训练任务详情',
    path: `/train-task/detail/${id}`,
  },
  {
    name: '实例日志',
  },
];

onMounted(async () => {
  getInstanceList();
});

const getWebSocketLog = (log) => {
  logsInfo.value.podLogs = log;
};

const getInstanceList = async () => {
  POST(taskApiInfo(TASK_TYPE.TASK).getPodList, {
    id,
    pageNum,
    pageSize,
  })
    .then((res) => {
      const { code, data } = res;
      if (code === 0) {
        instanceOptions.value = data.list.map((item) => {
          return {
            label: item?.podName,
            value: item?.podName,
          };
        });
      } else {
        message.error('获取实例列表信息失败，请稍后再试');
      }
    })
    .catch((err) => {
      message.error('获取实例列表信息失败，请稍后再试');
    });
};

// 切换实例
const handleInstanceChange = (value) => {
  requestData.value.podName = value;
};

const resetParams = () => {
  pageParams.value.pageNum = 1;
  logsInfo.value = { podLogs: [], loadMore: false };
};

const searchLogs = (params = {}) => {
  const { loadMore, podName } = params;
  requestData.value.podName = podName;
  if (!loadMore) {
    resetParams();
  } else {
    pageParams.value.pageNum++;
  }
  getLogs(params);
};
// 请求日志接口
const getLogs = async (params = {}) => {
  store.dispatch('updateGlobalLoading', true);
  const { startTime, endTime, key } = params;
  searchParams.value = { startTime, endTime, key };
  const res = await getTaskPodLog({
    ...pageParams.value,
    podName: requestData.value.podName,
    startTime,
    endTime,
    key,
    resGroupId,
    order: '',
  });
  if (res.code === 0) {
    store.dispatch('updateGlobalLoading', false);
    const { podLogs, loadMore } = res.data;
    logsInfo.value = { podLogs: logsInfo.value.podLogs.concat(podLogs || []), loadMore };
  }
};

const download = (obj, fileName) => {
  const link = document.createElement('a');
  const blob = new Blob([obj], { type: 'application/octet-stream' });
  link.style.display = 'none';
  const url = window.URL.createObjectURL(blob);
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

const exportLogs = async (params = {}) => {
  let data = {
    ...pageParams.value,
    resGroupId,
    order: '',
  }
  if (params?.podName) {
    data = { ...data, ...params };
  } else {
    data = { ...data, podName: requestData.value.podName, ...searchParams.value }
  }
  const res = await exportTaskPodLog(data);
  const fileName = decodeURIComponent(res.headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1'));
  download(res.data, fileName);
}
</script>

<style lang="less" scoped>
.train-task-logs {
  height: 100%;
  .log-container {
    position: relative;
    padding: 20px;
    margin: 0px 20px 20px;
    height: calc(100vh - 150px);
    background-color: white;
    color: @jt-text-color-primary;
  }
  .content-title {
    position: relative;
    padding: 0 20px;
  }
  .operation-select {
    position: absolute;
    right: 20px;
    bottom: -8px;
    .btn-export {
      color: @jt-color-white;
    }
    :deep(.ant-btn-default:disabled) {
      background: rgba(0, 20, 26, 0.15);
    }
  }
}
</style>
