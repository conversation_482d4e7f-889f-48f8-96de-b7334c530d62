<template>
  <div>
    <a-tree-select :status="typeError ? 'error' : ''" :dropdown-match-select-width="false" :dropdown-style="{ width: '100%', maxHeight: '400px', overflow: 'auto' }" style="width: 480px" placeholder="请选择" :tree-data="treeData" :load-data="onLoadData" tree-data-simple-mode :get-popup-container="(triggerNode) => triggerNode.parentNode" @select="handleSelect">
      <template #title="{ id, name, isLeaf }">
        <a-tooltip placement="topLeft">
          <template #title>
            <span>{{ name }}</span>
          </template>
          <div v-if="name" :key="id" class="tree-node-box">
            <jt-icon v-if="isLeaf" type="iconwenjian1" class="file-icon"></jt-icon>
            <jt-icon v-else type="iconanliku" class="folder-icon"></jt-icon>
            <div class="name">{{ name }}</div>
          </div>
        </a-tooltip>
      </template>
      <template #notFoundContent>
        <div class="empty-box">
          <a-empty>
            <template #description>
              <div class="desc-box">暂无数据</div>
            </template>
            <template #image>
              <div class="img-box">
                <img :src="imageSrc" alt="" />
              </div>
            </template>
          </a-empty>
        </div>
      </template>
      <!-- <template #suffixIcon><jt-icon type="iconanliku" style="font-size: 18px" /></template> -->
    </a-tree-select>
  </div>
</template>

<script>
import { message } from 'ant-design-vue';
import { mirrorManageApi, storageApi, serviceApi } from '@/apis';
import { requestWithProjectId } from '@/request';
import { MODEL_FILE_SOURCE } from '@/constants/presetAssets'
import cloneDeep from 'lodash/cloneDeep';

export default {
  props: {
    modelSource: {
      type: Object,
      required: true,
    },
  },
  emits: ['changeName'],
  data() {
    return {
      fileName: '',
      fullPath: '',
      size: 0,
      typeError: false,
      treeData: [],
      imageSrc: require('@/assets/images/empty.png'),
      fileServiceApiUrl: '',
      treeDataF: [], // 普通存储与高性能存储数据
      treeDataE: [], // 外部扩展存储数据
      externalOriginData: [], // 请求的外部扩展最原始的数据
      fileInitData: [
        {
          id: 'root_id',
          key: 'root_id',
          isLeaf: false,
          value: 'root_id',
          name: 'root',
          pId: 0,
          disabled: true,
          scopedSlots: {
            title: 'title',
          },
        },
        {
          id: 'work_id',
          key: 'work_id',
          isLeaf: false,
          value: 'work_id',
          name: 'work',
          pId: 'root_id',
          disabled: true,
          scopedSlots: {
            title: 'title',
          },
        },
      ],
      //allowTypes: ['tar','tgr','gz']
    };
  },
  watch: {
    modelSource: {
      handler(newValue, oldValue) {
        if (oldValue?.source !== newValue?.source) {
          this.treeData = [];
          if (newValue?.source === MODEL_FILE_SOURCE.FILE_STORAGE) {
            this.treeData = [...this.fileInitData];
            this.initTreeData();
            this.initExternalTreeData();
          } else {
            this.onLoadData();
          }
        }
      },
    },
  },
  async created() {
    await this.getFileServiceUrl();
    this.treeData = [...this.fileInitData];
    this.initTreeData();
    this.initExternalTreeData();
  },
  methods: {
    async getFileServiceUrl() {
      const { code, data } = await storageApi.getFileServiceUrl();
      if (code !== 0 || !data) {
        message.error('获取业务域文件路径接口异常，请稍后重试');
        return;
      }
      this.fileServiceApiUrl = data;
    },
    // 获取普通存储与高性能存储数据
    async initTreeData() {
      const params = {
        dir: '',
        storageType: '',
      };
      const id = 'work_id';
      const path = '';
      if (!this.fileServiceApiUrl) return;
      const res = await requestWithProjectId.GET(this.fileServiceApiUrl, params);
      if (res.code === 0) {
        const data = res.data.filter((item) => item.objectName !== 'externalstorage/');
        this.treeDataF = this.treeList(id, data, path);
        this.treeData.push(...this.treeDataF);
        // this.treeData = [...this.treeData, ...this.treeDataF, ...this.treeDataE];
      }
    },
    // 获取外部扩展存储数据
    async initExternalTreeData() {
      const res = await serviceApi.getTreeExtendFileUrl();
      if (res.code === 0) {
        const temp = cloneDeep(res.data);
        this.externalOriginData = temp;
        let fakeTreeData = [];
        const id = 'work_id';
        const path = '';
        if (res?.data.length) {
          fakeTreeData = [
            {
              objectName: 'externalstorage/',
              dir: true,
            },
          ];
        }
        this.treeDataE = this.treeList(id, fakeTreeData, path);
        this.treeData.push(...this.treeDataE);
        // this.treeData = [...this.treeData, ...this.treeDataF, ...this.treeDataE];
      }
    },
    handleSelect(value, node) {
      this.fileName = node.name;
      this.fullPath = node.dir && node.path.endsWith('/') ? node.path.slice(0, -1) : node.path;
      this.size = node.size;
      //const suffix = node.name.split('.')[node.name.split('.').length - 1]
      //this.typeError = !this.allowTypes.includes(suffix.toLowerCase())
      this.$emit('changeName', this.fullPath, this.fileName, this.size, this.typeError);
    },
    // 截取2个斜杠之间的内容
    getContentBetweenSlashes(url) {
      const match = url.match(/\/([^\/]*)\/[^\/]*/);
      return match ? match[1] : null;
    },
    // 生成扩展存储第2层级
    formatExternalTreeData(treeNode) {
      let arr = [];
      this.externalOriginData?.forEach((item) => {
        const temp = treeNode.dataRef.path?.replace(/^\//, '');
        const nametemp = temp.endsWith('/') ? temp.slice(0, -1) : temp;
        if (item.startsWith(nametemp)) {
          const str = item.replace(`${nametemp}`, '');
          if (str) {
            if (str.split('/').length - 1 == 1) {
              // 如果只有一个/，直接截取/后面的内容
              const index = str.lastIndexOf('/');
              const s = str.substring(index + 1, str.length);
              if (s) {
                const obj = {
                  objectName: s,
                  dir: true,
                };
                arr.push(obj);
              }
            } else if (str.split('/').length - 1 > 1) {
              // 有多个/,则截取2个第一个斜杠与第2个斜杠之间的内容
              if (this.getContentBetweenSlashes(str)) {
                const obj = {
                  objectName: this.getContentBetweenSlashes(str),
                  dir: true,
                };
                arr.push(obj);
              }
            }
          }
        }
      });
      // 去重
      if (arr.length) {
        arr = Array.from(new Set(arr?.map((item) => JSON.stringify(item))))?.map((item) => JSON.parse(item));
      }
      return arr;
    },
    // 获取对象存储列表数据
    async onLoadData(treeNode) {
      let dirNamePath = '';
      if (treeNode) {
        dirNamePath = treeNode.dataRef.path;
      }
      return new Promise(async (resolve) => {
        const params = {
          dir: dirNamePath && dirNamePath.endsWith('/') ? dirNamePath.slice(0, dirNamePath.length - 1) : dirNamePath,
          storageType: this.modelSource.source === MODEL_FILE_SOURCE.OBJECT_STORAGE ? 'OBJECT_STOR' : '',
        };
        if (this.modelSource.source === MODEL_FILE_SOURCE.OBJECT_STORAGE) {
          params.maker = '';
          params.maxKeys = '';
        }
        const { id = 0, path } = treeNode?.dataRef || {};
        let res = {};
        if (this.modelSource.source === MODEL_FILE_SOURCE.FILE_STORAGE) {
          if (id === 'root_id' || id === 'work_id') {
            resolve(true);
          } else if (dirNamePath.startsWith('externalstorage')) {
            // 如果是外部存储的话，不需要请求数据
            const fakeData = this.formatExternalTreeData(treeNode);
            this.treeData = this.treeData.concat(this.treeList(id, fakeData, path));
            resolve(true);
          } else {
            res = await requestWithProjectId.GET(this.fileServiceApiUrl, params);
            if (res.code === 0) {
              this.treeData = this.treeData.concat(this.treeList(id, res.data, path));
            } else {
              if (treeNode) {
                message.error(`${res.msg}`);
              }
            }
            resolve(true);
          }
        } else {
          res = await mirrorManageApi.getObjectList(params);
          if (res.code === 0) {
            this.treeData = this.treeData.concat(this.treeList(id, res.data, path));
          } else {
            if (treeNode) {
              message.error(`${res.msg}`);
            }
          }
          resolve(true);
        }
      });
    },
    // 数据整理
    treeList(parentId, tree, parentPath = '', arr = []) {
      if (!tree.length) return [];
      for (const item of tree) {
        const random = this.getRandom();
        const node = {
          ...item,
          id: `${item.objectName}-${random}`,
          key: `${item.objectName}-${random}`,
          isLeaf: !item.dir,
          value: `${item.objectName}-${random}`,
          name: this.getFileName(item.objectName),
          title: '',
          path: this.getFilePath(parentPath, item),
          pId: parentId,
          disabled: this.getDisabledStatus(item, parentId, parentPath),
          size: item.size,
          scopedSlots: {
            title: 'title',
          },
        };
        if (!(!item.dir && item.objectName.slice(-1) === '/')) {
          arr.push(node);
        }
      }
      return arr;
    },
    isMiddleDir(item, parentPath) {
      const tempPath = this.getFilePath(parentPath, item);
      const list = this.externalOriginData.filter((item) => item === tempPath);
      if (list.length === 0) return true;
      const tempList = this.externalOriginData.filter((item) => item.includes(tempPath));
      return tempList.length > 1;
    },
    getDisabledStatus(item, parentId) {
      // const externalStatus = parentPath.startsWith('externalstorage') && this.isMiddleDir(item, parentPath) || item.dir && parentId === 0 && item.objectName === 'externalstorage/'
      const externalStatus = item.dir && parentId === 'work_id' && item.objectName === 'externalstorage/';
      const filestorageStatus = item.dir && parentId === 'work_id' && (item.objectName === 'filestorage/' || item.objectName === 'filestorage' || item.objectName === '/filestorage/' || item.objectName === '/filestorage');
      return externalStatus || filestorageStatus || !item.dir;
    },
    getFileName(objectName) {
      if (objectName.includes('/')) {
        if (objectName.endsWith('/')) {
          return objectName.startsWith('/') ? objectName.slice(1).split('/')[objectName.slice(1).split('/').length - 2] : objectName.split('/')[objectName.split('/').length - 2];
        }
        return objectName.startsWith('/') ? objectName.slice(1).split('/')[objectName.slice(1).split('/').length - 1] : objectName.split('/')[objectName.split('/').length - 1];
      }
      return objectName;
    },
    getFilePath(parentPath, item) {
      if (this.modelSource.source === MODEL_FILE_SOURCE.FILE_STORAGE) {
        return `${parentPath ? (parentPath.endsWith('/') ? parentPath : parentPath + '/') : parentPath}${item.objectName}`;
      }
      return item.objectName;
    },
    getRandom() {
      return Math.random().toString(36).substring(2, 6);
    },
  },
};
</script>

<style lang="less" scoped>
.tree-node-box {
  position: relative;
  top: 1px;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 20px;

  .file-icon {
    font-size: 16px;
    color: @jt-primary-color;
    margin-right: 7px;
  }

  .folder-icon {
    font-size: 16px;
    color: #44bc11;
    margin-right: 7px;
  }

  .name {
    flex: 1;
    color: #595959;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

.empty-box {
  padding-bottom: 20px;

  .desc-box {
    margin-top: 100px;
    font-weight: 400;
    font-size: 14px;
    color: #555555;
  }

  .img-box {
    height: 180px;

    img {
      width: 180px;
      height: 100%;
    }
  }
}
</style>
