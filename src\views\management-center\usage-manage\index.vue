<template>
  <div class="operating-center">
    <sub-header :title="title" :bread-crumb="breadCrumb"></sub-header>
    <div class="operating-center-content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import SubHeader from '@/components/subHeader.vue';
import { computed } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const breadCrumb = computed(() => {
  if (route.meta.header && route.meta.header.length > 1) {
    return route.meta.header;
  } else {
    return [];
  }
});

const title = computed(() => {
  if (route.meta.header && route.meta.header.length === 1) {
    return route.meta.header[0].name;
  } else {
    return '用量管理';
  }
});
</script>

<style lang="less" scoped>
.operating-center {
  .operating-center-content {
    margin: 0px;
  }
}
</style>