<template>
  <div>
    <a-form ref="formRef" :model="form" :label-col="labelCol" :rules="rules" :wrapper-col="wrapperCol">
      <!-- 模型介绍 -->
      <a-row class="field-box">
        <a-col :span="colSpan">
          <a-form-item label="模型介绍" name="intro" :class="isError ? 'intro-cont' : ''">
            <mavon-editor ref="funcDesc" v-model="form.intro" placeholder="请输入模型介绍" :autofocus="false" :box-shadow="false" @imgAdd="funcDescImgAdd" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup>
import isEmpty from 'lodash/isEmpty';
import { useRouter } from 'vue-router';

const router = useRouter();
const emit = defineEmits(['toNext', 'toLastStep']);
// form表单相关
const isError = ref(false);
const labelCol = { span: 2 };
const wrapperCol = { span: 20 };
const colSpan = 24;
const formRef = ref(null);
const form = ref({
  intro: '', // 模型介绍
});
const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
  editData: {
    type: Object,
    default: () => {},
  },
});
const onCheckIntro = (rule, value) => {
  return new Promise((resolve, reject) => {
    if (!value.trim()) {
      return reject('请输入模型介绍');
    }
    return resolve();
  });
};

// 校验规则
const rules = {
  intro: [{ required: true, validator: onCheckIntro, trigger: ['change', 'blur'] }],
};

const funcDesc = ref(null);
const funcDescImgAdd = (pos, $file) => {
  const formData = new FormData();
  formData.append('file', $file);
  // uploadImage(formData).then((res) => {
  //   if (res.state === 'OK') {
  //     const imgUrl = res.body;
  //     refs.funcDesc.$img2Url(pos, imgUrl);
  //   }
  // });
};

const handleValide = async () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate().then(
      () => {
        isError.value = false;
        resolve(true);
      },
      (err) => {
        isError.value = true;
        reject(new Error(false));
      }
    );
  });
};

const integrateData = () => {
  return form.value;
};

defineExpose({
  validate: handleValide,
  getData: integrateData,
});
watch(
  () => props.defaultData,
  (val) => {
    if (!isEmpty(props.defaultData)) {
      form.value.intro = props.defaultData.intro;
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.intro-cont .ant-form-item-control-input-content {
  border: 1px solid #fe3a47;
}
:deep(.intro-cont .ant-form-item-control-input-content) {
  border: 1px solid #fe3a47;
}
:deep(.v-note-wrapper.fullscreen) {
  left: 220px;
  top: 50px;
}
:deep(.v-note-wrapper .v-note-panel .v-note-show .v-show-content) {
  word-wrap: break-word;
}
:deep(.add-image-link-wrapper) {
  background: rgba(0,0,0,0.1);
}
</style>