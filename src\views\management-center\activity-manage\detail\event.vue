<template>
  <a-drawer v-model:open="open" :footer="null" title="实例事件" width="736" destroy-on-close @close="handleCancel">
    <a-config-provider>
      <template #renderEmpty>
        <jt-empty :show-operation="true" title="事件" />
      </template>
      <a-table :columns="columns" :data-source="eventsData?.list" :pagination="false" @change="tableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'message'">
            <a-tooltip v-if="record.message" placement="top">
              <template #title>{{ record.message }}</template>
              <span class="event-msg">{{ record.message }}</span>
            </a-tooltip>
            <span v-else>--</span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
    <jt-pagination :total="eventsData?.total" :page-num="pageParams.pageNum" :page-size="pageParams.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, defineModel } from 'vue';
import { TABLE_SORT_FIELD } from '@/views/home/<USER>/constant';
import { getEventList } from '@/apis/activityManage';
import { TASK_TYPE } from '@/constants/trainTask';

const props = defineProps({
  activeKey: {
    type: String,
    required: true,
  },
  resourceGroupId: {
    type: String,
    required: true,
  },
  podName: {
    type: String,
    required: true,
  },
  projectId: {
    type: String,
    required: true,
  },
});
const emit = defineEmits(['close']);

const open = defineModel('open', { required: true, default: false });
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const eventsData = ref();

const columns = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '事件原因',
    dataIndex: 'reason',
    key: 'reason',
  },
  {
    title: '事件信息',
    dataIndex: 'message',
    key: 'message',
  },
  {
    title: '更新时间',
    dataIndex: 'lastTime',
    key: 'lastTime',
  },
];

watch(
  () => open.value,
  (visible) => {
    if (visible) {
      reqEventsList();
    } else {
      pageParams.value.pageNum = 1;
      eventsData.value = {
        list: [],
        total: 0
      }
    }
  }
);

const reqEventsList = async (order = TABLE_SORT_FIELD['descend']) => {
  const req = {
    podName: props.podName,
    resourceGroupId: props.resourceGroupId,
    projectId: props.projectId,
    ...pageParams.value,
    order,
  };
  const res = await getEventList(props.activeKey, req);
  if (res.code === 0) {
    if (props.activeKey === TASK_TYPE.INFE) {
      const infeData = {
        list: res.data.data,
        total: res.data.totalCount,
      };
      eventsData.value = infeData;
    } else {
      eventsData.value = res.data;
    }
  }
};

const changePageSize = (pageSize) => {
  pageParams.value.pageSize = +pageSize;
  reqEventsList();
};

const changePageNum = (page) => {
  pageParams.value.pageNum = +page;
  reqEventsList();
};

const handleCancel = () => {
  emit('close');
};

const tableChange = (pagination, filters, sorter) => {
  reqEventsList(TABLE_SORT_FIELD[sorter.order]);
};
</script>
<style lang="less" scoped>
.event-msg {
  display: inline-block;
  width: 290px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
