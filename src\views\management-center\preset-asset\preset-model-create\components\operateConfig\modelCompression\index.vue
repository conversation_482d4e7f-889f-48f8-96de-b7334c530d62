<template>
  <div class="form-box">
    <detail-sub-header title="基础配置" :size="14" style="margin-bottom: 20px"></detail-sub-header>
    <a-form ref="formRef" :model="form" :label-col="labelCol" :rules="rules">
      <!-- 压缩策略 -->
      <a-form-item label="压缩策略" name="strategy" class="big-gap">
        <a-checkbox-group v-model:value="form.strategy">
          <a-checkbox v-for="item in strategyOptions" :value="item.value" name="type" style="margin-right: 20px" :key="item.value">{{ item.label }}</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <!-- 资源&镜像配置 -->
    </a-form>
    <div>
      <detail-sub-header title="资源&镜像配置" style="margin-bottom: 16px" :size="14"></detail-sub-header>
      <resource-table :list="strategyConfigList" :strategyOption="props.defaultData.strategy" :strategy-list="form.strategy" @getTableData="getResourceData"></resource-table>
    </div>
  </div>
</template>

<script setup>
import isEmpty from 'lodash/isEmpty';
import detailSubHeader from '@/components/detail-sub-header';
import resourceTable from './resourceTable.vue';
import { compressManageApi } from '@/apis';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const strategyOptions = ref([]);
const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
});
// form表单相关
const labelCol = {
  style: {
    width: '80px',
  },
};
const isValidate = ref(false);
const isResEdit = ref(false);
let strategyConfigList = reactive([]);
const colSpan = 24;
const formRef = ref(null);
const form = ref({
  strategy: [], // 资源&镜像配置
});

// 校验规则
const rules = {
  strategy: [{ required: true, message: '请选择压缩策略' }],
};

const getResourceData = (data, isEdit) => {
  strategyConfigList = data;
  isResEdit.value = isEdit;
  emits('strategyChange', strategyConfigList);
};
// 获取压缩策略数据源
const getStrategyOption = () => {
  compressManageApi.getStrategyList().then((res) => {
    if (res.code === 0) {
      strategyOptions.value = res.data.map((item) => {
        return { value: item, label: item };
      });
    }
  });
};
const emits = defineEmits(['is-validate', 'strategyChange']);
const isArraySubsetOfAnother = (A, B, keysToCompare) => {
  return A.every((a) => {
    return B.some((b) => {
      return keysToCompare.every((key) => {
        return a === b[key];
      });
    });
  });
};
const handleValide = async () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate().then(
      () => {
        let resFlag = strategyConfigList.every((item) => item.imageId !== undefined);
        let flag = null;
        let newArr = [];
        let subsetFlag = null //判断选压缩策略必须配置资源镜像管理
        if (resFlag) {
          strategyConfigList.forEach((item) => {
            newArr = item.strategy.split(',');
            if (flag) return;
            flag = newArr.some((subItem) => form.value.strategy.indexOf(subItem) === -1);
          });
        }
        subsetFlag = isArraySubsetOfAnother(form.value.strategy, strategyConfigList, ['strategy'])
        if (strategyConfigList.length && subsetFlag && resFlag && !flag && !isResEdit.value) {
          isValidate.value = true;
          emits('is-validate', isValidate.value);
          resolve(true);
        } else {
          isValidate.value = false;
          emits('is-validate', isValidate.value);
        }
      },
      (err) => {
        isValidate.value = false;
        emits('is-validate', isValidate.value);
        reject(new Error(false));
      }
    );
  });
};
const clearValidate = () => {
  formRef.value.clearValidate();
};
const integrateData = () => {
  return { ...form.value, strategyConfigList };
};
defineExpose({
  validate: handleValide,
  getData: integrateData,
  clearValidate: clearValidate,
});
onMounted(() => {
  getStrategyOption();
});
watch(
  () => props.defaultData,
  (val) => {
    if (!isEmpty(props.defaultData)) {
      form.value.strategy = val.strategy ? (Array.isArray(val.strategy) ? val.strategy : val.strategy.split(',')) : [];
      val.strategyConfigList && val.strategyConfigList.length ? (strategyConfigList = val.strategyConfigList.filter((item) => item.replicasLowerLimit !== 0)) : (strategyConfigList = []);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => form,
  (val) => {
    emits('is-validate', true);
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.big-gap {
  margin-bottom: 24px;
}
</style>
