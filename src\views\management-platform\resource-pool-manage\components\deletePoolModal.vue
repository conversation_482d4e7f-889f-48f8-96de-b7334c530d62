<template>
  <a-modal v-model:open="open" :width="480" style="top: 20px" :body-style="{ marginTop: '20px', marginBottom: '28px', paddingLeft: '20px' }" @ok="handleOk">
    <template #title>
      <span style="display: flex; align-items: center"><jt-icon type="iconwarning-circle-fill" style="color: #ff0000; margin-right: 8px; font-size: 21px" />确定要删除这条资源类型吗？</span>
    </template>
    <a-spin :spinning="loading">
      <p class="delete-infer">{{ `确定删除资源类型“${resourceInfo?.clusterName}-${resourceInfo?.resourceType === 'CPU' ? resourceInfo?.resourceType : `${resourceInfo?.resourceType}-${resourceInfo?.gpuCardType}` }”吗？` }}</p>
      <div class="content-body">
        <a-input v-model:value="inputValue" :status="inputStatus" placeholder="请输入“确定删除”以确保您已知悉删除后的风险" autofocus />
        <div v-if="inputStatus === 'error'" class="input-Status">请输入“确定删除”</div>
      </div>
    </a-spin>
    <template #footer>
      <div>
        <a-button type="primary" danger class="btn-delete" :disabled="!inputValue || inputStatus === 'error'" @click="handleOk()">删除</a-button>
        <a-button type="default" @click="onClose()">取消</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { resourcePoolApi } from '@/apis';
import ErrorCode from '@/constants/errorCode';

const props = defineProps({
  resourceInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['ok']);

const open = defineModel('open', { type: Boolean, default: false });
const inputValue = ref('');
const inputStatus = ref('');
const loading = ref(false);

const handleOk = async () => {
  if (!inputValue || inputStatus === 'error') return;
  const param = {
    poolId: props.resourceInfo.poolId,
    clusterId: props.resourceInfo.clusterId,
    gpuCardTypeId: props.resourceInfo.gpuCardTypeId,
  };
  const res = await resourcePoolApi.deleteResourceType(param);
  if (res.code === 0 && res.data) {
    message.success('资源类型删除成功');
    emit('ok', inputValue.value);
    onClose();
  } else {
    message.error(ErrorCode[res.code] || ErrorCode[202057]);
  }
};

const onClose = () => {
  open.value = false;
  inputStatus.value = '';
  inputValue.value = '';
};

watch(inputValue, (val) => {
  if (val && val !== '确定删除') {
    inputStatus.value = 'error';
  } else {
    inputStatus.value = '';
  }
});
</script>

<style lang="less" scoped>
.delete-infer {
  color: #f53922;
  margin-bottom: 20px;
}
.content-body {
  position: relative;
  :deep(.ant-input.ant-input-status-error) {
    border-color: #f53922;
  }
  .input-Status {
    position: absolute;
    margin-top: 5px;
    color: #f53922;
  }
}
.btn-delete:disabled {
  background: #fbb7a5;
  border-radius: 2px;
  color: #ffffff;
}
</style>
