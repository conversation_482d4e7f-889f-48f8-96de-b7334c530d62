<template>
  <a-drawer v-model:open="open" title="镜像详情" width="528">
    <div v-if="imageType === IMAGE_TYPE.PRESET" class="descriptions-wrap">
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像名称：</p>
        <p class="descriptions-item-content">{{ props.imageDetails.imageName }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">适用场景：</p>
        <p class="descriptions-item-content">
          <jt-tag v-if="props.imageDetails.scene === IMAGE_SCENE_TYPE.TRAIN || props.imageDetails.scene === IMAGE_SCENE_TYPE.ALL" rounded="small" color="blue">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN] }}</jt-tag>
          <jt-tag v-if="props.imageDetails.scene === IMAGE_SCENE_TYPE.REASON || props.imageDetails.scene === IMAGE_SCENE_TYPE.ALL" rounded="small" color="green">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON] }}</jt-tag>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">Python版本：</p>
        <p class="descriptions-item-content">
          <jt-tag rounded="small" color="blue">
            {{ props.imageDetails.pyVersion }}
          </jt-tag>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像标签：</p>
        <p class="descriptions-item-content">
          <jt-tag v-for="(x, i) in props.imageDetails.tags" :key="i" rounded="small" color="mirror-blue-jt">{{ x }}</jt-tag>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">框架：</p>
        <p class="descriptions-item-content">
          
          <a-space>
            <jt-tag v-for="item in getFrameValue(props.imageDetails.framework)" :key="item" rounded="small" color="mirror-blue-jt"> {{ item }} </jt-tag>
          </a-space>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">架构：</p>
        <p class="descriptions-item-content">{{ getArchitectureMsg[props.imageDetails.architecture] }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">芯片类型：</p>
        <p class="descriptions-item-content">{{ getChipTypeMsg[props.imageDetails.chipType] }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像描述：</p>
        <p class="descriptions-item-content">{{ props.imageDetails.description }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像地址：</p>
        <p class="descriptions-item-content content-copy">
          <CopyOutlined class="button-copy" @click="copyContent(props.imageDetails.address)" />
          <span>
            {{ props.imageDetails.address }}
          </span>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">创建时间：</p>
        <p class="descriptions-item-content">{{ props.imageDetails.createTime }}</p>
      </div>
    </div>
    <div v-else class="descriptions-wrap">
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像名称：</p>
        <p class="descriptions-item-content">{{ props.imageDetails.imageGroupName }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">适用场景：</p>
        <p class="descriptions-item-content">
          <jt-tag v-if="props.imageDetails.scene === IMAGE_SCENE_TYPE.TRAIN || props.imageDetails.scene === IMAGE_SCENE_TYPE.ALL" rounded="small" color="blue">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN] }}</jt-tag>
          <jt-tag v-if="props.imageDetails.scene === IMAGE_SCENE_TYPE.REASON || props.imageDetails.scene === IMAGE_SCENE_TYPE.ALL" rounded="small" color="green">{{ IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON] }}</jt-tag>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">架构：</p>
        <p class="descriptions-item-content">{{ getArchitectureMsg[props.imageDetails.architecture] }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">芯片类型：</p>
        <p class="descriptions-item-content">{{ getChipTypeMsg[props.imageDetails.chipType] }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像版本：</p>
        <p class="descriptions-item-content">
          <jt-tag rounded="small" color="blue">{{ props.imageDetails.version }}</jt-tag>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像标签：</p>
        <p class="descriptions-item-content">
          <jt-tag v-for="(x, i) in props.imageDetails.tags" :key="i" rounded="small" color="mirror-blue-jt">{{ x }}</jt-tag>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像描述：</p>
        <p class="descriptions-item-content">{{ props.imageDetails.description }}</p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">镜像地址：</p>
        <p class="descriptions-item-content content-copy">
          <CopyOutlined class="button-copy" @click="copyContent(props.imageDetails.address)" />
          <span>
            {{ props.imageDetails.address }}
          </span>
        </p>
      </div>
      <div class="descriptions-item">
        <p class="descriptions-item-label">创建时间：</p>
        <p class="descriptions-item-content">{{ props.imageDetails.createTime }}</p>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { h } from 'vue';

import { CopyOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { handleCopy } from '@/utils';
import { IMAGE_TYPE } from '@/constants/trainTask.js';
import { IMAGE_SCENE_TYPE, IMAGE_SCENE_MSG } from '@/constants/image';

const props = defineProps({
  imageDetails: {
    type: Object,
    default: () => ({}),
  },
  imageType: {
    type: String,
    default: 'a',
  },
});
const open = defineModel('open', { type: Boolean, default: false });

const getFrameValue = (obj) => {
  const frame = obj.frameworkName.split(',');
  const version = obj.frameworkVersion.split(',');
  const list = frame.map((item, index) => {
    return `${item} ${version[index]}`;
  });
  return list;
};
const copyContent = (content) => {
  handleCopy(content); //message.success('复制成功');
};

const getArchitectureMsg = {
  0: 'X86',
  1: 'ARM',
};
const getChipTypeMsg = {
  0: 'CPU',
  1: 'GPU',
  2: 'NPU',
  3: 'TPU',
};

watch(
  () => open.value,
  () => {
    if (open.value) {
    }
  }
);
</script>

<style lang="less" scoped>
:deep(.ant-drawer-body) {
  padding: 20px 24px;
}
.descriptions-item {
  display: flex;
  margin-bottom: 20px;
  font-size: @jt-font-size-base;
  color: @jt-text-color-primary-opacity07;
  line-height: 21px;
}
.descriptions-item-label {
  width: 100px;
  text-align: right;
  margin-right: 4px;
  flex-shrink: 0;
  line-height: 26px;
}
.descriptions-item-content {
  word-break: break-all;
  color: @jt-text-color-primary;
  display: flex;
  line-height: 26px;
  &.content-copy {
    line-height: 21px;
    span {
      display: block;
      word-break: break-all;
    }
  }
}
.button-copy {
  color: #7f828f;
  margin-top: 5px;
  margin-right: 3px;
  &:hover {
    color: @jt-primary-color;
  }
}
</style>
