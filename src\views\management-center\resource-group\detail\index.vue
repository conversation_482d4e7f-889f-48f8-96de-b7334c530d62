<template>
  <div class="resource-group-detail-container">
    <h1 class="item-title">{{ baseInfo.groupName }}</h1>
    <div class="base-info-container card-container">
      <a-flex class="item-title-container">
        <h2 class="item-title base-title">基本信息</h2>
        <div v-if="editPermission" class="operation-box">
          <a-space>
            <a-button class="exit-class" @click="handleDelete">
              <jt-icon type="iconshanchu1" />
              <span>删除</span>
            </a-button>
            <a-button ghost type="primary" @click="handleEdit">
              <jt-icon type="iconbianji" />
              <span>编辑</span>
            </a-button>
          </a-space>
        </div>
      </a-flex>
      <a-divider></a-divider>

      <div class="item-title-container padding-bottom"></div>
      <div class="item-description">
        <a-descriptions :label-style="descripLabelStyle">
          <a-descriptions-item label="资源组名称">{{ baseInfo.groupName }}</a-descriptions-item>
          <a-descriptions-item label="资源组类型">{{ baseInfo.attribute === 'private' ? '专属资源组' : '公共资源组' || '--' }}</a-descriptions-item>
          <a-descriptions-item label="适用场景">
            <jt-tag v-for="tag of baseInfo.sceneType" :key="tag" rounded="small" :color="SCENARIO_TYPE_TAG_COLOR[tag]">{{ SCENARIO_TYPE[tag] }}</jt-tag>
          </a-descriptions-item>
          <a-descriptions-item label="资源类型">{{ baseInfo.type === 'gpucard' ? '加速卡' : baseInfo.type === 'cpu' ? 'CPU' : '--' }}</a-descriptions-item>
          <a-descriptions-item v-if="baseInfo.type === 'gpucard'" label="加速卡类型">{{ baseInfo.gpucardType }}</a-descriptions-item>
          <a-descriptions-item label="所属集群">{{ baseInfo.clusterName || '--' }}</a-descriptions-item>
          <a-descriptions-item label="节点数">{{ baseInfo.nodenum }}</a-descriptions-item>
          <a-descriptions-item v-if="baseInfo.type === 'gpucard'" label="已用 / 总加速卡">{{ Number(baseInfo.gpucardUsed) }} / {{ Number(baseInfo.gpucardTotal) }}卡</a-descriptions-item>
          <a-descriptions-item label="已用 / 总CPU">{{ Number(baseInfo.cpuUsed) }} / {{ Number(baseInfo.cpuTotal) }}核</a-descriptions-item>
          <a-descriptions-item label="已用 / 总内存">{{ Number(baseInfo.memoryUsed) }} / {{ Number(baseInfo.memoryTotal) }}GB</a-descriptions-item>
          <a-descriptions-item label="已关联 / 可关联项目">{{ baseInfo.yglProjectNum }} / {{ baseInfo.kglProjectNum }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ baseInfo.createTime || '--' }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions :label-style="descripLabelStyle">
          <a-descriptions-item label="资源组描述">{{ baseInfo?.description || '--' }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </div>

    <div class="card-container">
      <a-tabs v-model:activeKey="currentTab">
        <a-tab-pane :key="CURRENT_TAB_MAP.RESOURCE" :tab="CURRENT_TAB_MAP.RESOURCE"> </a-tab-pane>
        <a-tab-pane :key="CURRENT_TAB_MAP.NODE" :tab="CURRENT_TAB_MAP.NODE"> </a-tab-pane>
        <a-tab-pane :key="CURRENT_TAB_MAP.PROJECT" :tab="CURRENT_TAB_MAP.PROJECT"> </a-tab-pane>
        <template #rightExtra>
          <resource-tabs-title v-if="currentTab === CURRENT_TAB_MAP.NODE" title="节点列表" :type="CURRENT_TAB_MAP.NODE" search-place-holder="请输入节点名称" :show-edit="editPermission" @search="(e) => (nodeSearchValue = e)" @reload="() => tabsReload(currentTab)" @edit="() => tabsEdit(currentTab)" />
          <resource-tabs-title v-if="currentTab === CURRENT_TAB_MAP.PROJECT" title="关联项目" :type="CURRENT_TAB_MAP.PROJECT" search-place-holder="请输入项目空间名称" :show-edit="editPermission && baseInfo.attribute === 'private'" @search="(e) => (projectSearchValue = e)" @reload="() => tabsReload(currentTab)" @edit="() => tabsEdit(currentTab)" />
        </template>
      </a-tabs>

      <div class="tab-pannel">
        <resource-pannel v-if="currentTab === CURRENT_TAB_MAP.RESOURCE && baseInfo.id" :id-item="idItem"/>
        <related-node-pannel v-if="currentTab === CURRENT_TAB_MAP.NODE && baseInfo.id" ref="relatedNodePannelRef" :search-value="nodeSearchValue" />
        <related-project-pannel v-if="currentTab === CURRENT_TAB_MAP.PROJECT && baseInfo.id" ref="relatedProjectPannelRef" :search-value="projectSearchValue" />
      </div>
    </div>
    <edit-dlg v-model:visible="editItem.editDlgVisible" :edit-item="editItem" @close="closeEditModal"></edit-dlg>
    <delete-dlg v-if="deleteItem.deleteDlgVisible" :delete-item="deleteItem" @close="closeDeleteModal"></delete-dlg>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import { MGT_RESGROUP_EDIT_AUTH, CURRENT_TAB_MAP } from '@/constants/resourceGroup';
import { checkPermissionByApvAndMgtItem } from '@/keycloak';
import { useStore } from 'vuex';

import relatedNodePannel from './relatedNodePannel/index.vue';
import relatedProjectPannel from './relatedProjectPannel/index.vue';
import resourceTabsTitle from './components/resourceTabsTitle.vue';
import resourcePannel from './resoucePannel.vue';
import editDlg from './editDlg.vue';
import deleteDlg from './deleteDlg.vue';
import { SCENARIO_TYPE_MAP, SCENARIO_TYPE, SCENARIO_TYPE_TAG_COLOR } from '@/constants/nodeList';

import { getResourceGroupDetail } from '@/apis/resourceInfo.js';

const router = useRouter();
const route = useRoute();
const { dispatch } = useStore();

const editPermission = checkPermissionByApvAndMgtItem(MGT_RESGROUP_EDIT_AUTH);

const nodeSearchValue = ref('');
const projectSearchValue = ref('');
const relatedNodePannelRef = ref(null);
const relatedProjectPannelRef = ref(null);

const descripLabelStyle = {
  justifyContent: 'flex-end',
  minWidth: '140px',
};

const currentTab = ref(CURRENT_TAB_MAP.RESOURCE);

watch(currentTab, (val) => {
  nodeSearchValue.value = '';
  projectSearchValue.value = '';
  router.replace({ query: { ...route.query, tab: val } });
});

const id = ref('');
const idItem = ref({});
onMounted(async () => {
  currentTab.value = route.query.tab || CURRENT_TAB_MAP.RESOURCE;
  //接收参数值
  id.value = route.query.groupId || '';
  idItem.value = id.value;
  await getBaseInfo();
});

const baseInfo = ref({});

const getBaseInfo = async () => {
  const params = {
    groupId: id.value,
  };
  const res = await getResourceGroupDetail(params);
  if (res.code === 0) {
    baseInfo.value = res.data;
    baseInfo.value.sceneType = res.data.scene == SCENARIO_TYPE_MAP.ALL ? [SCENARIO_TYPE_MAP.DATA, SCENARIO_TYPE_MAP.TRAIN, SCENARIO_TYPE_MAP.OPTIMIZE, SCENARIO_TYPE_MAP.INFERENCE] : String(res.data.scene).split(',');
    dispatch('updateResgroupInfo', toRaw(baseInfo));
  }
};

const tabsReload = (currentTab) => {
  if (currentTab === CURRENT_TAB_MAP.NODE) {
    relatedNodePannelRef.value.reload();
  } else if (currentTab === CURRENT_TAB_MAP.PROJECT) {
    relatedProjectPannelRef.value.reload();
  }
};
const tabsEdit = (currentTab) => {
  if (currentTab === CURRENT_TAB_MAP.NODE) {
    relatedNodePannelRef.value.edit();
  } else if (currentTab === CURRENT_TAB_MAP.PROJECT) {
    relatedProjectPannelRef.value.edit();
  }
};

const editDlgVisible = ref(false);
const editItem = ref({
  editDlgVisible: false,
});
const handleEdit = () => {
  editItem.value = { ...baseInfo.value, editDlgVisible: true };
};
const closeEditModal = () => {
  editItem.value.editDlgVisible = false;
  getBaseInfo();
};

//删除
const deleteDlgVisible = ref(false);
const deleteItem = ref({
  deleteDlgVisible: false,
  id: id,
});

const handleDelete = () => {
  deleteItem.value = {
    groupId: id.value,
    groupName: baseInfo.value.groupName,
    deleteDlgVisible: true,
  };
};
const closeDeleteModal = () => {
  deleteItem.value.deleteDlgVisible = false;
};
</script>

<style lang="less" scoped>
.resource-group-detail-container {
  padding: 20px;
  padding-left: 0;
  margin-top: -15px;
}

.card-container {
  padding: 24px 20px 20px 20px;
  margin-bottom: 20px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
  border-radius: 4px;
  border: 1px solid #ffffff;
}

.item-title-container {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  &.padding-bottom {
    padding-bottom: 20px;
  }
}
.item-description {
  margin-left: -37px;
}

.item-title {
  font-weight: 600;
  font-size: 16px;
  color: #00141a;
  margin-bottom: 18px;
}

.base-title {
  margin-bottom: 0px;
}
.exit-class {
  color: rgba(0, 20, 26, 0.7);
  box-shadow: none;
}

.exit-class:hover {
  color: #f53922;
  border-color: #f53922;
  box-shadow: none;
}

:deep(.ant-divider-horizontal) {
  margin: 10px 0;
}
:deep(.ant-descriptions-item-label) {
  color: rgba(0, 20, 26, 0.7);
}

:deep(.ant-tabs-nav) {
  margin-bottom: 24px;
}
</style>
