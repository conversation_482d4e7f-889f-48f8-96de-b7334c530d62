import { watch, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';

/**
 * 通用列表状态记忆Hook
 * 支持将列表的搜索、筛选、排序、分页状态同步到URL query参数
 * @param {Object} stateRefs - 包含响应式状态的对象
 * @param {Object} options - 配置选项
 * @param {string} options.prefix - query参数前缀，用于避免冲突
 * @param {boolean} options.debounce - 是否防抖，默认true
 * @param {number} options.debounceMs - 防抖延迟，默认300ms
 * @returns {Object} - 包含初始化和手动同步方法
 */
export function useListStateMemory(stateRefs = {}, options = {}) {
  const router = useRouter();
  const route = useRoute();

  const { prefix = '', debounce = true, debounceMs = 300 } = options;

  let debounceTimer = null;

  /**
   * 获取响应式引用的值，兼容ref和reactive
   */
  const getValue = (stateRef) => {
    // 如果是ref对象，通过.value访问
    if (stateRef && typeof stateRef === 'object' && 'value' in stateRef) {
      return stateRef.value;
    }
    // 如果是reactive对象，直接返回
    return stateRef;
  };

  /**
   * 设置响应式引用的值，兼容ref和reactive
   */
  const setValue = (stateRef, newValue) => {
    // 如果是ref对象，通过.value设置
    if (stateRef && typeof stateRef === 'object' && 'value' in stateRef) {
      stateRef.value = newValue;
    } else {
      // 如果是reactive对象，需要逐个属性赋值
      if (typeof newValue === 'object' && newValue !== null) {
        Object.assign(stateRef, newValue);
      }
    }
  };

  /**
   * 生成带前缀的query key
   */
  const getQueryKey = (key, subKey = '') => {
    const baseKey = prefix ? `${prefix}_${key}` : key;
    return subKey ? `${baseKey}_${subKey}` : baseKey;
  };

  /**
   * 将复杂对象扁平化为多个query参数
   */
  const flattenObject = (obj, parentKey = '') => {
    const result = {};

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = parentKey ? `${parentKey}_${key}` : key;

      if (value === null || value === undefined) {
        continue;
      }

      if (typeof value === 'object' && !Array.isArray(value)) {
        // 递归处理嵌套对象
        Object.assign(result, flattenObject(value, fullKey));
      } else if (Array.isArray(value)) {
        // 数组处理：使用逗号分隔
        if (value.length > 0) {
          result[fullKey] = value.join(',');
        }
      } else {
        // 基本类型直接存储
        const stringValue = String(value);
        if (stringValue.trim() !== '') {
          result[fullKey] = stringValue;
        }
      }
    }

    return result;
  };

  /**
   * 将扁平化的query参数恢复为对象
   */
  const unflattenObject = (flatObj, targetKey) => {
    const result = {};
    const keyPrefix = prefix ? `${prefix}_${targetKey}_` : `${targetKey}_`;

    for (const [key, value] of Object.entries(flatObj)) {
      if (key.startsWith(keyPrefix)) {
        const relativePath = key.substring(keyPrefix.length);
        const pathParts = relativePath.split('_');

        let current = result;
        for (let i = 0; i < pathParts.length - 1; i++) {
          const part = pathParts[i];
          if (!current[part]) {
            current[part] = {};
          }
          current = current[part];
        }

        const lastKey = pathParts[pathParts.length - 1];

        // 尝试恢复数组（检查是否包含逗号）
        if (typeof value === 'string' && value.includes(',')) {
          // 尝试转换为数字数组，如果失败则保持字符串数组
          const arrayValue = value.split(',');
          const numericArray = arrayValue.map((v) => {
            const num = Number(v);
            return isNaN(num) ? v : num;
          });
          current[lastKey] = numericArray;
        } else {
          // 尝试转换为数字，如果失败则保持字符串
          const numValue = Number(value);
          current[lastKey] = isNaN(numValue) ? value : numValue;
        }
      }
    }

    return result;
  };

  /**
   * 将值序列化为query字符串
   */
  const serializeValue = (value, key) => {
    if (value === null || value === undefined) return {};

    if (typeof value === 'string') {
      // 过滤空字符串
      if (value.trim() === '') return {};
      return { [getQueryKey(key)]: value };
    }

    if (typeof value === 'number') {
      return { [getQueryKey(key)]: String(value) };
    }

    if (typeof value === 'object') {
      // 对象扁平化处理
      const flattened = flattenObject(value);
      const result = {};

      for (const [flatKey, flatValue] of Object.entries(flattened)) {
        result[getQueryKey(key, flatKey)] = flatValue;
      }

      return result;
    }

    return { [getQueryKey(key)]: String(value) };
  };

  /**
   * 从query字符串反序列化值
   */
  const deserializeValue = (queryObj, key, defaultValue) => {
    // 简单值的处理
    const simpleKey = getQueryKey(key);
    if (queryObj[simpleKey] !== undefined) {
      const queryValue = queryObj[simpleKey];

      // 如果默认值是数字，转换为数字
      if (typeof defaultValue === 'number') {
        const num = Number(queryValue);
        return isNaN(num) ? defaultValue : num;
      }

      // 默认返回字符串
      return queryValue;
    }

    // 复杂对象的处理
    if (typeof defaultValue === 'object' && defaultValue !== null) {
      const unflattened = unflattenObject(queryObj, key);
      return Object.keys(unflattened).length > 0 ? unflattened : defaultValue;
    }

    return defaultValue;
  };

  /**
   * 从URL query恢复状态
   */
  const initFromQuery = () => {
    const currentQuery = route.query;

    Object.keys(stateRefs).forEach((key) => {
      const stateRef = stateRefs[key];
      const currentValue = getValue(stateRef);
      const deserializedValue = deserializeValue(currentQuery, key, currentValue);

      // 只有当值确实不同时才更新
      if (JSON.stringify(deserializedValue) !== JSON.stringify(currentValue)) {
        setValue(stateRef, deserializedValue);
      }
    });
  };

  /**
   * 将当前状态同步到URL query
   */
  const syncToQuery = () => {
    const currentQuery = { ...route.query };
    let hasChanges = false;

    // 先清除所有相关的参数
    Object.keys(currentQuery).forEach((queryKey) => {
      Object.keys(stateRefs).forEach((stateKey) => {
        const expectedPrefix = getQueryKey(stateKey);
        if (queryKey === expectedPrefix || queryKey.startsWith(expectedPrefix + '_')) {
          delete currentQuery[queryKey];
          hasChanges = true;
        }
      });
    });

    // 添加新的参数
    Object.keys(stateRefs).forEach((key) => {
      const currentValue = getValue(stateRefs[key]);
      const serializedParams = serializeValue(currentValue, key);

      Object.entries(serializedParams).forEach(([paramKey, paramValue]) => {
        currentQuery[paramKey] = paramValue;
        hasChanges = true;
      });
    });

    if (hasChanges) {
      router.replace({
        path: route.path,
        query: currentQuery,
      });
    }
  };

  /**
   * 防抖版本的同步方法
   */
  const debouncedSyncToQuery = () => {
    if (!debounce) {
      syncToQuery();
      return;
    }

    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
      syncToQuery();
    }, debounceMs);
  };

  /**
   * 清除所有相关的query参数
   */
  const clearQuery = () => {
    const currentQuery = { ...route.query };
    let hasChanges = false;

    Object.keys(stateRefs).forEach((key) => {
      const expectedPrefix = getQueryKey(key);

      Object.keys(currentQuery).forEach((queryKey) => {
        if (queryKey === expectedPrefix || queryKey.startsWith(expectedPrefix + '_')) {
          delete currentQuery[queryKey];
          hasChanges = true;
        }
      });
    });

    if (hasChanges) {
      router.replace({
        path: route.path,
        query: currentQuery,
      });
    }
  };

  // 监听状态变化并自动同步到query
  // 为每个状态创建单独的watch，确保可靠的响应式监听
  Object.entries(stateRefs).forEach(([key, stateRef]) => {
    watch(
      stateRef,
      () => {
        nextTick(() => {
          debouncedSyncToQuery();
        });
      },
      {
        deep: true, // 深度监听对象变化
        immediate: false, // 避免初始化时触发
      }
    );
  });

  return {
    initFromQuery,
    syncToQuery,
    clearQuery,
  };
}
