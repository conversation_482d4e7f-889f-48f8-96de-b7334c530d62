<template>
  <div class="top-board">
    <LoadingSpin v-if="loading" :loading="loading" style="height: 80px" />
    <div v-else>
      <div class="box-title">数据概览</div>
      <div class="dashboard-box">
        <div class="label-left-line left-box">
          <img src="@/assets/images/dashboard/project/<EMAIL>" alt="" class="icon-item" />
          <div class="title-item">用户数</div>
          <div class="item-count">
            <div v-if="dataStat.userSettledCount < 10000" class="item">
              <div class="text-total">{{ formatNumber(dataStat.userSettledCount) }}</div>
              <div class="text-ps">入驻数</div>
            </div>
            <div v-else class="item">
              <div class="text-total">{{ formatNumber(dataStat.userSettledCount) }}</div>
              <div class="text-ps">入驻数（万）</div>
            </div>
            <div class="item" v-if="dataStat.userDayActiveCount < 10000">
              <div class="text-total">{{ formatNumber(dataStat.userDayActiveCount) }}</div>
              <div class="text-ps">日活</div>
              <div class="value"></div>
            </div>
            <div class="item" v-else>
              <div class="text-total">{{ formatNumber(dataStat.userDayActiveCount) }}</div>
              <div class="text-ps">日活（万）</div>
              <div class="value"></div>
            </div>
            <div class="item" v-if="dataStat.userMonthActiveCount < 10000">
              <div class="text-total">{{ formatNumber(dataStat.userMonthActiveCount) }}</div>
              <div class="text-ps">月活</div>
            </div>
            <div class="item" v-else>
              <div class="text-total">{{ formatNumber(dataStat.userMonthActiveCount) }}</div>
              <div class="text-ps">月活（万）</div>
            </div>
          </div>
        </div>
        <div class="label-left-line right-box">
          <img src="@/assets/images/dashboard/project/<EMAIL>" alt="" class="icon-item" />
          <div class="title-item">项目数</div>
          <div class="item-count">
            <div class="item" v-if="dataStat.projectCreatedCount < 10000">
              <div class="text-total">{{ formatNumber(dataStat.projectCreatedCount) }}</div>
              <div class="text-ps">创建数</div>
            </div>
            <div class="item" v-else>
              <div class="text-total">{{ formatNumber(dataStat.projectCreatedCount) }}</div>
              <div class="text-ps">创建数（万）</div>
            </div>
            <div class="item" v-if="dataStat.projectDayActiveCount < 10000">
              <div class="text-total">{{ formatNumber(dataStat.projectDayActiveCount) }}</div>
              <div class="text-ps">日活</div>
              <div class="value"></div>
            </div>
            <div class="item" v-else>
              <div class="text-total">{{ formatNumber(dataStat.projectDayActiveCount) }}</div>
              <div class="text-ps">日活（万）</div>
              <div class="value"></div>
            </div>
            <div class="item" v-if="dataStat.projectMonthActiveCount < 10000">
              <div class="text-total">{{ formatNumber(dataStat.projectMonthActiveCount) }}</div>
              <div class="text-ps">月活</div>
            </div>
            <div class="item" v-else>
              <div class="text-total">{{ formatNumber(dataStat.projectMonthActiveCount) }}</div>
              <div class="text-ps">月活（万）</div>
            </div>
            <div class="item" v-if="dataStat.projectMonthAddedCount < 10000">
              <div class="text-total">{{ formatNumber(dataStat.projectMonthAddedCount) }}</div>
              <div class="text-ps">本月新增</div>
            </div>
            <div class="item" v-else>
              <div class="text-total">{{ formatNumber(dataStat.projectMonthAddedCount) }}</div>
              <div class="text-ps">本月新增（万）</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getProjectBoardApi } from '@/apis/board';
import LoadingSpin from '@/components/loadingSpin/loadingSpin';

const dataStat = ref({});

const loading = ref(false);

const getProjectBoard = async () => {
  try {
    loading.value = true;
    const res = await getProjectBoardApi();
    if (res.code === 0) {
      dataStat.value = res.data;
    }
  } catch (error) {
    console.error('Error fetching data:');
  } finally {
    loading.value = false;
  }
};

// 数值每三位用逗号隔开，保留2位小数，当数值超过1万时，单位显示为万
const formatNumber = (number) => {
  if (number === null || number === undefined) return '0';

  const isW = number >= 10000;
  const formattedNumber = isW ? (number / 10000).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',') : number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return formattedNumber;
};

onMounted(() => {
  getProjectBoard();
});
</script>

<style lang="less" scoped>
@label-left-width: 88px;
.title-item {
  font-weight: 400;
  font-size: 16px;
  color: #121f2c;
  line-height: 56px;
  padding-right: 32px;
}
.box-title {
  font-weight: 600;
  font-size: 16px;
  color: #00141A;
  margin-bottom: 16px;
}
.dashboard-box {
  display: flex;
}
.left-box {
  width: 40%;
}
.right-box {
  width: 60%;
}
.label-left-line {
  display: flex;
  .icon-item {
    margin-right: 12px;
    width: 56px;
    height: 56px;
  }
  .item-count {
    display: flex;
    // justify-content: space-between;
    width: calc(100% - 200px);
  }
}
.item {
  width: 180px;
  display: flex;
  flex-direction: column;
  .text-total {
    font-weight: 500;
    font-size: 28px;
    color: #121f2c;
    line-height: 28px;
    margin-bottom: 8px;
  }
  .text-ps {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0,20,26,0.45);
    line-height: 18px;
  }
}
</style>
