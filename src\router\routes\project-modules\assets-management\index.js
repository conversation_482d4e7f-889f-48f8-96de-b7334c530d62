// 资产管理
import MODEL_MANAGE from './modelManage'; // 资产管理-模型管理
import MIRROR_MANAGE from './mirrorManage'; // 资产管理-镜像管理
import FILE_MANAGE from './fileManage'; // 资产管理-文件管理
import DATASET from './dataSet'; // 资产管理-数据集管理
import PROMPT_ENGINEERING from './promptEngineering'; // 资产管理-提示词管理

const ASSETS_MANAGEMENT = [
  ...MODEL_MANAGE,
  ...MIRROR_MANAGE,
  ...FILE_MANAGE,
  ...DATASET,
  ...PROMPT_ENGINEERING,
];

export default ASSETS_MANAGEMENT;
