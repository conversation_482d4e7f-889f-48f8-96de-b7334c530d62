<template>
  <div class="empty-box">
    <img :src="emptyImg" alt="" />
    <p v-if="keyword">抱歉没有找到相关{{ title }}，您可以换个关键词试试</p>
    <p v-else>暂无{{ title }}</p>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import emptyImg from '@/assets/images/model-experience/empty-status.png';

const props = defineProps({
  keyword: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.empty-box {
  width: 100%;
  padding: 100px 20px;
  text-align: center;

  img {
    width: 80px;
    height: 80px;
  }
  p {
    margin-top: 8px;
    width: 100%;
    line-height: 22px;
  }
}
</style>