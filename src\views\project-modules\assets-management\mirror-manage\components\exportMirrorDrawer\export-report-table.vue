<template>
  <div class="export-report-table">
    <a-space direction="vertical" :size="16">
      <a-table :columns="columns" :data-source="dataSource" :pagination="false" @change="onTableChange">
        <template #emptyText>
          <light-empty title="暂无导出记录" />
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'imageSha'">
            <a-tooltip v-if="record.imageStatus === 0" placement="top" :title="record.imageSha">
              <span class="ellipsis-text cursor">
                {{ record.imageSha }}
              </span>
            </a-tooltip>
            <a-tooltip v-else placement="top" title="文件已删除">
              <span class="ellipsis-text cursor record-delete">
                {{ record.imageSha }}
              </span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'imageAddress'">
            {{ MODEL_SOURCE_TEXT[record.storeType] }}
          </template>
          <template v-else-if="column.dataIndex === 'imageFilePath'">
            <a-tooltip placement="top" :title="record.imageFilePath">
              <span class="ellipsis-text">
                {{ record.imageFilePath }}
              </span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'exportBy'">
            <a-tooltip placement="top" :title="record.exportBy">
              <span class="ellipsis-text">
                {{ record.exportBy }}
              </span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <jt-tag :color="getExportImageStatus(record.status).tagColor">{{ getExportImageStatus(record.status).message }}</jt-tag>
            <a-tooltip v-if="record.status < 0" placement="top">
              <template #title>
               <div>{{ getExportMsgByBackendStatus(record.status).message }}</div>
              </template>
              <jt-icon type="iconquestion-circle" class="status-icon" />
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </a-space>
  </div>
</template>
<script setup>
import { mirrorManageApi } from '@/apis';
import { onUnmounted } from 'vue';
import LightEmpty from '@/views/management-center/activity-manage/components/light-empty.vue';
import { getExportImageStatus, getExportStatusByBackendStatus, getExportMsgByBackendStatus } from './utils.js';
import { MODEL_SOURCE_TEXT } from '@/views/project-modules/assets-management/model-manage/util.js';

const props = defineProps({
  imageGroupId: {
    type: Number,
    default: undefined,
  },
});
const state = reactive({
  selectedRowKeys: [],
});
const dataSource = ref([]);
let getTableDataTimer = null;
const columns = [
  {
    title: '镜像文件ID',
    dataIndex: 'imageSha',
    ellipsis: true,
    width: '20%',
  },
  {
    title: '版本',
    dataIndex: 'version',
    ellipsis: true,
    width: '7%',
  },
  {
    title: '导出位置',
    dataIndex: 'imageAddress',
    ellipsis: true,
    width: '10%',
  },
  {
    title: '导出路径',
    dataIndex: 'imageFilePath',
    ellipsis: true,
    width: '20%',
  },
  {
    title: '导出状态',
    dataIndex: 'status',
    ellipsis: true,
    filters: [
      {
        text: '成功',
        value: 'success',
      },
      {
        text: '失败',
        value: 'fail',
      },
      {
        text: '进行中',
        value: 'ongoing',
      },
    ],
    onFilter: (value, record) => {
      return value === getExportStatusByBackendStatus(record.status);
    },
    width: '10%',
  },
  {
    title: '导出人',
    dataIndex: 'exportBy',
    ellipsis: true,
  },
  {
    title: '导出时间',
    dataIndex: 'updateTime',
    sorter: (a, b) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
    width: 200,
  },
];

const getMirrorList = () => {
  clearGetTableDataTimer();
  const params = {
    imageGroupId: props.imageGroupId,
    status: [],
    updateTimeDesc: true,
    page: 1,
    pageSize: 100000,
  };
  mirrorManageApi.imageExportRecord(params).then((res) => {
    if (res.code === 0) {
      dataSource.value = res.data?.data;
      getTableDataTimer = setTimeout(() => {
        getMirrorList();
      }, 30000);
    }
  });
};

const onTableChange = (pagination, filters, sorter, extra) => {
};

const clearSelectedFiles = () => {
  state.selectedRowKeys = [];
};
const clearGetTableDataTimer = () => {
  clearTimeout(getTableDataTimer);
  getTableDataTimer = null;
};

onMounted(() => {
  getMirrorList();
});
onUnmounted(() => {
  clearGetTableDataTimer();
});

defineExpose({
  clearSelectedFiles,
  getMirrorList,
});
</script>

<style lang="less" scoped>
.record-delete {
  text-decoration-line: line-through;
  color: @jt-text-color-disabled;
}
.status-icon {
  font-size: 16px;
  vertical-align: sub;
  color: @jt-text-color-primary-opacity05;
}
</style>
