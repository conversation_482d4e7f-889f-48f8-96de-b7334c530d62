<!-- 详情页配额变更记录tab页列表 -->
<template>
  <div>
    <a-configProvider>
      <template #renderEmpty>
        <jt-empty :show-operation="!keyword" title="配额变更记录"> </jt-empty>
      </template>
      <a-table :columns="columns" :data-source="dataList" :loading="loading" :pagination="false" row-key="id" :scroll="{ x: 1300 }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'updateDesc'">
            <span v-if="record.origin === 1">--</span>
            <span v-else class="link-id-name text-ellipsis" @click="() => handleDetailModal(record)">详情</span>
          </template>
          <template v-else-if="column.dataIndex === 'createTime'">
            <div v-if="record.origin === 1" class="time">{{ '--' }}</div>
            <div v-else>
              <div v-if="record.reqUser" class="time">{{ record.createTime ? getFormatTime(record.createTime) : '--' }}</div>
              <div v-else class="time">{{ '--' }}</div>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'updateTime'">
            <div v-if="record.origin === 1" class="time">{{ '--' }}</div>
            <div v-else class="time">{{ record.updateTime ? getFormatTime(record.updateTime) : '--' }}</div>
          </template>
          <template v-else-if="column.dataIndex === 'reqDesc'">
            <div v-if="record.origin === 1">{{ '--' }}</div>
            <a-tooltip v-else placement="top" :title="record.reqDesc">
              <span class="text-ellipsis">{{ record.reqDesc || '--' }}</span>
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </a-configProvider>
    <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
    <DetailModal :quota-log-id="quotaLogId" :visible-handle="visibleHandle" @close="closeHandleModal" />
  </div>
</template>

<script setup>
import { ref, defineProps, watch } from 'vue';
import { useRoute } from 'vue-router';
import DetailModal from './detailModal';
import { getQuotaLogListApi } from '@/apis/quota';
import { getFormatTime } from '@/utils';
import { GB_STORE_ARRAY, TB_STORE_ARRAY, DYNAMIC_DATA_MAP } from '@/constants/quota';
import { bytesToGB, bytesToTB } from '@/utils/bytes';
import { dataToSizeConversion } from '@/utils/storage';

const props = defineProps({
  searchText: { type: String, default: '' },
  baseInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const route = useRoute();
const params = route.params;

const id = computed(() => {
  return params.id;
});

const keyword = ref(props.searchText);
const quotaLogId = ref(null);
const { visibleHandle } = {
  visibleHandle: ref(false),
};

const closeHandleModal = () => {
  visibleHandle.value = false;
};

const getInfos = (key, val) => {
  if (typeof val === 'undefined' || val === null) return '--'; // 当 val 为 undefined 或 null 时返回 '--'
  let result = null;
  if (GB_STORE_ARRAY.includes(key)) {
    result = val > 1024 ? dataToSizeConversion(val).sizeTxt + dataToSizeConversion(val).units : `${val}GB`;
    return result;
  } else if (TB_STORE_ARRAY.includes(key)) {
    result = val > 1024 ? dataToSizeConversion(val).sizeTxt + dataToSizeConversion(val).units : `${val}TB`;
    return result;
  } else {
    result = val;
    return `${result}${DYNAMIC_DATA_MAP[key].unit}`;
  }
};

// 发起人为空 申请时间显示--
const columns = [
  {
    title: '处理时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    fixed: 'left',
    width: 160,
    customRender({ text, record }) {
      if (record.origin === 1) {
        return '--';
      }
      return text ? getFormatTime(text) : '--';
    },
  },
  {
    title: '处理人',
    dataIndex: 'updateUser',
    key: 'updateUser',
    width: 150,
    customRender({ text, record }) {
      if (record.origin === 1) {
        return '--';
      }
      return text ? text : '--';
    },
  },
  {
    title: '加速卡',
    dataIndex: 'acce_card',
    width: 100,
    key: 'acce_card',
    customRender({ text }) {
      return getInfos('acce_card', text);
    },
  },
  {
    title: 'CPU',
    dataIndex: 'cpu',
    key: 'cpu',
    width: 100,
    customRender({ text }) {
      return getInfos('cpu', text);
    },
  },
  {
    title: '镜像',
    dataIndex: 'image',
    key: 'image',
    width: 100,
    customRender({ text }) {
      return getInfos('image', text);
    },
  },
  {
    title: '普通文件存储',
    dataIndex: 'general_stor',
    key: 'general_stor',
    width: 140,
    customRender({ text }) {
      return getInfos('general_stor', text);
    },
  },
  {
    title: '高性能文件存储',
    dataIndex: 'hs_stor',
    key: 'hs_stor',
    width: 140,
    customRender({ text }) {
      return getInfos('hs_stor', text);
    },
  },
  {
    title: '对象存储',
    dataIndex: 'object_stor',
    key: 'object_stor',
    width: 120,
    customRender({ text }) {
      return getInfos('object_stor', text);
    },
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
    customRender({ text, record }) {
      if (record.origin === 1) {
        return '--';
      }
      if (!record.reqUser) {
        return '--';
      }
      return text ? getFormatTime(text) : '--';
    },
  },
  {
    title: '申请人',
    dataIndex: 'reqUser',
    key: 'reqUser',
    width: 90,
    customRender({ text, record }) {
      if (record.origin === 1) {
        return '--';
      }
      return text ? text : '--';
    },
  },
  {
    title: '扩容说明',
    dataIndex: 'reqDesc',
    key: 'reqDesc',
    width: 160,
    customRender({ text, record }) {
      if (record.origin === 1) {
        return '--';
      }
      if (!record.reqUser) {
        return '--';
      }
      return text ? text : '--';
    },
  },
  {
    title: '操作',
    dataIndex: 'updateDesc',
    key: 'updateDesc',
    fixed: 'right',
    width: 100
  }
];
const dataList = ref([]);
const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});
const loading = ref(false);

const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getTableData();
};
const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getTableData();
};

const handleDetailModal = (record) => {
  quotaLogId.value = record.id;
  visibleHandle.value = true;
};

const getTableData = async () => {
  loading.value = true;
  const params = {
    projectId: props.baseInfo.projectId,
    pageNum: paginationOptions.value.pageNum,
    pageSize: paginationOptions.value.pageSize,
  };
  const res = await getQuotaLogListApi(params);

  if (res.code === 0) {
    let dataTransform = res.data.data || [];
    dataTransform = dataTransform.map((item) => {
      return {
        ...item,
        ...item.quota,
      };
    });
    dataList.value = dataTransform;
    paginationOptions.value.total = res.data.total;
  } else {
    dataList.value = [];
    paginationOptions.value.total = 0;
  }
  loading.value = false;
};

watch(
  () => props.searchText,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      keyword.value = newValue;
      getTableData();
    }
  }
);
const getTableList = () => {
  getTableData();
};

defineExpose({
  getTableList,
});
</script>

<style lang="less" scoped>
.text-ellipsis {
  display: inline-block;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.link-id-name {
  cursor: pointer;
  color: @jt-primary-color;
  &:hover {
    color: @jt-primary-color;
  }
}
.time {
  width: 148px;
}
:deep(.ant-table-cell) {
  vertical-align: top;
}
</style>
