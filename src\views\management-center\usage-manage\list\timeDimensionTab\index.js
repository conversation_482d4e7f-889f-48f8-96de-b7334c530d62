import dayjs from 'dayjs';
import { ref } from 'vue';

export const FilterConfig = [
  {
    range: {
      label: '按天',
      value: 'day',
    },
    rangePresetList: [
      {
        label: '近一月',
        value: [dayjs().add(-30, 'day').startOf('day'), dayjs().startOf('day')],
      },
      {
        label: '近三月',
        value: [dayjs().add(-90, 'day').startOf('day'), dayjs().startOf('day')],
      },
      {
        label: '近半年',
        value: [dayjs().add(-180, 'day').startOf('day'), dayjs().startOf('day')],
      },
    ],
    disabledDate: (current) => {
      return current && (current > dayjs() || current < dayjs().add(-180, 'day'));
    },
  },
  {
    range: {
      label: '按周',
      value: 'week',
    },
    rangePresetList: [
      {
        label: '近三月',
        value: [dayjs().add(-90, 'day').startOf('day'), dayjs().startOf('day')],
      },
      {
        label: '近半年',
        value: [dayjs().add(-180, 'day').startOf('day'), dayjs().startOf('day')],
      },
      {
        label: '近一年',
        value: [dayjs().add(-365, 'day').startOf('day'), dayjs().startOf('day')],
      },
    ],
    disabledDate: (current) => {
      return current && (current > dayjs() || current < dayjs().add(-365, 'day'));
    },
  },
  {
    range: {
      label: '按月',
      value: 'month',
    },
    rangePresetList: [
      {
        label: '近半年',
        value: [dayjs().add(-180, 'day').startOf('day'), dayjs().startOf('day')],
      },
      {
        label: '近一年',
        value: [dayjs().add(-365, 'day').startOf('day'), dayjs().startOf('day')],
      },
      {
        label: '全部',
        value: [dayjs('2023-08-01').startOf('day'), dayjs().startOf('day')],
      },
    ],
  },
];

export const accCardUnit = '卡·小时';
const baseColumns = [
  {
    title: '时间段',
    key: 'time',
    dataIndex: 'time',
    fixed: 'left',
    width: 300,
    sorter: true,
  },
  {
    title: '加速卡资源用量（卡·小时）',
    key: 'accCardUsage',
    dataIndex: 'accCardUsage',
    width: 210,
    sorter: true,
  },
];

const cpuColumns = [
  {
    title: 'CPU资源用量（核·小时）',
    key: 'cpuUsage',
    dataIndex: 'cpuUsage',
    width: 210,
    sorter: true,
  },
];
const storageColumns = [
  {
    title: '普通文件存储资源用量（TB·小时）',
    key: 'generalStorageUsage',
    dataIndex: 'generalStorageUsage',
    width: 250,
    sorter: true,
  },
  {
    title: '高性能文件存储资源用量（TB·小时）',
    key: 'hsStorageUsage',
    dataIndex: 'hsStorageUsage',
    width: 270,
    sorter: true,
  },
  {
    title: '对象存储资源用量（TB·小时）',
    key: 'objectStorageUsage',
    dataIndex: 'objectStorageUsage',
    width: 230,
    sorter: true,
  },
  {
    title: '镜像资源用量（TB·小时）',
    key: 'imageUsage',
    dataIndex: 'imageUsage',
    width: 210,
    sorter: true,
  },
];

export const useColumns = (columns, gpuCardType) => {
  columns.value = [
    ...baseColumns,
    ...gpuCardType.map((item) => {
      return {
        ...item,
        title: `${item.title}资源用量（${accCardUnit}）`,
        width: 300,
        sorter: true,
      };
    }),
    ...cpuColumns,
    ...storageColumns,
  ];
};

export const chartColumns = (columns, gpuCardType) => {
  columns.value = [
    ...baseColumns,
    ...gpuCardType.map((item) => {
      return {
        ...item,
        title: `${item.title}资源用量（${accCardUnit}）`,
        width: 300,
        sorter: true,
      };
    }),
    ...cpuColumns,
  ];
};

export const storageChartColumns = (columns) => {
  columns.value = [
    ...storageColumns,
  ];
};

export const usePagination = (getData) => {
  const paginationOptions = reactive({
    total: 0,
    pageNum: 1,
    pageSize: 10,
  });

  watch(
    () => paginationOptions.pageNum,
    () => {
      getData();
    }
  );

  watch(
    () => [paginationOptions.pageSize],
    () => {
      paginationOptions.pageNum = 1;
      getData();
    }
  );

  return [paginationOptions];
};
