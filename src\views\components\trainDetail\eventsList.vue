<template>
  <a-drawer v-model:open="open" :body-style="{ padding: '8px 24px 24px 24px' }" :footer="null" title="实例事件" width="60%" destroy-on-close @close="handleCancel">
    <a-config-provider>
      <a-alert class="options-msg" type="info" show-icon>
        <template #message>
          <p>仅显示最近一周的事件</p>
        </template>
        <template #action>
          <jt-reload-icon-btn @click="getEventsList()" />
        </template>
      </a-alert>
      <template #renderEmpty>
        <light-empty title="目前暂无事件" />
      </template>
      <a-table :loading="loading" :columns="columns" :data-source="eventsData?.list" :pagination="false" @change="tableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'message'">
            <a-tooltip v-if="record.message" placement="topLeft">
              <template #title>{{ record.message }}</template>
              <span>{{ record.message }}</span>
            </a-tooltip>
            <span v-else>--</span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
    <jt-pagination :total="eventsData?.total" :page-num="pageParams.pageNum" :page-size="pageParams.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { TASK_TYPE, taskApiInfo } from '@/constants/trainTask';
import { TABLE_SORT_FIELD } from '@/views/home/<USER>/constant';
import { getDevEvents, getTaskEvents } from '@/apis/modelTrain';
import LightEmpty from '@/views/management-center/activity-manage/components/light-empty.vue';
import { STATUS as INSTANCE_STATUS } from '@/constants/instance.js';
import { requestWithProjectId } from '@/request';

const { GET, POST } = requestWithProjectId;

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: TASK_TYPE.DEV,
  },
  instanceInfo: {
    type: Object,
    default: () => {},
  },
  podPageParams: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['close']);

const open = ref(false);
const pageParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const eventsData = ref();
const loading = ref(false);
const getInfoTimer = ref(null);
const curInstance = ref({}); // 当前实例信息

const columns = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
  },
  {
    title: '事件原因',
    dataIndex: 'reason',
    key: 'reason',
    width: 150,
  },
  {
    title: '事件信息',
    dataIndex: 'message',
    key: 'message',
    ellipsis: true,
  },
  {
    title: '更新时间',
    dataIndex: 'lastTime',
    key: 'lastTime',
    sorter: true,
    width: 200,
  },
];

watch(
  () => props.instanceInfo.visible,
  async (visible) => {
    open.value = visible;
    if (visible) {
      curInstance.value = props.instanceInfo;
      getEventsList();
      createGetInfoTimer();
    } else {
      removeGetInfoTimer();
    }
  }
);

// 根据实例类型轮询实例状态，开发环境直接获取详情，训练任务需要获取实例列表
const getInstancesList = () => {
  if (props.type === TASK_TYPE.DEV) {
    return GET(taskApiInfo(props.type).detail, {
      id: props.id,
    }).then(res => {
      const { code, data } = res;
      if (code === 0) {
        return data?.podInfoList;
      }
    });
  } else {
    return POST(taskApiInfo(props.type).getPodList, {
      id: props.id,
      ...props.podPageParams,
    }).then(res => {
      const { code, data } = res;
      if (code === 0) {
        return data?.list;
      }
    });
  }
}

const createGetInfoTimer = async() => {
  getInfoTimer.value = setInterval(async () => {
    // 针对启动中、停止中状态的实例每10s自动刷新
    if ([INSTANCE_STATUS.onSTART, INSTANCE_STATUS.onSTOP].includes(curInstance.value.status)) {
      const list = await getInstancesList();
      const pod = list?.find(item => item.podName === props.instanceInfo.podName);
      if (pod && pod.podName) {
        curInstance.value = pod;
      }
      getEventsList();
    }
  }, 1000 * 10);
};

const removeGetInfoTimer = () => {
  clearInterval(getInfoTimer.value);
};

const getEventsList = async (order = TABLE_SORT_FIELD['descend']) => {
  loading.value = true;
  const { podName, resourceGroupId } = props?.instanceInfo;
  const req = {
    podName,
    resourceGroupId,
    ...pageParams.value,
    order,
  };
  const res = props?.type === TASK_TYPE.DEV ? await getDevEvents(req) : await getTaskEvents(req);
  loading.value = false;
  if (res.code === 0) {
    eventsData.value = res.data;
  }
};

const changePageSize = (pageSize) => {
  pageParams.value.pageSize = +pageSize;
  pageParams.value.pageNum = 1;
  getEventsList();
};

const changePageNum = (page) => {
  pageParams.value.pageNum = +page;
  getEventsList();
};

const handleCancel = () => {
  emit('close');
};

const tableChange = (pagination, filters, sorter) => {
  getEventsList(TABLE_SORT_FIELD[sorter.order]);
};
</script>
<style lang="less" scoped>
.options-msg {
  padding: 0 0 16px 0;
  background: transparent;
  border: none;
}
</style>
