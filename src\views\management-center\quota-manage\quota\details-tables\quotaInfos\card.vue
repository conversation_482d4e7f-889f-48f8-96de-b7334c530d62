<template>
  <div class="card">
    <div class="card-head">{{ title }}</div>
    <div class="card-body">
      <div>
        <span class="card-num">{{ usedText == null ? '--' : usedText }}</span>
        <span class="card-unit">{{ unit }}</span>
        <span class="card-value"> / {{ totalText == null ? '--' : totalText }}</span>
        <span class="card-unit">{{ unitText }}</span>
      </div>
      <div class="card-text">{{ stateText }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  title: {
    type: String,
    default: '',
  },
  usedText: {
    type: Number,
    default: null,
  },
  totalText: {
    type: Number,
    default: null,
  },
  unit: {
    type: String,
    default: '',
  },
  unitText: {
    type: String,
    default: '',
  },
  stateText: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.card {
  margin: 10px 0 12px;
  .card-head {
    font-weight: 400;
    font-size: 14px;
    color: #00141A;
    line-height: 22px;
  }
  .card-body {
    margin-top: 8px;
    .card-num {
      font-size: 28px;
      color: #00141A;
    }
    .card-value {
      font-size: 16px;
      color: #00141A;
    }
    .card-unit {
      font-size: 14px;
      color: #00141A;
    }
    .card-text {
      margin-top: 4px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,20,26,0.45);
      line-height: 18px;
    }
  }
}
</style>
