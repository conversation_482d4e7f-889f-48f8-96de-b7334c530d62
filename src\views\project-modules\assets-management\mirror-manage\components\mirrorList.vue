<template>
  <div>
    <a-configProvider>
      <template #renderEmpty>
        <jt-empty :title="'镜像'" :show-operation="emptyShowOperation">
          <template #description> 立即<a-button type="link" ghost @click="uploadMirror">导入镜像</a-button> </template>
        </jt-empty>
      </template>
      <a-table :scroll="{ x: 1160 }" :columns="typeAColumns" :data-source="typeADataShow" :pagination="false" :loading="tableALoading" row-key="id" @change="tableAChange">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'imageId'">
            <div>
              <a-tooltip placement="topLeft">
                <template #title>
                  <span>{{ text }}</span>
                </template>

                <div class="location-span text-box" style="color: #00141a">{{ text }}</div>
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'description'">
            <div>
              <a-tooltip placement="topLeft">
                <template #title>
                  <span>{{ text }}</span>
                </template>

                <div class="location-span text-box">{{ text }}</div>
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'tags'">
            <div class="label-box">
              <a-tooltip v-for="item in text" :key="item" placement="top">
                <template #title>
                  <span>{{ item }}</span>
                </template>
                <div class="label-item">{{ item }}</div>
              </a-tooltip>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'version'">
            <span>{{ `${text}` }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'totalSize'">
            <span>{{ getSize(text) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'updateTime'">
            <span>{{ getTime(text) }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'address'">
            <div class="address-box">
              <div class="icon-copy" @click="copyContent(text)">
                <a-tooltip placement="top">
                  <template #title>
                    <span>复制</span>
                  </template>
                  <jt-icon type="iconfile-copy"></jt-icon>
                </a-tooltip>
              </div>
              <div class="text-box">
                <a-tooltip placement="topLeft">
                  <template #title>
                    <span>{{ text }}</span>
                  </template>
                  <div class="location-span">{{ text }}</div>
                </a-tooltip>
              </div>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <span class="operation">
              <span class="operation-item" @click="editMirror(record)">
                <!-- <jt-icon type="iconbianji" class="operation-icon"></jt-icon> -->
                <span class="operation-text">编辑</span>
              </span>
              <span class="operation-item" @click="deleteMirror(record)">
                <!-- <jt-icon type="iconshanchu1" class="operation-icon"></jt-icon> -->
                <span class="operation-text">删除</span>
              </span>
            </span>
          </template>
        </template>
      </a-table>
    </a-configProvider>

    <jt-pagination v-if="typeACount > 0" :page-num="typeACurrentPage" :page-size="typeAPageSize" :total="typeACount" @changePageNum="onTypeAPageChange" @changePageSize="onTypeAShowSizeChange"></jt-pagination>

    <div v-if="editVisible">
      <edit-mirror :edit-visible="editVisible" :edit-mirror-info="editMirrorInfo" @childFn="closeModal"> </edit-mirror>
    </div>

    <div v-if="deleteVisible">
      <delete-mirror-modal :delete-visible="deleteVisible" :delete-mirror-info="deleteMirrorInfo" @childFn="closeModal"> </delete-mirror-modal>
    </div>
  </div>
</template>

<script>
import deleteMirrorModal from './deleteMirror.vue';
import editMirror from './editMirror.vue';
import { handleCopy, getFormatTime } from '@/utils/index';
import { dataToSizeConversion } from '@/utils/storage';
import { mirrorManageApi } from '@/apis';
import { IMPORT_IMAGE_STATUS } from '@/constants/image';


export default {
  components: {
    editMirror,
    deleteMirrorModal,
  },
  props: {
    searchText: {
      type: String,
      default: '',
    },
  },
  emits: ['createNew', 'getCount'],
  data() {
    return {
      mirrorId: this.$route.params.id,
      emptyShowOperation: true,
      typeAColumns: [
        {
          title: '镜像文件ID',
          dataIndex: 'imageId',
          key: 'imageId',
          width: 270,
          fixed: 'left',
        },
        {
          title: '版本',
          dataIndex: 'version',
          key: 'version',
          width: 80,
        },
        {
          title: '镜像标签',
          dataIndex: 'tags',
          key: 'tags',
          width: 190,
        },
        {
          title: '大小',
          dataIndex: 'totalSize',
          key: 'totalSize',
          width: 100,
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime',
          key: 'updateTime',
          sorter: true,
          width: 160,
        },
        {
          title: '上传人',
          dataIndex: 'createBy',
          key: 'createBy',
          width: 120,
        },
        {
          title: '描述',
          dataIndex: 'description',
          key: 'description',
          width: 100,
        },
        {
          title: '地址',
          dataIndex: 'address',
          key: 'address',
          width: 150,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          width: 150,
          fixed: 'right',
        },
      ],
      typeADataShow: [],
      typeAPageSize: 5,
      typeACount: 0,
      typeACurrentPage: 1,
      tableALoading: false,
      updateTimeDesc: true,

      editVisible: false,
      editMirrorInfo: {},

      deleteVisible: false,
      deleteMirrorInfo: {},
    };
  },
  watch: {
    searchText(val) {
      this.emptyShowOperation = val ? false : true;
      this.typeACurrentPage = 1;
      this.getTableInfo();
    },
  },
  created() {
    // 发起数据初始化的请求；
    this.getTableInfo();
  },
  methods: {
    getTime(value) {
      return getFormatTime(value);
    },
    getTableInfo() {
      const sendData = {
        imageGroupId: this.mirrorId,
        status: [IMPORT_IMAGE_STATUS.COMPLETED.code],
        searchField: this.searchText,
        page: this.typeACurrentPage,
        pageSize: this.typeAPageSize,
        updateTimeDesc: this.updateTimeDesc,
      };
      this.tableALoading = true;
      mirrorManageApi.getMirrorList(sendData).then((res) => {
        this.tableALoading = false;
        if (res.code === 0) {
          this.typeADataShow = res.data.data;
          this.typeACount = res.data.total;
        }
      });
    },
    getSize(value) {
      if (value === undefined) return '--';
      const { sizeTxt, units } = dataToSizeConversion(value, 2);
      return `${sizeTxt}${units}`;
    },
    uploadMirror() {
      this.$emit('createNew');
    },
    editMirror(item) {
      this.editVisible = true;
      this.editMirrorInfo = item;
    },
    closeModal(data) {
      this.editVisible = false;
      this.editMirrorInfo = {};
      this.deleteVisible = false;
      this.deleteMirrorInfo = {};
      if (data) {
        this.getTableInfo();
        this.$emit('getCount');
      }
    },
    deleteMirror(record) {
      this.deleteVisible = true;
      this.deleteMirrorInfo = record;
    },
    copyContent(value) {
      handleCopy(value);
    },
    onTypeAShowSizeChange(pageSize) {
      this.typeAPageSize = pageSize;
      this.typeACurrentPage = 1;
      this.getTableInfo();
    },
    // 跳至xx页
    onTypeAPageChange(pageIndex) {
      this.typeACurrentPage = pageIndex;
      this.getTableInfo();
    },

    // table排序
    tableAChange(pagination, filters, sorter) {
      if (sorter.order) {
        this.updateTimeDesc = sorter.order === 'ascend' ? false : true;
      } else {
        this.updateTimeDesc = true;
      }
      this.typeACurrentPage = 1;
      this.getTableInfo();
    },
  },
};
</script>

<style lang="less" scoped>
.text-box {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal !important;
  word-wrap: break-word;
}
:deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
}

:deep(.ant-table-tbody > tr) {
  height: 88px;
}

.label-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .label-item {
    margin-bottom: 4px;
    height: 20px;
    max-width: 149px;
    padding: 0 12px;
    background: #eaf2ff;
    border-radius: 2px;
    border: 1px solid #99beff;
    line-height: 20px;
    font-size: 12px;
    font-weight: 400;
    color: #337dff;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.status-box {
  display: flex;
  align-items: center;

  .status-icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 4px;
  }

  .used {
    background-color: lightgreen;
  }

  .no-used {
    border: 2px solid lightgreen;
  }
}

.address-box {
  display: flex;
  align-items: center;

  .icon-copy {
    // color: #0082ff;
    cursor: pointer;
    margin-right: 6px;
  }

  .text-box {
    flex: 1;
  }
}

.operation {
  display: flex;
  align-items: center;

  .operation-item {
    cursor: pointer;
    margin-right: 24px;
    display: flex;
    align-items: center;

    .operation-icon {
      color: @jt-primary-color;
      margin-right: 4px;
    }

    .disabled-gray {
      cursor: not-allowed;
      color: #a0a6ab;
    }
  }
}
:deep(.ant-table-placeholder .ant-table-cell) {
  border-bottom: 0 !important;
}
.operation-text {
  color: @jt-primary-color;
}
</style>
