<template>
  <a-image :src="imageSrc" :style="$attrs.style"></a-image>
</template>
<script setup>
import { defineProps, ref, computed, onMounted } from 'vue';
import { modelEvaluationApi } from '@/apis';

const base64 = ref(undefined);
const props = defineProps(['src']);
const imageSrc = computed(() => {
  if (base64.value) {
    return base64.value;
  }

  return undefined;
});

watch(
  () => props.src,
  (value) => {
    console.log('src nnnn: ', value);
    if (value) {
      modelEvaluationApi
        .getResImage({
          imageName: value,
        })
        .then((res) => {
          base64.value = `data:image/png;base64,${res.data}`
          console.log(res);

        });
    }
  }
);

onMounted(() => {
  if (props.src) {
    modelEvaluationApi
      .getResImage({
        imageName: props.src,
      })
      .then((res) => {
        base64.value = `data:image/png;base64,${res.data}`
      });
  }
});
</script>

<style lang="scss" scoped>
</style>