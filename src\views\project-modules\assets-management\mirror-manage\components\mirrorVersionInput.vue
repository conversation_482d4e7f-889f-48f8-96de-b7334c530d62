<template>
  <div>
    <span v-for="tag in tags" :key="tag">
      <a-tag :closable="closable" class="changed-tag" @close="() => handleClose(tag)">
        {{ tag }}
      </a-tag>
    </span>
    <a-input
      v-if="inputVisible"
      ref="input"
      autocomplete="off"
      type="text"
      size="small"
      :class="{
        error: versionError,
      }"
      :style="{ width: '78px', 'line-height': '32px' }"
      :value="inputValue"
      @change="handleInputChange"
      @blur="handleInputConfirm"
      @keyup.enter="handleInputConfirm"
    />
    <a-tag v-if="!inputVisible && tags.length < 3" class="add-field" @click="showInput">
      <jt-icon type="icontianjia" class="add-icon"></jt-icon>
      <span class="add-text">添加标签</span>
    </a-tag>
  </div>
</template>

<script>
import { imageTagRegex } from '@/constants/regex';

export default {
  props: {
    existTags: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['getMirrorVersion', 'versionError'],
  data() {
    return {
      inputVisible: false,
      inputValue: '',
      closable: true,
      tags: [],
      versionError: false,
    };
  },
  watch: {
    existTags: {
      handler(val) {
        this.tags = val;
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
  created() {
    this.tags = this.existTags;
  },
  methods: {
    handleClose(removedTag) {
      const tags = this.tags.filter((tag) => tag !== removedTag);
      this.$nextTick(() => {
        Object.assign(this, {
          tags,
          inputVisible: false,
          inputValue: '',
        });
        this.$emit('getMirrorVersion', this.tags);
      });
      // this.$emit('getMirrorVersion', this.tags);
    },
    showInput() {
      this.inputVisible = true;
      this.versionError = false;
      this.$emit('versionError', false);
      this.$nextTick(function () {
        this.$refs.input.focus();
      });
    },
    handleInputChange(e) {
      this.inputValue = e.target.value;
      if (!this.inputValue) {
        this.versionError = false;
        this.$emit('versionError', false);
        return;
      }
      if (!imageTagRegex.test(this.inputValue)) {
      //if (!/^[a-zA-Z0-9-_.]*$/.test(this.inputValue) || e.target.value.length > 12 || this.hasConsecutiveSpecialChars(e.target.value)) {
        this.$nextTick(() => {
          this.versionError = true;
        });
        this.$emit('versionError', true);
      } else {
        this.$nextTick(() => {
          this.versionError = false;
        });
        this.$emit('versionError', false);
      }
    },
    // hasConsecutiveSpecialChars(str) {
    //   for (var i = 0; i < str.length - 1; i++) {
    //     if (this.isSpecialChar(str.charAt(i)) && this.isSpecialChar(str.charAt(i + 1))) {
    //       return true;
    //     }
    //   }
    //   if (str.charAt(0) === '.' || str.charAt(str.length - 1) === '.') return true;
    //   return false;
    // },
    // isSpecialChar(char) {
    //   let specialCharRegex = /[.]/;
    //   return specialCharRegex.test(char);
    // },
    handleInputConfirm() {
      if (this.versionError) {
        return;
      }
      const inputValue = this.inputValue;
      let tags = this.tags;
      if (inputValue && tags.indexOf(inputValue) === -1) {
        tags = [...tags, inputValue];
      }
      Object.assign(this, {
        tags,
        inputVisible: false,
        inputValue: '',
      });
      this.$emit('getMirrorVersion', this.tags);
    },
  },
};
</script>

<style lang="less" scoped>
.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(240, 65, 52, 0.2);
}

:deep(.ant-tag-blue) {
  border: 1px solid #99beff;
  text-align: center;
  border-radius: 2px;
  background: #eaf2ff;
  color: #337dff;
  padding: 2px 4px;
  font-size: 14px;
}
.add-text {
  font-size: 14px;
}
.add-field {
  background: #fff;
  cursor: pointer;
  border-style: dashed;
  line-height: 32px;
}
.changed-tag {
  font-size: 14px;
  line-height: 32px;
  background: #e6f7fa;
  border-radius: 2px;
  border: 1px solid #5ac8e0;
  color: @jt-primary-color;
}
:deep(.ant-tag .ant-tag-close-icon) {
  color: @jt-primary-color;
  margin-left: 10px;
}
</style>
