<template>
  <div class="chart-wrap">
    <a-flex justify="space-between" style="margin-bottom: 10px">
      <div>服务调用TOP10属性分布</div>
      <div>
        <a-radio-group v-model:value="selectedType" @change="handleRadioChange">
          <a-radio-button value="ResourceGroup">资源组</a-radio-button>
          <a-radio-button value="Project">项目</a-radio-button>
          <a-radio-button value="User">用户</a-radio-button>
        </a-radio-group>
      </div>
    </a-flex>
    <empty-data v-if="!chartsData.datas?.count" title="请选择时间点" class="empty-content"></empty-data>
    <v-chart v-else :option="chartOption" autoresize style="height: 400px; width: 100%" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
import { getFormatTime } from '@/utils';
import VChart from 'vue-echarts';
import emptyData from '@/components/emptyData';

use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);

const props = defineProps({
  dataSource: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  timeParamsRootRef: {
    type: [Number],
    default: 0,
  },
});

const colorMap = ['rgba(91, 143, 249, 0.85)', 'rgba(90, 216, 166, 0.85)', 'rgba(246, 189, 22, 0.85)', 'rgba(232, 104, 74, 0.85)', 'rgba(109, 200, 236, 0.85)', 'rgba(146, 112, 202, 0.85)', 'rgba(255, 157, 77, 0.85)', 'rgba(73, 183, 182, 0.85)', 'rgba(255, 153, 195, 0.85)', 'rgba(128, 170, 255, 0.85)', 'rgba(189, 210, 253, 0.85)', 'rgba(199, 225, 102, 0.85)', 'rgba(189, 239, 219, 0.85)', 'rgba(237, 116, 173, 0.85)', 'rgba(184, 196, 224, 0.85)', 'rgba(255, 185, 78, 0.85)', 'rgba(251, 229, 162, 0.85)', 'rgba(255, 134, 106, 0.83)', 'rgba(246, 195, 183, 0.85)', 'rgba(140, 220, 251, 0.85)', 'rgba(182, 227, 245, 0.85)', 'rgba(119, 114, 241, 0.85)'];

const chartsData = reactive({
  xAxis: [],
  datas: {
    dev: [],
    train: [],
    reasoning: [],
  },
});

const selectedType = ref('ResourceGroup');

const handleRadioChange = (e) => {
  const value = e.target.value;
  selectedType.value = value;
  chartOption.value = getChartOption();
};

const getXAix = () => {
  return props.dataSource
    .find((item) => item.type === selectedType.value)
    ?.infos.map((item) => {
      return item.name;
    });
};

const getY = () => {
  return props.dataSource
    .find((item) => item.type === selectedType.value)
    ?.infos.map((item) => {
      return item.count;
    });
};

const getChartOption = () => {
  chartsData.xAxis = getXAix();
  chartsData.datas.count = getY();
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params, ticket) => {
        let rowString = ``;
        let bottomName = ``;
        params.forEach((x, i) => {
          rowString += `<div class="row">
                  <p>
    
                    <span class="dot-common" style="background-color:${colorMap[i]}"></span>
                    <span style="margin-right:6px">服务调用量</span>
                    <span style="display:inline-block;min-width:50px">${x.data}</span>

                  </p>
                </div>
                `;
          bottomName += `
                <div>
                  <p>
                    <span style="display:inline-block;min-width:50px">${x.name}</span>
                  </p>
                </div>`;
        });
        return `
          <div class="tooltip-wrap">
            <div class="tooltip-content">
              ${rowString}
            </div>
            <div class="tooltip-footer">${bottomName}${getFormatTime(props.timeParamsRootRef, 'YYYY-MM-DD HH:mm')}</div>
          </div>
          `;
      },
    },
    legend: {
      data: ['开发环境'],
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: chartsData.xAxis,
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function (value) {
          if (value >= 100000) {
            return (value / 100000).toFixed(2) + '万';
          }
          return value;
        }
      },
    },
    series: [
      {
        name: '属性分布',
        type: 'bar',
        barWidth: 20,
        data: chartsData.datas.count,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#23C2B8' }, // 起始颜色
              { offset: 1, color: 'rgba(35,194,184,0.4)' }, // 结束颜色56,192,85
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  };
};

const chartOption = ref(getChartOption());

watch(
  () => props.dataSource,
  () => {
    chartOption.value = getChartOption();
  }
);
</script>

<style lang="less" scoped>
.chart-wrap {
  position: relative;
}
.chart-name {
  position: absolute;
}
:deep(.tooltip-wrap) {
  min-width: 200px;
  .tooltip-content {
    margin-bottom: 12px;
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .row {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    margin-bottom: 8px;
    p {
      margin-right: 60px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #121f2c;
      line-height: 20px;
    }
    span {
      font-size: 12px;
      font-weight: 400;
      color: #606972;
      line-height: 18px;
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
</style>
