<template>
  <div class="activity-detail">
    <jt-sub-header :bread-crumb="breadCrumb"></jt-sub-header>
    <BaseInfo />
    <div class="table-detail">
      <TableList />
    </div>
  </div>
</template>

<script setup>
import BaseInfo from './baseInfo.vue';
import TableList from './tableList.vue';

import { useRoute } from 'vue-router';
const route = useRoute();

const breadCrumb = route.meta.header;
</script>

<style lang="less" scoped>
.activity-detail {
  .table-detail {
    margin: 20px 20px 64px;
    background-color: #fff;
    box-shadow: 0px 2px 4px 0px rgba(0,20,26,0.04);
    padding: 20px 20px 48px;
  }
}
</style>
