<template>
  <div class="step-box">
    <a-form ref="formRef" class="form-base" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol" :colon="false">
      <a-form-item ref="name" class="form-line-first" label="资源组名称" name="name" :help="helpText">
        <a-input v-model:value="formState.name" class="space-info" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="资源组类型" required name="resourceGroupAttribute">
        <a-radio-group v-model:value="formState.resourceGroupAttribute" @change="handleChangeResAttr">
          <a-radio value="public">公共资源组</a-radio>
          <a-radio value="private">专属资源组</a-radio>
        </a-radio-group>
        <span class="warn-tips">创建后不可修改</span>
      </a-form-item>

      <a-form-item v-if="checkResourceAuth(['datasetAnalyse', 'datasetClean', 'datasetEnhance', 'trainDev', 'trainTask', 'inferService']) || isCheckAuth" label="适用场景" required name="usageScenario">
        <a-checkbox-group v-model:value="formState.usageScenario">
          <a-checkbox v-if="checkResourceAuth(['datasetAnalyse', 'datasetClean', 'datasetEnhance'])" :value="SCENARIO_TYPE_MAP.DATA"
            ><jt-tag rounded="small" :color="SCENARIO_TYPE_TAG_COLOR[SCENARIO_TYPE_MAP.DATA]">{{ SCENARIO_TYPE[SCENARIO_TYPE_MAP.DATA] }}</jt-tag></a-checkbox
          >
          <a-checkbox v-if="checkResourceAuth(['trainDev', 'trainTask'])" :value="SCENARIO_TYPE_MAP.TRAIN"
            ><jt-tag rounded="small" :color="SCENARIO_TYPE_TAG_COLOR[SCENARIO_TYPE_MAP.TRAIN]">{{ SCENARIO_TYPE[SCENARIO_TYPE_MAP.TRAIN] }}</jt-tag></a-checkbox
          >
          <a-checkbox v-if="isCheckAuth" :value="SCENARIO_TYPE_MAP.OPTIMIZE"
            ><jt-tag rounded="small" :color="SCENARIO_TYPE_TAG_COLOR[SCENARIO_TYPE_MAP.OPTIMIZE]">{{ SCENARIO_TYPE[SCENARIO_TYPE_MAP.OPTIMIZE] }}</jt-tag></a-checkbox
          >
          <a-checkbox v-if="checkResourceAuth('inferService')" :value="SCENARIO_TYPE_MAP.INFERENCE"
            ><jt-tag rounded="small" :color="SCENARIO_TYPE_TAG_COLOR[SCENARIO_TYPE_MAP.INFERENCE]">{{ SCENARIO_TYPE[SCENARIO_TYPE_MAP.INFERENCE] }}</jt-tag></a-checkbox
          >
        </a-checkbox-group>
        <span class="warn-tips">创建后不可取消已选场景</span>
      </a-form-item>

      <a-form-item label="资源类型" required name="resourceType">
        <a-radio-group v-model:value="formState.resourceType">
          <a-radio v-if="resourceGroupType?.gpuCard === 1" value="gpucard">加速卡</a-radio>
          <a-radio v-if="resourceGroupType?.cpu === 1" value="cpu">CPU</a-radio>
        </a-radio-group>
        <span class="warn-tips">创建后不可修改</span>
      </a-form-item>

      <a-form-item v-if="formState.resourceType === 'gpucard'" label="加速卡类型" required name="acceleratorType">
        <a-select v-model:value="formState.acceleratorType" placeholder="请选择加速卡类型" style="width: 232px; margin-right: 16px">
          <template v-for="(item, index) in gpuCartTypeList" :key="index">
            <a-select-option :value="item.id">{{ item.gpuCardTypeName }}</a-select-option>
          </template>
        </a-select>
        <span class="warn-tips">创建后不可修改</span>
      </a-form-item>

      <a-form-item label="所属集群" required name="cluster">
        <a-select v-model:value="formState.cluster" placeholder="请选择集群" style="width: 232px; margin-right: 16px">
          <template v-for="(item, index) in clusterList" :key="index">
            <a-select-option :value="item.id">{{ item.clusterName }}</a-select-option>
          </template>
        </a-select>
        <span class="warn-tips">创建后不可修改</span>
      </a-form-item>

      <a-form-item label="资源组描述" name="description">
        <JtTextarea v-model:value="formState.description" :rows="4" class="textarea space-info" show-count placeholder="请输入" :maxlength="80" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import isEmpty from 'lodash/isEmpty';
import JtTextarea from '@/components/JtTextarea.vue';
import { getGpuCardTypeApi, getClusterApi, getGroupNameExistApi, getPoolReourceGroupTypeApi } from '@/apis/resourceGroups';
import { SCENARIO_TYPE_MAP, SCENARIO_TYPE, SCENARIO_TYPE_TAG_COLOR } from '@/constants/nodeList';
import { checkResourceAuth } from '@/utils/auth';
// 模型调优-增量预训练/有监督微调/偏好对齐/模型压缩/模型评估
const isCheckAuth = checkResourceAuth(['postPretrain', 'supervisedFinetune', 'preferAlign', 'modelCompress', 'modelEvaluation']);
const gpuCartTypeList = ref([]);
const clusterList = ref([]);
const emit = defineEmits(['onDisabledNext']);
const resourceGroupType = ref({});

const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
  reSetRelated: {
    type: Function,
    default: () => {},
  },
});

const fetchResourceGroupType = async () => {
  try {
    const res = await getPoolReourceGroupTypeApi();
    if (res.code === 0) {
      resourceGroupType.value = res.data;
      // 更新默认值
      if (res.data?.gpuCard === 0 && res.data?.cpu === 0) {
        formState.resourceType = '';
      } else if (res.data?.gpuCard === 0 && res.data?.cpu === 1) {
        formState.resourceType = 'cpu';
      } else if (res.data?.gpuCard === 1 && res.data?.cpu === 0) {
        formState.resourceType = 'gpucard';
      };
    };
  } catch (error) {
    throw new Error(error);
  };
};

const fetchExist = async (groupName) => {
  try {
    const params = { groupName };
    const res = await getGroupNameExistApi(params);
    if (res.code === 0) {
      return res.data;
    }
  } catch (error) {
    console.log('error');
  }
};

const fetchGpuCardTypeList = async () => {
  emit('onDisabledNext', true);
  try {
    const res = await getGpuCardTypeApi();
    if (res.code === 0) {
      gpuCartTypeList.value = res.data;
    }
  } catch (error) {}
  emit('onDisabledNext', false);
};

const fetchClusterList = async () => {
  emit('onDisabledNext', true);
  try {
    const res = await getClusterApi();
    if (res.code === 0) {
      clusterList.value = res.data;
    }
  } catch (error) {}
  emit('onDisabledNext', false);
};

onMounted(async () => {
  await fetchGpuCardTypeList();
  await fetchClusterList();
  if (!isEmpty(props.defaultData)) {
    formState.name = props.defaultData.name;
    formState.resourceGroupAttribute = props.defaultData.resourceGroupAttribute;
    formState.usageScenario = props.defaultData.usageScenario;
    formState.resourceType = props.defaultData.resourceType;
    formState.acceleratorType = props.defaultData.acceleratorType;
    formState.cluster = props.defaultData.cluster;
    formState.description = props.defaultData.description;
  }
  await fetchResourceGroupType();
});

const labelCol = {
  span: 4,
};
const wrapperCol = {
  span: 20,
};

const formState = reactive({
  name: '',
  resourceGroupAttribute: 'public',
  usageScenario: [SCENARIO_TYPE_MAP.TRAIN],
  resourceType: 'gpucard',
  acceleratorType: null,
  cluster: null,
  description: '',
});

const rules = {
  name: [
    { required: true, message: '请输入资源组名称', trigger: 'blur' },
    { max: 30, message: '资源组名称最多30个字符', trigger: 'blur' },
    {
      message: '已有同名资源组',
      asyncValidator: async (rule, value) => {
        if (!value) {
          helpText.value = `请输入30个字符以内`;
          throw new Error('请输入资源组名称');
        }

        if (value) {
          if (value.replace(/\s/g, '').length === 0) {
            helpText.value = `请输入30个字符以内`;
            throw new Error('请输入资源组名称');
          }
          if (value.length > 30) {
            helpText.value = `请输入30个字符以内`;
            throw new Error('资源组名称最多30个字符');
          }
          const isDuplicate = await fetchExist(value);
          if (isDuplicate) {
            helpText.value = `已有同名资源组`;
            throw new Error('已有同名资源组');
          } else {
            helpText.value = ``;
          }
        }
      },
      trigger: 'blur',
    },
  ],
  resourceGroupAttribute: [{ required: true, message: '请选择资源组属性', trigger: 'change' }],
  usageScenario: [{ required: true, message: '请选择适用场景', trigger: 'change' }],
  resourceType: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
  acceleratorType: [{ required: true, message: '请选择加速卡类型', trigger: 'change' }],
  cluster: [{ required: true, message: '请选择所属集群', trigger: 'change' }],
  description: [{ max: 80, message: '资源组描述最多80个字符', trigger: 'blur' }],
};

const formRef = ref(null);

const helpText = ref('请输入30个字符以内');

const handleValide = async () => {
  return formRef.value.validate();
};

const integrateData = () => {
  return { ...formState, gpuCartTypeList, clusterList };
};

defineExpose({
  validate: handleValide,
  getData: integrateData,
});

const handleChangeResAttr = (event) => {
  console.log(event.target.value);
  if (event.target.value === 'private') {
    props.reSetRelated(true);
  } else {
    props.reSetRelated(false);
  }
};
</script>
<style lang="less" scoped>
.step-box {
  margin-bottom: 60px;
}
.form-base {
  padding-left: 230px;
  .ant-form-item {
    margin-bottom: 32px;
  }
  .form-line-first {
    margin-bottom: 24px;
  }
}

.warn-tips {
  font-weight: 400;
  font-size: 14px;
  color: #f9881b;
  padding: 0;
}
.space-info {
  width: 480px;
}
:deep(.count-area) {
  color: rgb(0, 20, 26, 0.45);
}
</style>
