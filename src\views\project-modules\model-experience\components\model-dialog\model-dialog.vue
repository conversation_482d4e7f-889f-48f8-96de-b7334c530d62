<template>
  <div class="model-dialog">
    <text-dialog v-if="modelType == 0" />
    <pic-to-text-dialog v-if="modelType == 1" />
    <text-to-pic-dialog v-if="modelType == 2" />
  </div>
</template>

<script setup>
import { useStore } from 'vuex';
import TextDialog from './text-dialog.vue';
import PicToTextDialog from './pic-to-text-dialog.vue';
import TextToPicDialog from './text-to-pic-dialog.vue';

const store = useStore();
const modelType = computed(() => store.state.modelExperience.modelType);
</script>

<style lang="less" scoped>
.model-dialog {
  height: 100%;
}
</style>