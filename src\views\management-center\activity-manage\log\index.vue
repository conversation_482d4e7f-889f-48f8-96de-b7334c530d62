<template>
  <div class="manage-service-log">
    <!-- 引入用户侧的组件，展示效果与用户侧完全相同，不同点在权限 -->
    <service-log :breadCrumb="breadCrumb" :socket-url="socketUrl" :podNameStr="podNameStr" :resGroupId="resGroupId" :projectId="projectId" :id="id" :userId="userId" />
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import ServiceLog from '@/views/project-modules/model-reason/service-manage/detail/logs/serviceLog.vue';

const socketUrl = '/web/admin/serving/v1/log';
const route = useRoute();
const { projectId, id } = route.params;
const { podNameStr, resGroupId, userId } = route.query;

const breadCrumb = [
  {
    name: '运管中心',
  },
  {
    name: `项目活动管理`,
    path: '/activity-manage',
  },
  {
    name: `推理服务详情`,
    path: `/activity-manage/service/detail/${projectId}/${id}`,
  },
  {
    name: `实例日志`,
  },
];
</script>

<style lang="less" scoped></style>
