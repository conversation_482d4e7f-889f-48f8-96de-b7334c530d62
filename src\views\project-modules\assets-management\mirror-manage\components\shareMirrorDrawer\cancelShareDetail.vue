<template>
  <div class="cancel-share-detail">
    <a-space direction="vertical" :size="16">
      <a-alert style="padding-left: 16px" message="移除分享后，相关分享记录将被删除，被分享者将失去相关镜像文件读写权限，该操作不可撤回，请谨慎操作。" type="warning" show-icon />
      <a-table row-key="id" :pagination="paginationOptions" :selections="onSelectChange" :row-selection="{ type: 'radio', selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }" :columns="columns" :data-source="dataSource" :loading="tableLoading" @change="onTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'nameSpace'">
            <a-tooltip placement="top" :title="record.nameSpace">
              <span class="ellipsis-text cursor">
                {{ record.nameSpace }}
              </span>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'sharedProjectId'">
            <a-tooltip placement="top" :title="record.sharedProjectId">
              <span class="ellipsis-text cursor">
                {{ record.sharedProjectId }}
              </span>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'imageSha'">
            <a-tooltip placement="top" :title="record.imageStatus === IMAGE_STATUS.DELETED ? '文件已删除' : record.imageSha">
              <span class="ellipsis-text cursor">
                <s v-if="record.imageStatus === IMAGE_STATUS.DELETED">{{ record.imageSha }}</s>
                <template v-else>{{ record.imageSha }}</template>
              </span>
            </a-tooltip>
          </template>
        </template>
        <template #emptyText>
          <light-empty title="暂无镜像文件" />
        </template>
      </a-table>
  </a-space>
  </div>
</template>
<script setup>
import LightEmpty from '@/views/management-center/activity-manage/components/light-empty.vue';
import { IMAGE_STATUS } from '@/constants/image';
import { mirrorManageApi } from '@/apis';

const props = defineProps({
  mirrorGroupInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['change']);
const state = reactive({
  selectedRowKeys: [],
});
const dataSource = ref([]);
const tableLoading = ref(false);
const paginationOptions = ref({
  showSizeChanger: false, // 是否可以改变每页条数
  showQuickJumper: false, // 是否可以快速跳转至某页
  pageSize: 10, // 每页条数
  hideOnSinglePage: true, // 只有一页时是否隐藏分页器
  total: 0, // 初始化数据总数
  current: 1, // 当前页数
  simple: true,
  position: ['bottomRight'],
});
const columns = [
  {
    title: '项目空间名称',
    dataIndex: 'nameSpace',
    ellipsis: true,
  },
  {
    title: '项目空间ID',
    dataIndex: 'sharedProjectId',
    ellipsis: true,
  },
  {
    title: '镜像文件ID',
    dataIndex: 'imageSha',
    ellipsis: true,
  },
  {
    title: '版本',
    dataIndex: 'version',
  },
  {
    title: '分享人',
    dataIndex: 'shareBy',
    ellipsis: true,
  },
  {
    title: '分享时间',
    dataIndex: 'shareTime',
    sorter: (a, b) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
    width: 200,
  },
];

const onSelectChange = (_selectedRowKeys, selectedRows) => {
  state.selectedRowKeys = [ ..._selectedRowKeys ];
  emit('change', [ ..._selectedRowKeys ]);
};

const rowSelection = {
  type: 'radio',
  selectedRowKeys: state.selectedRowKeys,
  onChange: onSelectChange,
};

const getShareList = async (shareTimeDesc = true) => {
  tableLoading.value = true;
  const sendData = {
    shareImageGroupId: props.mirrorGroupInfo?.imageGroupId,
    page: paginationOptions.value.current,
    pageSize: paginationOptions.value.pageSize,
    shareTimeDesc
  };
  const { code, data } = await mirrorManageApi.getShareImageList(sendData);
  if (code === 0) {
    tableLoading.value = false;
    dataSource.value = data?.data;
    paginationOptions.value.total = data?.total;
  }
};

const onTableChange = (pagination, filters, sorter, extra) => {
  const { current } = pagination;
  const { order } = sorter;
  paginationOptions.value.current = current;
  getShareList(order !== 'ascend');
};

const resetParams = () => {
  paginationOptions.value.current = 1;
};

const clearSelectedFiles = () => {
  state.selectedRowKeys = [];
  emit('change', []);
};

onMounted(() => {
  getShareList();
});

defineExpose({
  clearSelectedFiles,
  getShareList,
  resetParams,
});
</script>
<style lang="less" scoped>
.cancel-share-detail {

}
</style>