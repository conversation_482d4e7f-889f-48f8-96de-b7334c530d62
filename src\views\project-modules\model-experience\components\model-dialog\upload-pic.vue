<template>
  <a-tooltip placement="top">
    <template #title>
      <div class="tooltip-box">
        <p class="tooltip-title">当前支持上传1张图片</p>
        <p class="tooltip-txt">10M以内，支持jpg/jpeg/png/bmp格式</p>
      </div>
    </template>
    <a-upload :max-count="1" :show-upload-list="false" :before-upload="beforeUpload">
      <a-button type="link" :disabled="uploadPicInfo" :class="uploadPicInfo ? 'upload-disabled-btn' : 'upload-btn'">
        <jt-icon type="icontupian" />
        <span>上传图片</span>
      </a-button>
    </a-upload>
  </a-tooltip>
</template>

<script setup>
import { computed } from 'vue';
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import { toGB } from '@/utils';

const store = useStore();
const uploadPicInfo = computed(() => store.state.modelExperience.uploadPicInfo);
const rules = [
  {
    rule: function (file) {
      const typeValidArr = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'];
      return !typeValidArr.includes(file.type);
    },
    msg: '文件格式不正确',
  },
  {
    rule: function (file) {
      return file.size > 1024 * 1024 * 10;
    },
    msg: '文件大小不能超过10M',
  },
];

const beforeUpload = (file) => {
  const error = rules.find((item) => item.rule(file));
  if (error) {
    return message.error(error.msg);
  }
  const picObj = {
    loading: true,
  };
  store.commit('updateUploadPicInfo', picObj);
  getBase64(file);
};

const getBase64 = (file) => {
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = (e) => {
    const picObj = {
      loading: false,
      name: file.name,
      size: file.size,
      base64Src:e.target.result,
    };
    store.commit('updateUploadPicInfo', picObj);
  };
  reader.onerror = (error) => {
    console.error('Error:', error);
  };
};

const getSize = (val) => {
  const sizeObj = toGB(val);
  return sizeObj.size + sizeObj.unit;
};
</script>

<style lang="less" scoped>
.tooltip-box {
  .tooltip-title {
    font-size: 14px;
  }
  .tooltip-txt {
    font-size: 12px;
    color: rgba(0, 20, 26, 0.45);
  }
}
.upload-btn {
  font-size: 14px;
  color: #00141a;
  padding: 0 0 0 8px;
  :hover {
    color: #00adcc;
  }
}
.upload-disabled-btn {
  padding: 0 0 0 8px;
  color: rgba(0, 20, 26, 0.25);
}
</style>