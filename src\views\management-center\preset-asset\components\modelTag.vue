<template>
  <div class="model-tags-content">
    <div class="tags-container">
      <template v-if="hiddenTagsCount < props.typeList.length">
        <div v-for="(tag, index) in visibleTags" :key="index" class="model-tag">{{ tag }}</div>
        <a-tooltip v-if="isOverflow" placement="top">
          <template #title>
            <span>{{ tags.slice(hiddenTagsCount * -1).join('；') }}</span>
          </template>
          <div class="model-tag-hidden">+{{ hiddenTagsCount }}</div>
        </a-tooltip>
      </template>
      <template v-else>
        <a-tooltip placement="top">
          <template #title>
            <span>{{ props.typeList[0] }}</span>
          </template>
          <div class="first-tag">{{ props.typeList[0] }}</div>
        </a-tooltip>
        <a-tooltip v-if="props.typeList.length > 1" placement="top">
          <template #title>
            <span>{{ props.typeList.slice(1).join('；') }}</span>
          </template>
          <div class="model-tag-hidden">+{{ props.typeList.length - 1 }}</div>
        </a-tooltip>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';

const parentWidth = ref(0);
const showAll = ref(false);
const isOverflow = ref(false);
const props = defineProps({
  typeList: {
    type: Array,
    default() {
      return [];
    },
  },
});
const tags = computed(() => props.typeList);
const visibleTags = computed(() => {
  if (showAll.value) {
    return tags.value;
  } else {
    return tags.value.slice(0, visibleTagsCount.value);
  }
});

const visibleTagsCount = computed(() => {
  let visibleWidth = 0;
  let count = 0;
  for (let i = 0; i < tags.value.length; i++) {
    const tagWidth = getTagWidth(tags.value[i]);
    // 预留一个50的空白宽度
    if (visibleWidth + tagWidth < parentWidth.value - 50) {
      visibleWidth += tagWidth;
      count++;
    } else {
      break;
    }
  }
  return count;
});

const hiddenTagsCount = computed(() => tags.value.length - visibleTagsCount.value);

function getTagWidth(index) {
  const tempDiv = document.createElement('div');
  tempDiv.style.position = 'absolute';
  tempDiv.style.visibility = 'hidden';
  tempDiv.style.whiteSpace = 'nowrap';
  tempDiv.appendChild(document.createTextNode(index));
  document.body.appendChild(tempDiv);
  const width = tempDiv.offsetWidth;
  document.body.removeChild(tempDiv);
  return width;
}

function calculateOverflow() {
  let groupItem = document.querySelector('.model-tags-content');
  parentWidth.value = groupItem.offsetWidth;
  isOverflow.value = tags.value.length > visibleTagsCount.value;
}
watch(
  visibleTagsCount,
  () => {
    calculateOverflow()
  }
)

onMounted(() => {
  window.addEventListener('resize', calculateOverflow);
});

onUnmounted(() => {
  window.removeEventListener('resize', calculateOverflow);
});
</script>

<style lang="less" scoped>
.model-tags-content {
  width: 100%;
  flex: 1;
}
.tags-container {
  display: flex;
  flex-wrap: nowrap;
  // overflow: hidden;
  .model-tag {
    flex-shrink: 0;
    margin-right: 8px;
    cursor: pointer;
    background: #eafbff;
    border-radius: 2px;
    border: 1px solid #8ddbeb;
    font-size: 12px;
    color: #00a0cc;
    line-height: 18px;
    padding: 2px 8px;
  }
  .first-tag {
    max-width: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
    cursor: pointer;
    background: #eafbff;
    border-radius: 2px;
    border: 1px solid #8ddbeb;
    font-size: 12px;
    color: #00a0cc;
    line-height: 18px;
    padding: 2px 8px;
  }
  .model-tag-hidden {
    flex-shrink: 0;
    cursor: pointer;
    background: #eafbff;
    border-radius: 2px;
    border: 1px solid #8ddbeb;
    font-size: 12px;
    color: #00a0cc;
    line-height: 18px;
    padding: 2px 8px;
  }
}
</style>
