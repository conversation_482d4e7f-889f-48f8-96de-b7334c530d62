<template>
  <div v-if="dynamics?.length && !isEntranceBySasac() && !closeNotification" class="dynamic-notification">
    <jt-icon type="iconbofang2" class="notification-icon" />
    <a-carousel class="notification-content" autoplay :autoplaySpeed="3000" :dots="false" dot-position="left">
      <div v-for="(item, index) in dynamics" :key="index">
        {{ item?.dynamicTitle }}
        <a-button v-if="item?.dynamicUrl" type="link" @click="handleClick(item?.dynamicUrl)">查看详情</a-button>
      </div>
    </a-carousel>
    <jt-icon type="iconguanbi" class="notification-icon close" @click="close" />
  </div>
</template>

<script setup>
import { isEntranceBySasac, openInNewTab } from '@/utils';
import { resourceApi } from '@/apis';

const closeNotification = ref(sessionStorage.getItem('CLOSE_HOME_NOTIFICATION') === 'true');
const dynamics = ref([])

onMounted(() => {
  getList()
});

const getList = async () => {
  const res = await resourceApi.getDynamicList();
  if (res?.code == 0) {
    dynamics.value = res?.data;
  }
};

const handleClick = (url) => {
  openInNewTab(url);
};

const close = () => {
  closeNotification.value = true;
  sessionStorage.setItem('CLOSE_HOME_NOTIFICATION', true);
};
</script>

<style lang="less" scoped>
.dynamic-notification {
  display: flex;
  padding: 9px 17px;
  margin-bottom: 20px;
  background: linear-gradient( 117deg, @jt-color-white 0%, rgba(255,255,255,0.85) 100%);
  border-radius: 2px;
  border: 1px solid @jt-color-white;
  backdrop-filter: blur(9px);
  .notification-icon {
    flex: 20px 0;
    color: @jt-primary-color;
    &.close {
      color: #8c8c8c;
    }
  }
  .notification-content {
    flex: 1;
    height: 32px;
    line-height: 32px;
    padding-left: 4px;
  }
}
</style>
