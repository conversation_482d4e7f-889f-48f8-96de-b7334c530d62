export const RESOURCE_STATUS = {
  FREE: '空闲',
  CROWD: '拥挤',
  BUSY: '占满',
};

export const privateResourceColumns = [
  {
    title: '资源组名称',
    dataIndex: 'groupName',
    width: '120px',
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: '80px',
  },
  {
    title: '加速卡类型',
    dataIndex: 'gpucardType',
    width: '120px',
  },
  {
    title: '节点数',
    dataIndex: 'nodenum',
    width: '80px',
  },
  {
    title: '加速卡（已用/总量）',
    dataIndex: 'cardNum',
  },
  {
    title: 'CPU（已用/总量）',
    dataIndex: 'cpuNum',
  },
  {
    title: '内存(GB)（已用/总量）',
    dataIndex: 'memory',
  },
];

export const usageStatusColors = {
  空闲: 'green',
  占满: 'red',
  拥挤: 'orange',
};

export const emptyTexts = {
  public: '目前暂无公共资源组',
  private: '目前暂无专属资源组，请立即关联资源',
};

