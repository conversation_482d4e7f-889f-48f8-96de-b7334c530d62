import router from '@/router';
import store from '@/store';
import isEmpty from 'lodash/isEmpty';

export const addIdToQuery = (urlParams = {}) => {
  if (isEmpty(urlParams)) return;
  const path = location.hash.split('?')[0].substring(1);
  const search = /(?<=#.*\?).*/.exec(location.href)?.[0];
  const params = new URLSearchParams(search);
  const paramsObj = Object.fromEntries(params.entries());
  router.push({
    path,
    query: {
      ...paramsObj,
      ...urlParams,
    }
  });
  store.commit('UPDATE_ROUTEVIEWREFRESHKEY');
};

