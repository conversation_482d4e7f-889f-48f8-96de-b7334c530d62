<template>
  <div v-if="!closed" class="activity-manage-guide">
    <h2>Hi，欢迎使用操作日志！</h2>
    <div v-if="collapse" class="guide-content">
      <p>操作日志支持查看用户在项目空间管理、运管中心中各模块的关键操作日志，包括操作时间、操作人、操作模块、操作、操作结果、操作对象，以及其他详细信息等</p>
    </div>
    <!-- <a-button v-if="collapse" type="default" style="margin: 4px 0px" @click="checkHelp">查看帮助</a-button> -->
    <a-button v-if="collapse" type="default" disabled style="margin: 4px 0px">查看帮助</a-button>
    <a-space :size="8" class="operation-btns">
      <jt-icon :type="collapse ? 'iconshouqi' : 'iconzhankai'" class="iconfont" @click="handleToggle" />
      <jt-icon type="iconguanbi" class="iconfont" @click="handleClose" />
    </a-space>
    <img v-if="collapse" :src="bannerImg" class="banner-img" alt="" />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const bannerImg = require('@/assets/images/list-banner/ope-log.png');
const storageKey = 'KUNLUN-OPERATION-LOG';
const guideState = sessionStorage.getItem(storageKey);

const collapse = ref(guideState === '' ? false : true);
const closed = ref(guideState === 'closed' ? true : false);

const handleToggle = () => {
  collapse.value = !collapse.value;
  sessionStorage.setItem(storageKey, collapse.value ? 'collapse' : '');
};

const handleClose = () => {
  closed.value = true;
  sessionStorage.setItem(storageKey, 'closed');
};

// const checkHelp = () => {
//   const urlPrefix = 'https://jiutian.10086.cn/portal/common-helpcenter#/document/695?platformCode=DMX_KFPT';
//   window.open(urlPrefix, '_blank');
// };
</script>

<style lang="less" scoped>
.activity-manage-guide {
  padding: 20px;
  margin: 8px 0 20px;
  position: relative;
  background-color: #fff;
  flex-shrink: 0;
  border-radius: 4px;
  box-shadow: @jt-box-shadow;
  .banner-img {
    position: absolute;
    bottom: 0px;
    right: 0px;
    height: 204px;
  }
  .guide-content {
    display: flex;
    margin-bottom: 12px;
    p {
      width: 762px;
      margin-top: 12px;
      margin-right: 60px;
      color: rgba(0, 20, 26, 0.7);
      line-height: 22px;
    }
  }
  h2 {
    font-size: @jt-font-size-lger;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    margin-bottom: 0;
  }
  .operation-btns {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    span {
      background-color: #fff;
      display: inline-block;
      height: 24px;
      line-height: 26px;
      width: 24px;
      border-radius: 2px;
      border: 1px solid rgba(0, 20, 26, 0.15);
      text-align: center;
      cursor: pointer;
      color: rgba(0, 20, 26, 0.45);
      transition: 0.3s all;
      &:hover {
        color: @jt-primary-color;
        border-color: @jt-primary-color;
      }
    }
  }
}
:deep .ant-btn-default {
  background-color: rgba(0,20,26,0.04);
  border-color: rgba(0,20,26,0.25);
  box-shadow: none;
  color: rgba(0,20,26,0.25);
}
:deep .ant-btn.ant-btn-default:not(:hover):not(.ant-btn-dangerous){
  color: rgba(0,20,26,0.25);
}
:deep .ant-btn-default:not(:disabled):hover {
  color: rgba(0,20,26,0.25);
  border-color: rgba(0,20,26,0.25);
}
</style>
