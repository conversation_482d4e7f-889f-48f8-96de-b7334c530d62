<template>
  <div class="form-box">
    <a-form ref="formRef" :model="form" :label-col="labelCol" :rules="rules">
      <!-- 调用方式 -->
      <a-form-item label="调用方式" name="callMethod" style="margin-bottom:24px">
        <a-checkbox-group v-model:value="form.callMethod">
          <a-checkbox value="1" name="type" style="margin-right: 20px">Python</a-checkbox>
          <a-checkbox value="2" name="type" style="margin-right: 20px">Java</a-checkbox>
          <a-checkbox value="3" name="type">Curl</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <!-- Python示例代码 -->
      <a-form-item v-if="isShowCode('1')" label="Python示例代码" name="pythonExampleCode" class="big-gap">
        <JtTextarea v-model:value="form.pythonExampleCode" :rows="10" class="textarea space-info" placeholder="请输入" :maxlength="10000" />
      </a-form-item>
      <!-- Java示例代码 -->
      <a-form-item v-if="isShowCode('2')" label="Java示例代码" name="javaExampleCode" class="big-gap">
        <JtTextarea v-model:value="form.javaExampleCode" :rows="10" class="textarea space-info" placeholder="请输入" :maxlength="10000" />
      </a-form-item>
      <!-- Curl示例代码 -->
      <a-form-item v-if="isShowCode('3')" label="Curl示例代码" name="curlExampleCode" class="big-gap">
        <JtTextarea v-model:value="form.curlExampleCode" :rows="10" class="textarea space-info" placeholder="请输入" :maxlength="10000" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import isEmpty from 'lodash/isEmpty';
import { computed } from 'vue';
import JtTextarea from '@/components/JtTextarea.vue';
const isValidate = ref(false);
const props = defineProps({
  defaultData: {
    type: Object,
    default: () => {},
  },
});

// form表单相关
const labelCol = {
  style: {
    width: '120px',
  },
};
const formRef = ref(null);
const form = ref({
  callMethod: [], // 调用方式
  pythonExampleCode: '',
  javaExampleCode: '',
  curlExampleCode: '',
});
const isShowCode = (codeType) => {
  if (form.value.callMethod) {
    if (form.value.callMethod.indexOf(codeType) !== -1) {
      return true;
    } else {
      return false;
    }
  }
};
// 代码校验
const onCheckCode = async (_rule, value) => {
  if (!value.trim()) {
    return Promise.reject('请输入');
  }
  if (value.length > 10000) {
    return Promise.reject('请输入10000字符以内');
  }
  return Promise.resolve();
};
// 校验规则
const rules = {
  callMethod: [{ required: true, message: '请选择调用方式' }],
  pythonExampleCode: [{ required: true, validator: onCheckCode, trigger: ['change', 'blur'] }],
  javaExampleCode: [{ required: true, validator: onCheckCode, trigger: ['change', 'blur'] }],
  curlExampleCode: [{ required: true, validator: onCheckCode, trigger: ['change', 'blur'] }],
};
const emits = defineEmits(['is-validate']);
const handleValide = async () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate().then(
      () => {
        isValidate.value = true;
        emits('is-validate', isValidate.value);
        resolve(true);
      },
      (err) => {
        isValidate.value = false;
        emits('is-validate', isValidate.value);
        reject(new Error(false));
      }
    );
  });
};
const clearValidate = () => {
  formRef.value.clearValidate();
};
const integrateData = () => {
  return { ...form.value };
};
defineExpose({
  validate: handleValide,
  getData: integrateData,
  clearValidate: clearValidate,
});
watch(
  () => props.defaultData,
  (val) => {
    if (!isEmpty(props.defaultData)) {
      form.value.callMethod = val.callMethod ? (Array.isArray(val.callMethod) ? val.callMethod : val.callMethod.split(',')) : [];
      form.value.pythonExampleCode = val.pythonExampleCode || '';
      form.value.javaExampleCode = val.javaExampleCode || '';
      form.value.curlExampleCode = val.curlExampleCode || '';
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => form,
  (val) => {
    emits('is-validate', true);
  },
  {
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.big-gap {
  margin-bottom: 32px;
}
</style>