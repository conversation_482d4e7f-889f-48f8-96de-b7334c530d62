<template>
    <div class="resource-table-box">
      <a-table :columns="typeAColumns" :data-source="typeADataShow" :pagination="false">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'source'">
            <a-tooltip placement="topLeft">
              <template #title>
                <span>{{ recordSource(text) }}</span>
              </template>
              <span>{{ recordSource(text) }}</span>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'imageId'">
            <a-tooltip placement="topLeft">
              <template #title>
                <span>{{ getMirrorName(text, mirrorFileOptions) }}</span>
              </template>
              <span>{{ getMirrorName(text, mirrorFileOptions) }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'startCommand'">
            <a-tooltip placement="topLeft">
              <template #title>
              <span style="overflow: hidden; max-height: 300px; overflow-y: scroll; display: inline-block; word-break: break-all">{{ text }}</span>
            </template>
              <span>{{ text }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'replicasLowerLimit'">
            <span>{{ `${text}-${record.replicasUpperLimit}个` }}</span>
          </template>
          <template v-else-if="column.dataIndex === 'instanceCardLowerLimit'">
            <span>{{ `${text}-${record.instanceCardUpperLimit}卡` }}</span>
          </template>
        </template>
        <template #emptyText>
          <light-empty title="暂无配置" />
        </template>
      </a-table>
      <div v-if="typeACount > 0" class="pagination-box">
        <jt-pagination :page-num="typeACurrentPage" :page-size="typeAPageSize" :total="typeACount" @changePageNum="onTypeAPageChange" @changePageSize="onTypeAShowSizeChange"></jt-pagination>
      </div>
    </div>
  </template>
  
  <script setup>
  import { getMirrorName } from '../../../util';
  import LightEmpty from './light-empty.vue';
  const props = defineProps({
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    showStartCommand: {
      type: Boolean,
      default: true,
    },
  });
  
  const typeAColumns = ref([
    {
      title: '加速卡类型',
      dataIndex: 'gpucardType',
      key: 'gpucardType',
      width: 2,
    },
    {
      title: '镜像文件',
      dataIndex: 'imageId',
      key: 'imageId',
      ellipsis: true,
      width: 2,
    },
    {
      title: '启动命令',
      dataIndex: 'startCommand',
      key: 'startCommand',
      ellipsis: true,
      width: 4,
    },
    {
      title: '实例数',
      dataIndex: 'replicasLowerLimit',
      key: 'replicasLowerLimit',
      width: 2,
    },
    {
      title: '单实例卡数',
      dataIndex: 'instanceCardLowerLimit',
      key: 'instanceCardLowerLimit',
      width: 5,
    },
  ]);
  const typeADataShow = ref([]);
  const typeACount = ref(0);
  const typeACurrentPage = ref(1);
  const typeAPageSize = ref(5);
  
  const sourceOptions = computed(() => [
    {
      label: '模型压缩',
      value: '4',
    },
    {
      label: '增量预训练',
      value: '5',
    },
    {
      label: '有监督微调',
      value: '6',
    },
    {
      label: '偏好对齐',
      value: '7',
    },
  ]);
  
  const recordSource = (val) => {
    return sourceOptions.value.find((item) => item.value === val).label;
  };
  
  const getTableInfo = () => {
    typeADataShow.value = props.list.slice((typeACurrentPage.value - 1) * typeAPageSize.value, typeACurrentPage.value * typeAPageSize.value);
    typeADataShow.value = typeADataShow.value.filter((item) => item.replicasLowerLimit !== 0);
  };
  const mirrorFileOptions = inject('mirrorFileOptions');
  
  const onTypeAShowSizeChange = (pageSize) => {
    typeAPageSize.value = pageSize;
    typeACurrentPage.value = 1;
    getTableInfo();
  };
  // 跳至xx页
  const onTypeAPageChange = (pageIndex) => {
    typeACurrentPage.value = pageIndex;
    getTableInfo();
  };
  
  watch(
    () => props.list,
    (val) => {
      if (val.length > 0) {
        typeACount.value = val.length;
        getTableInfo();
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
  watch(
    () => props.showStartCommand,
    (val) => {
      if (!val) {
        typeAColumns.value.splice(2, 1);
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
  </script>
  
  <style lang="less" scoped>
  .resource-table-box {
    margin-bottom: 32px;
  }
  .empty {
    margin: 20px 0 40px;
    img {
      width: 64px;
      display: block;
      margin: auto;
    }
    p {
      margin-top: 12px;
      font-size: 16px;
      color: @jt-text-color-primary;
    }
  }
  </style>
  