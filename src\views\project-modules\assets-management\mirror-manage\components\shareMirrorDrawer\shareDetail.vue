<template>
  <a-form ref="formRef" :model="form" :rules="rules" class="share-detail-form" :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }" autocomplete="off">
    <a-form-item label="镜像名称" name="imageName">
      {{ form.imageName }}
    </a-form-item>
    <a-form-item label="适用场景" name="scene">
      <a-space :size="8">
        <div class="scene" v-for="item in form.scene" :key="item">
          <span :class="['scene-label', item === IMAGE_SCENE_TYPE.TRAIN && 'train']">{{ IMAGE_SCENE_MSG[item] }}</span>
        </div>
      </a-space>
    </a-form-item>
    <a-form-item label="架构" name="architecture">
      {{ ARCHITECTURE_TYPE_MSG[form.architecture] || '-' }}
    </a-form-item>
    <a-form-item label="芯片类型" name="chipType">
      {{ CHIP_TYPE_MSG[form.chipType] || '-' }}
    </a-form-item>
    <a-form-item style="margin-bottom: 32px" label="镜像文件" name="files" :autoLink="false">
      <mirror-file-table v-model:selectedRowKeys="form.files" :mirror-group-info="mirrorGroupInfo" />
    </a-form-item>
    <a-form-item label="分享对象" name="objectProject" :help="idErrorText" :validate-status="idError">
      <a-input ref="inputRef" v-model:value="form.objectProject" placeholder="请输入分享对象的项目空间ID" />
    </a-form-item>
  </a-form>
</template>
<script setup>
import { IMAGE_SCENE_TYPE, IMAGE_SCENE_MSG, ARCHITECTURE_TYPE_MSG, CHIP_TYPE_MSG } from '@/constants/image';
import { projectSpaceIdRegex } from '@/constants/regex';
import MirrorFileTable from './mirrorFileTable.vue';

const props = defineProps({
  mirrorGroupInfo: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['change']);

const form = reactive({
  imageName: props?.mirrorGroupInfo?.imageGroupName,
  scene: props?.mirrorGroupInfo?.scene === IMAGE_SCENE_TYPE.ALL ? [IMAGE_SCENE_TYPE.TRAIN, IMAGE_SCENE_TYPE.REASON] : [props?.mirrorGroupInfo?.scene],
  architecture: props?.mirrorGroupInfo?.architecture,
  chipType: props?.mirrorGroupInfo?.chipType,
  files: [],
  objectProject: '',
});

const formRef = ref();
const idErrorText = ref('长度为32，且需同时包含数字和小写字母');
const idError = ref('success');
const inputRef = ref();
const projectIsIllegal = ref(false);

const validateFiles = async () => {
  if (!form.files?.length) {
    return Promise.reject('');
  } else {
    return Promise.resolve();
  }
};

const onInputChange = (rule, value, callback) => {
  return new Promise((resolve, reject) => {
    if (projectIsIllegal.value) {
      idErrorText.value = '项目不存在';
      idError.value = 'error';
      return reject(idErrorText.value);
    }
    if (!projectSpaceIdRegex.test(value)) {
      idErrorText.value = !value ? '请输入' : '长度为32，且需同时包含数字和小写字母';
      idError.value = 'error';
      return reject(idErrorText.value);
    } else {
      idErrorText.value = '长度为32，且需同时包含数字和小写字母';
      idError.value = 'success';
      projectIsIllegal.value = false;
      return resolve();
    }
  });
 };

const rules = {
  files: [
    { required: true, message: '请选择至少一个镜像文件', trigger: 'blur', type: 'array' },
    // { validator: validateFiles, trigger: ['change', 'blur'], type: 'array' },
  ],
  objectProject: [
    { required: true, message: '请输入', trigger: 'change'},
    { validator: onInputChange, trigger: ['change', 'blur'] },
  ],
};

const validateFields = () => {
  return formRef.value
    .validate()
    .then((res) => {
      return {
        selectedFiles: form.files,
        objectProject: form.objectProject,
      }
    })
    .catch((error) => {
      throw new Error(error);
    });
};

const clearSelectedFiles = () => {
  form.files = [];
};

const clearProjectId = () => {
  form.objectProject = '';
};

// 点击分享时进行项目空间校验
const projectNotExist = () => {
  projectIsIllegal.value = true;
  onInputChange();
};

watch(
  () => form.objectProject,
  () => {
    projectIsIllegal.value = false;
  }
);

watch(
  () => form.files,
  () => {
    emit('change', form.files);
  }
);

defineExpose({
  validateFields,
  clearSelectedFiles,
  clearProjectId,
  projectNotExist,
});
</script>
<style lang="less" scoped>
.share-detail-form {
  position: relative;
  .scene {
    .scene-label {
      padding: 4px 8px;
      color: #13aa36;;
      background: #e8ffea;
      border: 1px solid #83dd8f;
      font-size: @jt-font-size-sm;
      border-radius: 2px;
      &.train {
        color: #0a74f9;
        background: #ebF5fe;
        border-color: #9ccbfd;
      }
    }
  }
  .ant-form-item {
    margin-bottom: 20px;
  }
}
</style>