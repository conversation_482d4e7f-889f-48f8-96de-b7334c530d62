<template>
  <a-drawer :open="open" :bodyStyle="{ paddingTop: '16px' }" :footer-style="{ textAlign: 'right', padding: '8px 24px 24px 8px', height: 'auto' }" title="同步至自定义镜像" width="528" destroy-on-close @close="handleCancel">
    <a-form ref="formRef" :model="form" class="image-form" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
      <a-form-item label="保存方式" name="method">
        <a-radio-group v-model:value="form.method" @change="methodChange">
          <a-radio :value="SYNC_METHOD.EXIST">已有镜像新版本</a-radio>
          <a-radio :value="SYNC_METHOD.CREATE">发布为新镜像</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="form.method === SYNC_METHOD.EXIST" label="选择镜像" name="selectImageName">
        <a-select
          v-model:value="form.selectImageName"
          placeholder="请选择镜像名称"
          :get-popup-container="
            (triggerNode) => {
              return triggerNode.parentNode;
            }
          "
          :options="imageOptions"
        />
      </a-form-item>
      <a-form-item v-else label="镜像名称" :help="helpText" :validate-status="nameError" name="inputImageName">
        <a-input v-model:value="form.inputImageName" placeholder="请输入镜像名称" />
      </a-form-item>
      <a-form-item style="margin-bottom: -4px" label="适用场景" name="scene">
        <a-space :size="16">
          <div class="scene" v-for="item in form.scene" :key="item">
            <span :class="['scene-label', item === IMAGE_SCENE_TYPE.TRAIN && 'train']">{{ IMAGE_SCENE_MSG[item] }}</span>
          </div>
        </a-space>
      </a-form-item>
      <a-form-item style="margin-bottom: -4px" label="架构" name="architecture">
        {{ ARCHITECTURE_TYPE_MSG[form.architecture] || '-' }}
      </a-form-item>
      <a-form-item label="芯片类型" name="chipType">
        {{ CHIP_TYPE_MSG[form.chipType] || '-' }}
      </a-form-item>
      <a-form-item v-if="form.method === SYNC_METHOD.CREATE" label="镜像描述" name="description">
        <div class="textarea-input">
          <a-textarea v-model:value="form.description" show-word-limit placeholder="请输入" />
          <p>
            <span
              :class="{
                error: form.description !== '' && form.description.length > rules.description[0].max,
              }"
            >
              {{ form.description.length }}
            </span>
            / {{ rules.description[1].max }}
          </p>
        </div>
      </a-form-item>
      <a-form-item label="镜像标签" :help="`请添加1个标签，12个字符以内的大小写字母、数字、中划线、下划线、小数点`" name="tags">
        <template v-for="tag in form.tags" :key="tag">
          <a-tooltip v-if="tag.length > 20" :title="tag">
            <jt-tag rounded="small" class="image-tag" :closable="true" color="mirror-blue-jt" @close="handleClose(tag)">
              {{ `${tag.slice(0, 20)}...` }}
            </jt-tag>
          </a-tooltip>
          <jt-tag v-else rounded="small" class="image-tag" :closable="true" color="mirror-blue-jt" @close="handleClose(tag)">
            {{ tag }}
          </jt-tag>
        </template>
        <a-input v-if="state.inputVisible" ref="inputRef" v-model:value="state.inputValue" type="text" size="small" :style="{ width: '78px', padding: '5px' }" :class="isInputErr && 'input-err'" @blur="handleInputConfirm" @keyup.enter="handleInputConfirm" />
        <jt-tag v-if="!state.inputVisible && form.tags?.length < tagCount" rounded="small" class="add-tag" @click="showInput">
          <jt-icon type="icontianjia" />
          添加标签
        </jt-tag>
      </a-form-item>
      <a-form-item label="版本描述" name="versionDescription">
        <div class="textarea-input">
          <a-textarea v-model:value="form.versionDescription" show-word-limit placeholder="请输入" />
          <p>
            <span
              :class="{
                error: form.versionDescription !== '' && form.versionDescription.length > rules.versionDescription[0].max,
              }"
            >
              {{ form.versionDescription.length }}
            </span>
            / {{ rules.versionDescription[1].max }}
          </p>
        </div>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="handleCancel">取消</a-button>
      <a-button class="kl-create-btn" type="primary" :loading="loading" @click="onSubmit">同步</a-button>
    </template>
  </a-drawer>
</template>
    
<script setup>
import { ref, defineProps, watch, defineEmits, reactive, toRaw, nextTick, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { imageApi, modelTrainApi, mirrorManageApi } from '@/apis';
import { message } from 'ant-design-vue';
import { IMAGE_SCENE_TYPE, IMAGE_SCENE_MSG, ARCHITECTURE_TYPE_MSG, CHIP_TYPE_MSG, SYNC_METHOD, imageErrorUseBackMsg } from '@/constants/image';
import { imageNameRegex, imageTagRegex } from '@/constants/regex';
import { IMAGE_TYPE } from '@/constants/trainTask';
import ErrorCode from '@/constants/errorCode';
import isEmpty from 'lodash/isEmpty';
import { debounce } from 'lodash';

const store = useStore();
const router = useRouter();
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
  mirrorData: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['close']);

const imageOptions = ref([]); // 镜像信息
const helpText = ref('24个字符内，以字母开头，支持英文小写a-z、数字0-9');
const tagCount = ref(1); // 标签个数
const isInputErr = ref(false);
const formRef = ref();
const inputRef = ref();
const nameError = ref('success');
const loading = ref(false);
const form = reactive({
  method: SYNC_METHOD.EXIST,
  selectImageName: null,
  inputImageName: '',
  scene: [IMAGE_SCENE_TYPE.TRAIN],
  architecture: '-',
  chipType: '-',
  description: '',
  tags: [],
  versionDescription: '',
});

const handleInputChange = async () => {
  if (!imageTagRegex.test(state.inputValue)) {
    return Promise.reject('');
  } else {
    return Promise.resolve();
  }
};

const validateImageName = debounce(() => {
  return new Promise(async (resolve, reject) => {
    if (!imageNameRegex.test(form.inputImageName)) {
      helpText.value = '24个字符内，以字母开头，支持英文小写a-z、数字0-9';
      nameError.value = 'error';
      return reject('24个字符内，以字母开头，支持英文小写a-z、数字0-9');
    } else {
      // 检查项目空间内是否存在同名的镜像
      const res = await imageApi.matchImages({ imageName: form.inputImageName, scene: form.scene.includes(IMAGE_SCENE_TYPE.TRAIN) && form.scene.includes(IMAGE_SCENE_TYPE.REASON) ? [IMAGE_SCENE_TYPE.ALL] : form.scene, architecture: [form.architecture], chipType: [form.chipType] });
      if (res?.code === 0 && res?.data.find((item) => item.imageGroupName === form.inputImageName)) {
        helpText.value = '镜像名称重复';
        nameError.value = 'error';
        return reject('镜像名称重复');
      }
      nameError.value = 'success';
      helpText.value = '24个字符内，以字母开头，支持英文小写a-z、数字0-9';
      return resolve();
    }
  });
}, 100);

const rules = {
  selectImageName: [{ required: true, message: '请选择', trigger: 'blur' }],
  inputImageName: [
    { required: true, message: '请输入', trigger: 'blur' },
    { validator: validateImageName, trigger: ['change', 'blur'] },
  ],
  description: [
    { required: true, message: '请输入镜像描述信息', trigger: 'blur' },
    { max: 80, message: '80个字符以内', trigger: 'change' },
  ],
  tags: [
    { required: true, message: '请输入标签', trigger: 'blur', type: 'array' },
    { validator: handleInputChange, trigger: ['change', 'blur'], type: 'array' },
  ],
  versionDescription: [
    { required: true, message: '请输入版本描述信息', trigger: 'blur' },
    { max: 80, message: '80个字符以内', trigger: 'change' },
  ],
};

const state = reactive({
  tags: [],
  inputVisible: false,
  inputValue: '',
});

const showInput = () => {
  state.inputVisible = true;
  nextTick(() => {
    inputRef.value.focus();
  });
};

const handleClose = (removedTag) => {
  const tags = form.tags.filter((tag) => tag !== removedTag);
  form.tags = tags;
};

const handleInputConfirm = () => {
  if (isInputErr.value) return;
  const inputValue = state.inputValue;
  let tags = form.tags;
  if (tags?.length >= tagCount.value) return;
  if (inputValue && imageTagRegex.test(inputValue) && tags.indexOf(inputValue) === -1) {
    isInputErr.value = false;
    tags = [...tags, inputValue];
  } else {
    isInputErr.value = true;
  }
  form.tags = tags;
  Object.assign(state, { tags, inputVisible: false, inputValue: '' });
};

watch(
  () => props.open,
  (visible) => {
    if (visible) {
      const { scene, architecture, chipType } = props.mirrorData;
      form.scene = scene === IMAGE_SCENE_TYPE.ALL ? [IMAGE_SCENE_TYPE.TRAIN, IMAGE_SCENE_TYPE.REASON] : [scene];
      form.architecture = architecture;
      form.chipType = chipType;
      getImageList();
    }
  }
);

// 标签输入校验
watch(
  () => state.inputValue,
  (value) => {
    isInputErr.value = !imageTagRegex.test(value);
  }
);

const getImageList = async () => {
  const { scene, architecture, chipType } = form;
  const res = await imageApi.matchImages({
    imageName: '',
    scene: scene.includes(IMAGE_SCENE_TYPE.TRAIN) && scene.includes(IMAGE_SCENE_TYPE.REASON) ? [IMAGE_SCENE_TYPE.ALL] : scene,
    architecture: [architecture],
    chipType: [chipType],
  });
  const { code, data } = res;
  if (code === 0) {
    imageOptions.value = (data || []).map((item) => {
      return {
        label: item?.imageGroupName,
        value: item?.imageGroupName,
      };
    });
  }
};

const methodChange = () => {
  formRef.value.clearValidate();
  formRef.value.resetFields(['selectImageName', 'inputImageName', 'description', 'tags', 'versionDescription']);
  nameError.value = 'success';
  helpText.value = '24个字符内，以字母开头，支持英文小写a-z、数字0-9';
};

// 同步后需要返回新镜像的信息，用于页面跳转
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      loading.value = true;
      const { method, selectImageName, inputImageName, description, versionDescription, tags } = toRaw(form);
      const { id } = props.mirrorData;
      mirrorManageApi
        .syncImage({ id, sharedImageName: method === SYNC_METHOD.EXIST ? selectImageName : inputImageName, imageGroupDesc: description, imageDesc: versionDescription, tags })
        .then((res) => {
          loading.value = false;
          if (res.code === 0) {
            message.success({
              content: '镜像文件同步至自定义镜像成功',
              duration: 1,
              onClose: () => {
                emit('close');
                const syncImageGroupId = res?.data;
                router.push(`/mirrorManage/detail/${syncImageGroupId}`);
              },
            });
          } else {
            if (imageErrorUseBackMsg.includes(res.code)) {
              message.error(`当前镜像下标签${res.msg}重复`);
            } else {
              message.error(ErrorCode[res.code] || '镜像文件同步至自定义镜像失败，请稍后重试');
            }
          }
        })
        .catch((err) => {
          loading.value = false;
          message.error(ErrorCode[res.code] || '镜像文件同步至自定义镜像失败，请稍后重试');
        });
    })
    .catch((error) => {
      throw new Error(error);
    });
};

function handleCancel() {
  formRef.value.resetFields();
  Object.assign(state, { tags: [], inputVisible: false, inputValue: '' });
  emit('close');
}
</script>
<style lang="less" scoped>
.image-tag {
  margin-block-end: 8px;
  padding: 5px;
  color: @jt-primary-color;
  font-size: @jt-font-size-base;
  background: #e6f7fa;
  border-color: #5ac8e0;
}
:deep(.ant-tag .ant-tag-close-icon) {
  color: @jt-primary-color;
}
:deep(.ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled)) {
  background: #e6f7fa;
}
:deep(.ant-select-dropdown .ant-select-item-option:hover) {
  background-color: rgba(0, 20, 26, 0.04);
}
.add-tag {
  padding: 5px;
  font-size: @jt-font-size-base;
  background: #fff;
  border-style: dashed;
}
.scene {
  .scene-label {
    padding: 4px 8px;
    color: #13aa36;;
    background: #e8ffea;
    border: 1px solid #83dd8f;
    font-size: @jt-font-size-sm;
    border-radius: 2px;
    &.train {
      color: #0a74f9;
      background: #ebF5fe;
      border-color: #9ccbfd;
    }
  }
}
// 多行文本框
.textarea-input {
  position: relative;
  textarea {
    width: 100%;
    height: 144px;
    resize: none;
    padding: 8px;
    display: block;
  }
  p {
    color: @jt-text-color-secondary;
    position: absolute;
    right: 8px;
    bottom: 8px;
    user-select: none;
    margin-bottom: 0;
    span {
      &.error {
        color: @jt-error-color;
      }
    }
  }
}
.input-err {
  border-color: @jt-error-color;
}
.error {
  position: absolute;
  color: @jt-error-color;
}
:deep(.ant-form-item) {
  .ant-form-margin-offset {
    margin-bottom: 0 !important;
  }
}
</style>