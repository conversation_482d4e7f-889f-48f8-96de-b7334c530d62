<template>
  <a-table :columns="createProjectColumn" :loading="loading" :dataSource="dataSource" :pagination="false" @change="onChange">
    <template #emptyText>
      <div style="margin-top: 30px">
        <img :src="emptyImg" alt="" width="80" />
        <div style="padding-top: 16px; margin-bottom: 50px; color: rgba(0, 20, 26, 0.7); font-size: 14px">暂无创建项目</div>
      </div>
    </template>
    <template #bodyCell="{ column, record, text }">
      <template v-if="column.dataIndex === 'projectName'">
        <a-tooltip>
          <template #title>
            {{ text }}
          </template>
          <div class="route-hover-item" @click="() => routeToDetail(record)">{{ text }}</div>
        </a-tooltip>
      </template>
    </template>
  </a-table>
  <jt-pagination :total="paginationOptions.total" :page-num="paginationOptions.pageNum" :page-size="paginationOptions.pageSize" @changePageSize="changePageSize" @changePageNum="changePageNum" />
</template>

<script setup>
import { quotaApi } from '@/apis';
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getFormatTime, openInNewTab } from '@/utils';
const emptyImg = require('@/assets/images/activity-manage/empty.png');
const route = useRoute();
const router = useRouter();

const createProjectColumn = [
  {
    title: '项目空间名称',
    key: 'projectName',
    dataIndex: 'projectName',
  },
  {
    title: '项目空间ID',
    key: 'projectId',
    dataIndex: 'projectId',
  },
  {
    title: '成员数',
    key: 'memberCount',
    dataIndex: 'memberCount',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    sorter: true,
    customRender({ text }) {
      return text ? getFormatTime(text) : '--';
    },
  },
];

let sortField = undefined;
let isAsc = undefined;
const dataSource = ref([]);
const loading = ref(false);
const paginationOptions = ref({
  total: 0,
  pageSize: 10,
  pageNum: 1,
});

// onMounted(() => {
//   getTable();
// });

const routeToDetail = (record) => {
  const to = router.resolve({
    path: `/res-dashboard/project-detail/${record.projectId}`,
  });
  openInNewTab(to.href);
};

const getTable = async () => {
  const res = await quotaApi.getCreateProjectListByUser({
    sortField,
    isAsc,
    pageSize: paginationOptions.value.pageSize,
    pageNum: paginationOptions.value.pageNum,
    userName: route.query.userName,
  });
  if (res.code === 0) {
    dataSource.value = res.data.data;
    paginationOptions.value.total = res.data.total;
  } else {
    dataSource.value = [];
    paginationOptions.value.total = 0;
  }
};
const changePageNum = (pageNum) => {
  paginationOptions.value.pageNum = pageNum;
  getTable();
};
const changePageSize = (size) => {
  paginationOptions.value.pageNum = 1;
  paginationOptions.value.pageSize = size;
  getTable();
};
const onChange = (pagination, filters, sorter) => {
  if (sorter.order) {
    sortField = sorter.field;
    isAsc = sorter.order === 'ascend' ? true : false;
  } else {
    sortField = undefined;
    isAsc = undefined;
  }
  getTable();
};

watch(
  () => route.query.userName,
  () => {
    getTable();
  }, {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped></style>
