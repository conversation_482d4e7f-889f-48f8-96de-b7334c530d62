<template>
  <div class="pie-chart">
    <v-chart :option="chartOptions" autoresize style="height: 525px; width: 100%" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { PieChart } from 'echarts/charts'; // Changed to LineChart
import { GridComponent, LegendComponent, TitleComponent, TooltipComponent, DataZoomComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';

import VChart from 'vue-echarts';

use([CanvasRenderer, PieChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);
import {pieChartData} from '../constants';

var color = pieChartData.map((item) => item.color);
var xdata = pieChartData.map((item) => item.name);

const chartOptions = ref({
  color: color,
  legend: {
    orient: 'vertical',
    x: 'left',
    top: 'center',
    left: '60%',
    bottom: '0%',
    data: xdata,
    itemWidth: 16, // 设置图例标记的宽度
    itemHeight: 16, // 设置图例标记的高度
    itemGap: 16,
    icon: 'circle', // 设置图例标记的形状为圆形
    textStyle: {
      color: '#9CC0D4',
      fontSize: 24,
    },
    formatter: function (name) {
      return '' + name;
    },
  },
  series: [
    {
      type: 'pie',
      clockwise: false, //饼图的扇区是否是顺时针排布
      minAngle: 2, //最小的扇区角度（0 ~ 360）
      radius: ['45%', '70%'],
      center: ['30%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        //图形样式
        // normal: {
        //   borderColor: 'transparent',
        //   borderWidth: 6,
        // },
      },
      label: {
        normal: {
          show: false,
          position: 'center',
          formatter: '{value|{c}台}\n{percent|{d}%}\n{text|{b}} ',
          rich: {
            value: {
              color: '#D6FBFF',
              fontSize: 32,
              align: 'center',
              verticalAlign: 'middle',
              padding: 8,
            },
            percent: {
              color: '#D6FBFF',
              fontSize: 32,
              align: 'center',
              verticalAlign: 'middle',
              padding: 8,
            },
            text: {
              color: '#D6FBFF',
              fontSize: 48,
              align: 'center',
              verticalAlign: 'middle',
              padding: 8,
            },
          },
        },
        emphasis: {
          show: true,
          textStyle: {
            fontSize: 24,
          },
        },
      },
      data: ydata,
    },
  ],
});
</script>

<style lang="scss" scoped></style>
