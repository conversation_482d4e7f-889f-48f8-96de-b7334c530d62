<template>
  <div class="wrapper-box">
    <jt-sub-header :bread-crumb="breadValue"></jt-sub-header>
    <div v-if="!hasAuth" class="content-box">
      <jt-welcome-page :data="guideData" @toDetail="toDetail"></jt-welcome-page>
    </div>
    <section v-else class="content-box">
      <nav-bar @createNew="createNew" @viewNow="viewNow"></nav-bar>
      <div class="content-wrap-box">
        <!-- 页面内容 分tab栏-->
        <a-tabs default-active-key="prepareMirror" :active-key="currentTab" size="large" @change="changeTab">
          <template #rightExtra>
            <a-space>
              <jt-reload-icon-btn @click="handelReload" />
              <a-input v-model:value="searchText" class="input" :placeholder="searchBarPlaceholder" allow-clear>
                <template #prefix>
                  <jt-icon type="iconsousuo" class="search-icon" style="color: #bec2c5"></jt-icon>
                </template>
              </a-input>
              <a-button v-if="currentTab === 'customMirror'" type="primary" class="kl-create-btn" @click="handleNewMirror">
                新建镜像
                <template #icon>
                  <jt-icon type="icontianjia" class="add-icon"></jt-icon>
                </template>
              </a-button>
            </a-space>
          </template>

          <a-tab-pane key="prepareMirror">
            <template #tab>
              <span class="tab-bar"> 平台预置镜像 </span>
              {{ prepareMirrorCount }}
            </template>
            <prepare-mirror-list ref="prepareList" :search-text="searchText" :current-tab="currentTab"></prepare-mirror-list>
          </a-tab-pane>

          <a-tab-pane key="customMirror">
            <template #tab>
              <span class="tab-bar"> 自定义镜像 </span>
              {{ customMirrorCount }}
            </template>
            <custom-mirror-list ref="mirrorList" :search-text="searchText" :current-tab="currentTab" @getCount="getCount" @createNew="handleNewMirror" @updateGroup="updateMirrorGroup"></custom-mirror-list>
          </a-tab-pane>

          <a-tab-pane key="shareMirror">
            <template #tab>
              <span class="tab-bar"> 共享镜像 </span>
              {{ shareMirrorCount }}
            </template>
            <!--共享镜像列表-->
            <share-mirror-list ref="shareMirrorList" :search-text="searchText" :current-tab="currentTab" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </section>
    <template v-if="showAddModal">
      <createMirrorGroup :add-visible="showAddModal" :edit-mirror-group="editMirror" :mirror-group-info="editMirrorInfo" @childFn="closeModal"> </createMirrorGroup>
    </template>
  </div>
</template>

<script>
import prepareMirrorList from './components/prepareMirrorList.vue';
import customMirrorList from './components/customMirrorList.vue';
import shareMirrorList from './components/shareMirrorList.vue';
import createMirrorGroup from './components/createMirror.vue';
import navBar from './components/navBar.vue';
import { mirrorManageApi } from '@/apis';
export default {
  components: {
    prepareMirrorList,
    customMirrorList,
    shareMirrorList,
    createMirrorGroup,
    navBar,
  },
  data() {
    return {
      guideData: {
        title: '欢迎使用镜像管理！',
        subTitle: '平台为您提供了大量的预置镜像，当预置镜像无法满足需求时，您可以选择将自己的镜像迁移到平台，或者在平台的模型训练环节构建自定义镜像',
        btnText: '进入镜像管理',
      },
      hasAuth: false,
      prepareMirrorCount: 0,
      customMirrorCount: 0,
      shareMirrorCount: 0,
      currentTab: 'prepareMirror',
      searchText: '',
      breadValue: [{ name: '项目空间管理', path: '/project-space' }, { name: '资产管理' }, { name: '镜像管理' }],
      showAddModal: false,
      editMirror: false,
      editMirrorInfo: {},
    };
  },
  created() {
    if (this.$route?.query?.tabKey) {
      this.changeTab(this.$route.query.tabKey);
    }
    this.hasAuth = this.$store.state.projectId ? true : false;
    if (this.hasAuth) {
      // 发起数据初始化的请求；
      this.getCount();
    }
  },
  watch: {
    currentTab() {
      this.getCount();
    },
  },
  computed: {
    searchBarPlaceholder() {
      return this.currentTab === 'customMirror' ? '请输入镜像名称/创建人' : this.currentTab === 'shareMirror' ? '请输入镜像名称/ID/分享人用户名' : '请输入镜像名称';
    },
  },
  methods: {
    getCount() {
      const sendData = {
        scene: [],
        architecture: [],
        ideType: [],
        searchField: '',
        page: 1,
        pageSize: 100,
        updateTimeDesc: true,
      };
      mirrorManageApi.getPrepareMirrorList(sendData).then((res) => {
        if (res.code === 0) {
          this.prepareMirrorCount = res.data.total;
        }
      });

      const sendData1 = {
        scene: [],
        versionNumberDesc: null,
        searchField: '',
        page: 1,
        pageSize: 100,
        updateTimeDesc: true,
      };
      mirrorManageApi.getCustomMirrorList(sendData1).then((res) => {
        if (res.code === 0) {
          this.customMirrorCount = res.data?.total || 0;
        }
      });
      mirrorManageApi.getSharedImageList({
        scene: [],
        architecture: [],
        chipType: [],
        searchField: '',
        page: 1,
        pageSize: 10,
        shareTimeDesc: true,
      }).then((res) => {
        if (res.code === 0) {
          this.shareMirrorCount = res.data?.total || 0;
        }
      });
    },
    createNew() {
      this.currentTab = 'customMirror';
      this.$nextTick(() => {
        this.showAddModal = true;
        this.editMirror = false;
      });
    },
    viewNow() {
      this.currentTab = 'prepareMirror';
    },
    toDetail() {
      this.hasAuth = true;
      // 发起数据初始化的请求；
      this.getCount();
    },
    handleNewMirror() {
      this.showAddModal = true;
      this.editMirror = false;
    },
    closeModal(data) {
      this.showAddModal = false;
      this.editMirror = false;
      this.editMirrorInfo = {};
      if (data) {
        this.getTableInfo();
        this.getCount();
      }
    },
    updateMirrorGroup(item) {
      this.showAddModal = true;
      this.editMirror = true;
      this.editMirrorInfo = item;
    },
    handelReload() {
      this.getTableInfo();
    },
    getTableInfo() {
      if (this.currentTab === 'prepareMirror') {
        this.$refs.prepareList.getTableInfo();
      }
      if (this.currentTab === 'customMirror') {
        this.$refs.mirrorList.getTableInfo();
      }
      if (this.currentTab === 'shareMirror') {
        this.$refs.shareMirrorList.getTableInfo();
      }
    },
    // 切换tab栏
    changeTab(key) {
      this.currentTab = key;
      this.searchText = '';
      if (key === 'prepareMirror') {
        this.$nextTick(() => {
          this.$refs.prepareList.getTableInfo();
        });
      }
      if (key === 'customMirror') {
        this.$nextTick(() => {
          this.$refs.mirrorList.getTableInfo();
        });
      }
      if (key === 'shareMirror') {
        this.$nextTick(() => {
          this.$refs.shareMirrorList.getTableInfo();
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.wrapper-box {
  .content-box {
    padding: 8px 20px 20px;
    .content-wrap-box {
      position: relative;
      margin-bottom: 20px;
      padding: 4px 20px 48px 20px;
      background-color: #fff;
      min-height: ~'calc(100% - 20px)';
      border-radius: 4px;
      box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
    }
  }
}
:deep(.ant-tabs-top-bar) {
  border: 0;
  margin-bottom: 16px;
}

:deep(.ant-tabs-extra-content) {
  height: 33px;
  display: flex;
  align-items: center;
  margin-top: 15px;

  .input {
    width: 240px;
    height: 32px;
    border-radius: 2px;
    font-size: 14px;

    .search-icon {
      color: #bec2c5;
      position: relative;
      top: 0px;
    }
  }
}
.tab-bar {
  margin-right: 3px;
  height: 24px;
}
:deep(.ant-tabs-nav .ant-tabs-tab) {
  padding: 16px 12px;
}
</style>
