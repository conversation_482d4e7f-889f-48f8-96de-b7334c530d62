<template>
  <div class="left-card-statistic">
    <div class="top-chart">
      <div class="chart-container">
        <v-chart :option="topChartOptions" autoresize style="height: 525px; width: 100%" />
      </div>
    </div>
    <div class="bottom-chart">
      <div class="chart-container">
        <v-chart :option="bottomChartOptions" autoresize style="height: 1700px; width: 100%" />
      </div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { BarChart } from 'echarts/charts'; // Changed to LineChart
import { GridComponent, LegendComponent, TitleComponent, TooltipComponent, DataZoomComponent } from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';

import VChart from 'vue-echarts';

use([Canvas<PERSON>enderer, BarChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent]);
import { leftTopCard, leftBottomCard } from '../constants/index';

const initchartoptions = (chartData, gridStyle) => {
  let names = [];
  let values = [];
  let rates = [];
  let totals = [];
  let symbolData = [];
  chartData.forEach((item) => {
    names.push(item.name);
    values.push(item.value);
    rates.push(item.rate);
    totals.push(100);
    symbolData.push({
      symbolPosition: 'end',
      value: item.rate,
    });
  });
  return {
    grid: {
      left: gridStyle.left,
      right: '-8%',
      bottom: '2%',
      top: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      show: false,
      axisPointer: {
        type: 'none',
      },
    },
    xAxis: {
      show: false,
      type: 'value',
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          interval: 0,
          color: '#fff',
          align: 'left',
          verticalAlign: 'bottom',
          lineHeight: 26,
          fontSize: 26,
          padding: [0, 0, 20, 10],
        },
        data: names,
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        axisLabel: {
          interval: 0,
          color: 'white',
          //linear-gradient(90deg, #FFFFFF 0%, #A8F6FF 61%, #32CBDD 100%)
          padding: [0, 0, 20, -95],
          align: 'left',
          verticalAlign: 'bottom',
          lineHeight: 32,
          fontSize: 32,
        },
        data: values,
      },
    ],
    series: [
      {
        type: 'pictorialBar',
        symbolSize: [28, 28],
        symbolOffset: [15, 0],
        zlevel: 2,
        itemStyle: {
          normal: {
            color: '#AAF6FF',
            shadowColor: 'rgba(83,237,255,0.72)',
            shadowBlur: 24,
          },
        },
        data: symbolData,
      },
      {
        name: '值',
        type: 'bar',
        zlevel: 1,
        itemStyle: {
          normal: {
            barBorderRadius: 30,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: 'rgba(0,228,255,0.34)',
              },
              {
                offset: 1,
                color: '#00E4FF',
              },
            ]),
          },
        },
        barWidth: 16,
        data: rates,
      },
      {
        name: '背景',
        type: 'bar',
        barWidth: 16,
        barGap: '-100%',
        data: totals,
        itemStyle: {
          normal: {
            color: 'rgba(0,228,255,0.1)',
            barBorderRadius: 22,
          },
        },
      },
    ],
  };
};

const topChartOptions = ref(initchartoptions(leftTopCard, {left: '-30%'}));
const bottomChartOptions = ref(initchartoptions(leftBottomCard, {left: '-20%'}));
</script>

<style lang="less" scoped>
.left-card-statistic {
  width: 22%;
  height: 73vh;
  margin-left: 3.2%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .bar-chart {
    margin-top: 80px;
    margin-right: 30px;
    height: calc(71vh - 100px);
  }
  .top-chart {
    height: 38vh;
    padding-top: 6vh;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url('~@/assets/images/board/left-top-card.png');
  }
  .bottom-chart {
    height: 32.5vh;
    padding-top: 4.5vh;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-image: url('~@/assets/images/board/left-bottom-card.png');
    .chart-container {
      height: 25vh;
    }
  }
  .chart-container {
    overflow-y: auto;
    overflow-x: hidden;
    margin-right: 30px;
  }
}
</style>
